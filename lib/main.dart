import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_dotenv/flutter_dotenv.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_config.dart';
import 'package:quick_retail_mobile/core/constants/app_constants.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/app_theme.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/data/enum/environment.dart';
import 'package:quick_retail_mobile/core/data/services/navigation_service.dart';
import 'package:quick_retail_mobile/core/data/view_models/theme_selection_view_model.dart';
import 'package:quick_retail_mobile/core/utilities/secure_storage/secure_storage_init.dart';
import 'package:quick_retail_mobile/locator.dart';
import 'package:quick_retail_mobile/router.dart';
import 'package:quick_retail_mobile/ui/splash.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  await dotenv.load(fileName: ".env");
  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]);

  AppConfig.setEnvironment(Environment.dev);
  SecureStorageInit.initSecureStorage();
  setupLocator();

  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends ConsumerStatefulWidget {
  const MyApp({super.key});

  @override
  ConsumerState<MyApp> createState() => _MyAppState();
}

class _MyAppState extends ConsumerState<MyApp> {
  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return ScreenUtilInit(
        designSize: const Size(draftWidth, draftHeight),
        builder: (context, child) => Consumer(
        builder: (context, ref, child){
          final themeVm = ref.watch(themeSelectionViewModel);
          final themeMode = themeVm.themeMode;

          // Update the status bar dynamically
          AppTheme.updateStatusBarBrightness(themeMode: themeMode);

          return MaterialApp(
            title: 'Flutter Demo',
            debugShowCheckedModeBanner: false,
            theme: AppTheme.lightTheme,
            darkTheme: AppTheme.darkTheme,
            themeMode: themeMode,
            // home: const ViewSpecificDonation(),
            navigatorKey: locator<NavigationService>().navigationKey,
            onGenerateRoute: generateRoute,
            routes: {
              NamedRoutes.splash: (context) => const Splash(),
            },
          );
        },
      ));
  }
}
