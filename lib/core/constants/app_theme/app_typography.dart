import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class AppTypography {
  // // Caption-level text
  // static TextStyle verySmall = TextStyle(
  //   fontSize: 8.sp,
  //   fontWeight: FontWeight.normal,
  // );
  //
  // // Small text, often used for annotations or meta info
  // static TextStyle small = TextStyle(
  //   fontSize: 10.sp,
  //   fontWeight: FontWeight.normal,
  // );

  // Standard body text sizes
  static TextStyle bodySmall = TextStyle(
    fontSize: 12.sp,
    fontWeight: FontWeight.normal,
  );
  static TextStyle bodyMedium = TextStyle(
    fontSize: 14.sp,
    fontWeight: FontWeight.normal,
  );
  static TextStyle bodyLarge = TextStyle(
    fontSize: 16.sp,
    fontWeight: FontWeight.normal,
  );

  // For subheadings or prominent text
  static TextStyle titleSmall = TextStyle(
    fontSize: 18.sp,
    fontWeight: FontWeight.normal,
  );

  // For titles or section headers
  static TextStyle titleMedium = TextStyle(
    fontSize: 20.sp,
    fontWeight: FontWeight.bold,
  );
  static TextStyle titleLarge = TextStyle(
    fontSize: 24.sp,
    fontWeight: FontWeight.bold,
  );

  // // For main headings or feature emphasis
  // static TextStyle headline = TextStyle(
  //   fontSize: 32.sp,
  //   fontWeight: FontWeight.bold,
  // );
}
