class NamedRoutes {
  //onboarding/authentication
  static const splash = '/';
  static const landing = '/landing';
  static const resetPassword = '/resetPassword';
  static const signup = '/signup';
  static const createPassword = '/createPassword';
  static const createNewPassword = '/createNewPassword';
  static const login = '/login';
  static const otpScreen = '/otpScreen';
  static const pricing = '/pricing';
  static const demo = '/demo';

  //pos
  static const category = '/category';
  static const createCategory = '/createCategory';
  static const createSubCategory = '/createSubCategory';
  static const coreFeature = '/coreFeature';
  static const posDashboard = '/posDashboard';
  static const productManagement = '/productManagement';
  static const productBasicInfo = '/productBasicInfo';
  static const productPricingAndInventory = '/productPricingAndInventory';
  static const productVariation = '/productVariation';
  static const variationInventory = '/variationInventory';
  static const scanProduct = '/scanProduct';
  static const salesProcessing = '/salesProcessing';
  static const addSaledOrderProduct = '/addSaledOrderProduct';
  static const addSaledOrderCustomer = '/addSaledOrderCustomer';
  static const confirmPayment = '/confirmPayment';
  static const searchProduct = '/searchProduct';
  static const searchCustomer = '/searchCustomer';
  static const transactionDashboard = '/transactionDashboard';
  static const viewTransaction = '/viewTransaction';
  static const customerMgt = '/customerMgt';
  static const viewCustomer = '/viewCustomer';
  static const happyTimeDashboard = '/happyTimeDashboard';
  static const giftcardTransactions = '/giftcardTransactions';
  static const discountTransactions = '/discountTransactions';
  static const createDiscount = '/createDiscount';
  static const createGiftcard = '/createGiftcard';
  static const storeMgt = '/storeMgt';
  static const createStore = '/createStore';
  static const salesReturnMgt = '/salesReturnMgt';
  static const createSalesReturn = '/createSalesReturn';
  static const inventoryMgt = '/inventoryMgt';
  static const triggerOrder = '/triggerOrder';
  static const accountSetup = '/accountSetup';
  static const paymentSummary = '/paymentSummary';
  static const inAppWebView = '/inAppWebView';
  static const successfulSubscription = '/successfulSubscription';






  static const bottomNav = '/bottomNav';
}
