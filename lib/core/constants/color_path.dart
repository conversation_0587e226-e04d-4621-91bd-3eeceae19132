import 'package:flutter/material.dart';

class ColorPath {
  //brand colors
  static const vermilion = Color(0xffFF4405);
  static const flamingo = Color(0xffF16722);
  static const fire = Color(0xffB63D00);

  static const inactiveFlamingo = Color.fromARGB(255, 233, 191, 170);

  static const seaShellPink = Color(0xffFFF4ED);
  static const black = Color(0xff000000);
  static const white = Color(0xffffffff);

  //landing gradient colors
  static const codGray1 = Color(0xff141414);
  static const codGray2 = Color(0xff151414);

  static const dolphinGray = Color(0xff6C6975);

  //text colors(light mode)
  static const scampiPurple = Color(0xff58589A);
  static const ebonyBlack = Color(0xff101828);
  static const fiordBlack = Color(0xff475367);
  static const paleSky = Color(0xff667185);
  static const mirageBlack = Color(0xff1D2939);
  static const oxfordBlue = Color(0xff344054);
  static const fiordGrey = Color(0xff475467);
  static const paleGrey = Color(0xff667085);
  static const gullGrey = Color(0xff98A2B3);
  static const minskBlue = Color(0xff2E2E81);
  static const rockBlack = Color(0xff010123);
  static const tropazBlue = Color(0xff2453A0);

  static const mischkaGrey = Color(0xffD0D5DD);
  static const mysticGrey = Color(0xffD9DFE9);
  static const athensGrey = Color(0xffEAECF0);
  static const athensGrey2 = Color(0xffF2F4F7);
  static const athensGrey3 = Color(0xffF9FAFB);
  static const athensGrey4 = Color(0xffFCFCFD);
  static const athensGrey5 = Color(0xffE4E7EC);
  static const athensGrey6 = Color(0xffDFDFEC);
  static const athensGrey7 = Color(0xffEEEEF5);
  static const athensGrey8 = Color(0xffF0F2F4);
  static const porceleanGrey = Color(0xffF6F7F8);
  static const slateGrey = Color(0xff71808E);
  static const periwinkleGrey = Color(0xffD5D9EB);
  static const lilacGrey = Color(0xffF8F9FC);
  static const panacheGreen = Color(0xffE7F6EC);

  static const flamingoRed = Color(0xffF04438);
  static const radicalRed = Color(0xffF63D68);
  static const rose = Color(0xffFD6F8E);
  static const rose30 = Color(0xffF56630);
  static const pearYellow = Color(0xffC9D933);
  static const earlyDawn = Color(0xffFFFAEB);
  static const vesuvius = Color(0xffB54708);

  static const manzYellow = Color(0xffEEEB66);
  static const danubeBlue = Color(0xff78AADE);
  static const hazeBlue = Color(0xffCDCDE1);
  static const whisperPurple = Color(0xffEEEEF5);
  static const lyncBlue = Color(0xff70809A);
  static const meadowGreen = Color(0xff12B76A);
  static const riptideGreen = Color(0xff6CE9A6);
  static const yonderPurple = Color(0xff8181B3);
  static const amaranthRed = Color(0xffE31B54);
  static const soapStoneRed = Color(0xffFFFBFA);
  static const funGreen = Color(0xff027A48);
  static const foamGreen = Color(0xffECFDF3);
  static const shamrockGreen = Color(0xff32D583);
  static const emeraldGreen = Color(0xff5FC381);

  static const salemGreen = Color(0xff0F973D);
  static const scandalGreen = Color(0xffD1FADF);
  static const ribbonBlue = Color(0xff1570EF);
  static const aliceBlue = Color(0xffEFF8FF);
  static const atlantisGreen = Color(0xff86CB3C);
  static const rumGreen = Color(0xffF5FBEE);
  static const fernGreen = Color(0xff4F7A21);
  static const goldYellow = Color(0xffCFC534);
  static const moonYellow = Color(0xffFEFBD8);
  static const dodgerBlue = Color(0xff528BFF);
  static const zirconBlue = Color(0xffEFF4FF);
  static const periwinkleBlue = Color(0xffD1E0FF);
  static const hazeGreen = Color(0xff039855);
  static const mintGreen = Color(0xffA6F4C5);
  static const loganPurple = Color(0xffABABCD);
  static const californiaOrange = Color(0xffF79009);
  static const tealGreen = Color(0xff00857A);
  static const frostedGreen = Color(0xffCDFEF9);
  static const chablisPink = Color(0xffFFF5F6);
  static const pastelPink = Color(0xffFECDD6);
  static const lavenderPink = Color(0xffFFF1F3);
  static const bambooOrange = Color(0xffDC6803);
  static const persianBlue = Color(0xff026AA2);

  static const fetaGreen = Color(0xffF6FEF9);
  static const shirazRed = Color(0xffC01048);
  static const brinkPink = Color(0xffFD6F8E);
  static const alizarinRed = Color(0xffD92D20);
  static const titanPurple = Color(0xffEBE9FE);
  static const heartPurple = Color(0xff5925DC);
  static const onahauGreen = Color(0xffCFF9FE);
  static const ceruleanGreen = Color(0xff06AED4);
  static const thunderbirdRed = Color(0xffB42318);

  static const success200 = Color(0xff5FC381);
  static const grey100 = Color(0xffF0F2F5);
  static const grey500 = Color(0xff667185);
  static const grey800 = Color(0xff1D2739);

  static const primary900 = Color(0xff711E00);

  static Color dynamicColor(String? hexString) {
    // Return default color if hexString is null or empty
    if (hexString == null || hexString.isEmpty) {
      return vermilion;
    }
    try {
      // Remove the '#' character if it exists
      final hexCode = hexString.replaceAll('#', '');

      // Parse the hexadecimal string to an integer
      return Color(int.parse('FF$hexCode', radix: 16));
    } catch (e) {
      debugPrint('Error parsing color: $e');
      return vermilion;
    }
  }
}
