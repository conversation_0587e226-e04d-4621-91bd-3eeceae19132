import 'package:flutter_dotenv/flutter_dotenv.dart';

class ApiRoutes {
  //genaral
  static var allApplications = "${dotenv.env['APPLICATION']}/all";
  static allSubscriptions({String billType = "trial"}) =>
      "${dotenv.env['APPLICATION']}/allSubscription?billing_type=$billType";
  static var allCompanySizes = "${dotenv.env['APPLICATION']}/company-sizes";
  static var allSecurityQuestions = "/api/v1/superadmin/question/all";

  // auth
  static var register = "${dotenv.env['AUTH']}/signup/register";
  static var verifyPayment = "${dotenv.env['AUTH']}/payment/verify-payment";
  static var createPassword = "${dotenv.env['AUTH']}/signup/add-password";
  static var login = "${dotenv.env['AUTH']}/signin/login";
  static var resendLink = "${dotenv.env['AUTH']}/signup/resend-link";
  static var forgotPassword =
      "${dotenv.env['AUTH']}/forgot-password/reset-link";
  static var resetPassword = "${dotenv.env['AUTH']}/forgot-password/reset";

  // pos dashboard
  static var analysisOverview =
      "${dotenv.env['POS']}/dashboard/analysis-overview";
  static var salesOverview = "${dotenv.env['POS']}/dashboard/sales-overview";
  static var customerStats = "${dotenv.env['POS']}/dashboard/customer-stats";

  static var allCustomers = "${dotenv.env['POS']}/customer/all";
  static var allSalesOrder = "${dotenv.env['POS']}/sales/all-sales";

  static var createProduct = "${dotenv.env['POS']}/product/add-product";
  static var allProducts = "${dotenv.env['POS']}/product/all";
  static getSingleProduct(String productId) =>
      "${dotenv.env['POS']}/product/show-product/$productId";
  static deleteProduct(String productId) =>
      "${dotenv.env['POS']}/product/delete-product/$productId";
  static updateProduct(String productId) =>
      "${dotenv.env['POS']}/product/update-product/$productId";
  static editProduct(String productId) =>
      "${dotenv.env['POS']}/product/edit-product/$productId";

  static var allCategories = "${dotenv.env['POS']}/category/all";
  static var allSubCategories = "${dotenv.env['POS']}/subcategory/all";
  static allSubCatByCategoryId(int categoryId) =>
      "${dotenv.env['POS']}/category/$categoryId/subcategories";

  static var allStoreLocations = "${dotenv.env['POS']}/location/all";
}
