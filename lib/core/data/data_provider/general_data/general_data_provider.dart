import 'dart:async';

import 'package:quick_retail_mobile/core/constants/api_routes.dart';
import 'package:quick_retail_mobile/core/data/enum/request_type.dart';
import 'package:quick_retail_mobile/core/data/models/responses/general/all_questions_response.dart';
import 'package:quick_retail_mobile/core/data/models/responses/general/available_applications_response.dart';
import 'package:quick_retail_mobile/core/data/models/responses/general/company_sizes_responses.dart';
import 'package:quick_retail_mobile/core/data/network_manager/network_manager.dart';

import '../../models/responses/general/available_subscriptions_response.dart';

class GeneralDataProvider {
  //fetches available applications
  Future<AvailableApplicationsResponse> fetchAvailableApplications() async {
    var completer = Completer<AvailableApplicationsResponse>();
    try {
      Map<String, dynamic> response =
          await NetworkManager().networkRequestManager(
        RequestType.get, ApiRoutes.allApplications,
        useAuth: false,
        // body: jsonEncode(details)
      );
      var result = AvailableApplicationsResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }

  //fetches available subscriptions
  Future<AvailableSubscriptionsResponse> fetchAvailableSubscriptions(
      {String? billType}) async {
    var completer = Completer<AvailableSubscriptionsResponse>();
    try {
      Map<String, dynamic> response =
          await NetworkManager().networkRequestManager(
        RequestType.get,
        ApiRoutes.allSubscriptions(billType: billType ?? "trial"),
        useAuth: false,
        // body: jsonEncode(details)
      );
      var result = AvailableSubscriptionsResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }

  //fetches company sizes
  Future<CompanySizesResponses> fetchCompanySizes() async {
    var completer = Completer<CompanySizesResponses>();
    try {
      Map<String, dynamic> response =
          await NetworkManager().networkRequestManager(
        RequestType.get, ApiRoutes.allCompanySizes,
        useAuth: false,
        // body: jsonEncode(details)
      );
      var result = CompanySizesResponses.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }

  //fetches all questions
  Future<AllQuestionsResponse> fetchAllQuestions() async {
    var completer = Completer<AllQuestionsResponse>();
    try {
      Map<String, dynamic> response =
          await NetworkManager().networkRequestManager(
        RequestType.get, ApiRoutes.allSecurityQuestions,
        useAuth: false,
        // body: jsonEncode(details)
      );
      var result = AllQuestionsResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }
}
