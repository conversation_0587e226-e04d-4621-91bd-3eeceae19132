import 'dart:async';
import 'dart:convert';

import 'package:quick_retail_mobile/core/constants/api_routes.dart';
import 'package:quick_retail_mobile/core/data/enum/request_type.dart';
import 'package:quick_retail_mobile/core/data/models/responses/auth/create_password_response.dart';
import 'package:quick_retail_mobile/core/data/models/responses/auth/login_response.dart';
import 'package:quick_retail_mobile/core/data/models/responses/auth/register_response.dart';
import 'package:quick_retail_mobile/core/data/models/responses/auth/verify_payment_response.dart';
import 'package:quick_retail_mobile/core/data/models/responses/default_response.dart';
import 'package:quick_retail_mobile/core/data/network_manager/network_manager.dart';

class AuthDataProvider {
  //posts registration data
  Future<RegisterResponse> register(
      {required Map<String, dynamic> details}) async {
    var completer = Completer<RegisterResponse>();
    try {
      Map<String, dynamic> response = await NetworkManager()
          .networkRequestManager(RequestType.post, ApiRoutes.register,
              useAuth: false, body: jsonEncode(details));
      var result = RegisterResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }

  //verifies subscription payment
  Future<VerifyPaymentResponse> verifyPayment(
      {required String reference}) async {
    var completer = Completer<VerifyPaymentResponse>();
    try {
      Map<String, dynamic> response = await NetworkManager()
          .networkRequestManager(RequestType.get, ApiRoutes.verifyPayment,
              useAuth: false, queryParameters: {"reference": reference});
      var result = VerifyPaymentResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }

  //creates Password
  Future<CreatePasswordResponse> createPassword(
      {required Map<String, dynamic> details}) async {
    var completer = Completer<CreatePasswordResponse>();
    try {
      Map<String, dynamic> response = await NetworkManager()
          .networkRequestManager(RequestType.post, ApiRoutes.createPassword,
              useAuth: false);
      var result = CreatePasswordResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }

  //Login
  Future<LoginResponse> login({required Map<String, dynamic> details}) async {
    var completer = Completer<LoginResponse>();
    try {
      Map<String, dynamic> response = await NetworkManager()
          .networkRequestManager(RequestType.post, ApiRoutes.login,
              body: jsonEncode(details), useAuth: false);
      var result = LoginResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }

  //reset password
    Future<DefaultResponse> resetPassword({required Map<String, dynamic> details}) async {
    var completer = Completer<DefaultResponse>();
    try {
      Map<String, dynamic> response = await NetworkManager()
          .networkRequestManager(RequestType.post, ApiRoutes.resetPassword,
              body: jsonEncode(details), useAuth: false);
      var result = DefaultResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }

    //forgot password
    Future<DefaultResponse> forgotPassword({required Map<String, dynamic> details}) async {
    var completer = Completer<DefaultResponse>();
    try {
      Map<String, dynamic> response = await NetworkManager()
          .networkRequestManager(RequestType.post, ApiRoutes.forgotPassword,
              body: jsonEncode(details), useAuth: false);
      var result = DefaultResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }
}
