import 'dart:async';
import 'dart:convert';

import 'package:quick_retail_mobile/core/constants/api_routes.dart';
import 'package:quick_retail_mobile/core/data/enum/request_type.dart';
import 'package:quick_retail_mobile/core/data/models/location_target_response.dart';
import 'package:quick_retail_mobile/core/data/network_manager/network_manager.dart';

class LocationProvider {
  Future<LocationTargetResponse> fetchAllStoreLocations(
      [Map<String, dynamic>? details]) async {
    var completer = Completer<LocationTargetResponse>();
    try {
      Map<String, dynamic> response = await NetworkManager()
          .networkRequestManager(RequestType.post, ApiRoutes.allStoreLocations,
              useAuth: true, body: jsonEncode(details));
      var result = LocationTargetResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }
}
