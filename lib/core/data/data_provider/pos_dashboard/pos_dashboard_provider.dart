import 'dart:async';
import 'dart:convert';

import 'package:quick_retail_mobile/core/constants/api_routes.dart';
import 'package:quick_retail_mobile/core/data/enum/request_type.dart';
import 'package:quick_retail_mobile/core/data/network_manager/network_manager.dart';

import '../../models/responses/pos_dashboard/pos_dashboard.dart';

class PosDashboardProvider {
  Future<AnalysisOverviewResponse> fetchAnalysisOverview(
      [Map<String, dynamic>? details]) async {
    var completer = Completer<AnalysisOverviewResponse>();
    try {
      Map<String, dynamic> response = await NetworkManager()
          .networkRequestManager(RequestType.post, ApiRoutes.analysisOverview,
              useAuth: true, body: jsonEncode(details));
      var result = AnalysisOverviewResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }

  Future<CustomerStatsResponse> fetchCustomerStats(
      [Map<String, dynamic>? details]) async {
    var completer = Completer<CustomerStatsResponse>();
    try {
      Map<String, dynamic> response = await NetworkManager()
          .networkRequestManager(RequestType.post, ApiRoutes.customerStats,
              useAuth: true, body: jsonEncode(details));
      var result = CustomerStatsResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }

  Future<SalesOverviewResponse> fetchSalesOverview(
      [Map<String, dynamic>? details]) async {
    var completer = Completer<SalesOverviewResponse>();
    try {
      Map<String, dynamic> response = await NetworkManager()
          .networkRequestManager(RequestType.post, ApiRoutes.salesOverview,
              useAuth: true, body: jsonEncode(details));
      var result = SalesOverviewResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }
}
