import 'dart:async';
import 'dart:convert';

import 'package:quick_retail_mobile/core/constants/api_routes.dart';
import 'package:quick_retail_mobile/core/data/enum/request_type.dart';
import 'package:quick_retail_mobile/core/data/models/customer_response.dart';
import 'package:quick_retail_mobile/core/data/network_manager/network_manager.dart';

class CustomerProvider {
  Future<CustomerResponse> fetchAllCustomers(
      [Map<String, dynamic>? details]) async {
    var completer = Completer<CustomerResponse>();
    try {
      Map<String, dynamic> response = await NetworkManager()
          .networkRequestManager(RequestType.post, ApiRoutes.allCustomers,
              useAuth: true, body: jsonEncode(details));
      var result = CustomerResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }
}
