import 'dart:async';
import 'dart:convert';

import 'package:quick_retail_mobile/core/constants/api_routes.dart';
import 'package:quick_retail_mobile/core/data/enum/request_type.dart';
import 'package:quick_retail_mobile/core/data/models/sales_order_response.dart';
import 'package:quick_retail_mobile/core/data/network_manager/network_manager.dart';

class SalesProvider {
  Future<SalesOrderResponse> fetchAllSalesOrder(
      [Map<String, dynamic>? details]) async {
    var completer = Completer<SalesOrderResponse>();
    try {
      Map<String, dynamic> response = await NetworkManager()
          .networkRequestManager(RequestType.post, ApiRoutes.allSalesOrder,
              useAuth: true, body: jsonEncode(details));
      var result = SalesOrderResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }
}
