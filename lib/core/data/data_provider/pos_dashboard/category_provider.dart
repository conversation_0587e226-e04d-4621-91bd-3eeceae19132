import 'dart:async';
import 'dart:convert';

import 'package:quick_retail_mobile/core/constants/api_routes.dart';
import 'package:quick_retail_mobile/core/data/enum/request_type.dart';
import 'package:quick_retail_mobile/core/data/models/category_response.dart';
import 'package:quick_retail_mobile/core/data/network_manager/network_manager.dart';

class CategoryProvider {
  Future<CategoryResponse> fetchAllCategories(
      [Map<String, dynamic>? details]) async {
    var completer = Completer<CategoryResponse>();
    try {
      Map<String, dynamic> response = await NetworkManager()
          .networkRequestManager(RequestType.post, ApiRoutes.allCategories,
              useAuth: true, body: jsonEncode(details));
      var result = CategoryResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }

  Future<CategoryResponse> fetchAllSubCategories(
      [Map<String, dynamic>? details]) async {
    var completer = Completer<CategoryResponse>();
    try {
      Map<String, dynamic> response = await NetworkManager()
          .networkRequestManager(RequestType.post, ApiRoutes.allSubCategories,
              useAuth: true, body: jsonEncode(details));
      var result = CategoryResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }

  Future<CategoryResponse> fetchAllSubCatByCatgoryId(int categoryId) async {
    var completer = Completer<CategoryResponse>();
    try {
      Map<String, dynamic> response = await NetworkManager()
          .networkRequestManager(
              RequestType.get, ApiRoutes.allSubCatByCategoryId(categoryId),
              useAuth: true);
      var result = CategoryResponse.fromJson(response);
      completer.complete(result);
    } catch (e) {
      completer.completeError(e);
    }
    return completer.future;
  }
}
