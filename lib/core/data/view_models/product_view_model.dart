import 'dart:convert';
import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quick_retail_mobile/core/constants/app_constants.dart';
import 'package:quick_retail_mobile/core/data/data_provider/pos_dashboard/product_provider.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/models/product_response.dart';
import 'package:quick_retail_mobile/core/data/models/product_variation.dart';
import 'package:quick_retail_mobile/core/data/states/base_state.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/locator.dart';

class ProductViewModel extends BaseState {
  final _productProvider = locator<ProductProvider>();

  //message
  String _message = '';
  String get message => _message;

  createProduct([Map<String, dynamic>? details]) async {
    log("createProduct details $details");
    setState(ViewState.busy);
    details?.removeWhere((k, v) => v == null || v == '');
    await _productProvider.createProduct(details).then((response) {
      _message = response.message ?? defaultSuccessMessage;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }

  ProductResponse? _productResponse;
  ProductResponse? get productResponse => _productResponse;
  List<ProductData> get products =>
      _productResponse?.data?.products?.data ?? [];

  // message
  String _fetchAllProductsMessage = '';
  String get fetchAllProductsMessage => _fetchAllProductsMessage;
  fetchAllProducts({
    ProductParams? productParams,
    bool showBusyState = true,
  }) async {
    productParams ??= ProductParams();
    final body = productParams.toJson();
    body.removeWhere((k, v) => v == null || v == '');
    if (showBusyState) setState(ViewState.busy);
    await _productProvider.fetchAllProducts(body).then((response) {
      _fetchAllProductsMessage = response.message ?? defaultSuccessMessage;
      _productResponse = response;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _fetchAllProductsMessage =
          Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }

  ProductData? _singleProduct;
  ProductData? get singleProduct => _singleProduct;

  //message
  String _fetchSingleProductMessage = '';
  String get fetchSingleProductMessage => _fetchSingleProductMessage;
  Future<ProductData?> fetchSingleProduct(String variationId) async {
    setState(ViewState.busy);
    try {
      final response = await _productProvider.fetchSingleProduct(variationId);
      _fetchSingleProductMessage = defaultSuccessMessage;
      _singleProduct = response;
      setState(ViewState.retrieved);
      return response;
    } catch (error) {
      _fetchSingleProductMessage =
          Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
      return null;
    }
  }

  //message
  String _deleteMessage = '';
  String get deleteMessage => _deleteMessage;
  deleteProduct(String productId) async {
    setState(ViewState.busy);
    await _productProvider.deleteProduct(productId).then((response) {
      _deleteMessage = response.message ?? defaultSuccessMessage;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _deleteMessage =
          Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }

  //message
  String _updateMessage = '';
  String get updateMessage => _updateMessage;
  updateProduct(String productId, [Map<String, dynamic>? details]) async {
    setState(ViewState.busy);
    details?.removeWhere((k, v) => v == null || v == '');
    await _productProvider.updateProduct(productId, details).then((response) {
      _updateMessage = response.message ?? defaultSuccessMessage;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _updateMessage =
          Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }

  //message
  String _getProductDetailsMessage = '';
  String get getProductDetailsMessage => _getProductDetailsMessage;
  Future<List<ProductVariation>> getProductDetailsVariation(
      String productId) async {
    setState(ViewState.busy);

    try {
      final response =
          await _productProvider.getProductDetailsVariation(productId);
      _getProductDetailsMessage = defaultSuccessMessage;
      setState(ViewState.retrieved);
      List<ProductVariation> variationRes = productVariationFromJson(
        json.encode(
          response.data["product_variations"],
        ),
      );
      return variationRes;
    } catch (error) {
      _getProductDetailsMessage =
          Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
      return [];
    }
  }
}

final productViewModel = ChangeNotifierProvider<ProductViewModel>((ref) {
  return ProductViewModel();
});
