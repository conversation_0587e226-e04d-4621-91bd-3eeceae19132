import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quick_retail_mobile/core/constants/app_constants.dart';
import 'package:quick_retail_mobile/core/data/data_provider/pos_dashboard/customer_provider.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/models/customer_response.dart';
import 'package:quick_retail_mobile/core/data/models/query_args.dart';
import 'package:quick_retail_mobile/core/data/states/base_state.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/locator.dart';

class CustomerViewModel extends BaseState {
  final CustomerProvider _customerProvider = locator<CustomerProvider>();

  //message
  String _message = '';
  String get message => _message;

  CustomerResponse? _customerResponse;
  List<CustomerData> get customerResponse =>
      _customerResponse?.data?.customers?.data ?? [];
  List<CustomerData> get recentCustomers => customerResponse.take(3).toList();

  fetchAllCustomers({
    QueryArgs? queryArgs,
    bool showBusyState = true,
  }) async {
    queryArgs ??= QueryArgs();
    final body = queryArgs.toMap();
    body.removeWhere((k, v) => v == null || v == '');
    if (showBusyState) setState(ViewState.busy);
    await _customerProvider.fetchAllCustomers(body).then((response) {
      _message = response.message ?? defaultSuccessMessage;
      _customerResponse = response;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }
}

final customerViewModel = ChangeNotifierProvider<CustomerViewModel>((ref) {
  return CustomerViewModel();
});
