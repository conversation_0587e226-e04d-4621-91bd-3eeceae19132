import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quick_retail_mobile/core/constants/app_constants.dart';
import 'package:quick_retail_mobile/core/data/data_provider/pos_dashboard/category_provider.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/models/category_response.dart';
import 'package:quick_retail_mobile/core/data/states/base_state.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/locator.dart';

class CategoryViewModel extends BaseState {
  final _categoryProvider = locator<CategoryProvider>();

  //message
  String _message = '';
  String get message => _message;

  //categories
  CategoryResponse? _categoryResponse;
  List<Category> get categories => _categoryResponse?.data ?? [];

  fetchAllCategories({
    bool showBusyState = true,
  }) async {
    if (showBusyState) setState(ViewState.busy);
    await _categoryProvider.fetchAllCategories().then((response) {
      _message = response.message ?? defaultSuccessMessage;
      _categoryResponse = response;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }

  //sub categories
  CategoryResponse? _subCategoryResponse;
  List<Category> get subCategories => _subCategoryResponse?.data ?? [];

  fetchAllSubCategories({
    bool showBusyState = true,
  }) async {
    if (showBusyState) setState(ViewState.busy);
    await _categoryProvider.fetchAllSubCategories().then((response) {
      _message = response.message ?? defaultSuccessMessage;
      _subCategoryResponse = response;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }

  // message
  String _subCategoryMessage = '';
  String get subCategoryMessage => _subCategoryMessage;
  //sub categories by category id
  CategoryResponse? _subCatByCatIdResponse;
  List<Category> get subCatByCategoryId => _subCatByCatIdResponse?.data ?? [];

  fetchAllSubCatByCatgoryId({
    bool showBusyState = true,
    required int categoryId,
  }) async {
    if (showBusyState) setSecondState(ViewState.busy);
    await _categoryProvider.fetchAllSubCatByCatgoryId(categoryId).then(
        (response) {
      _subCategoryMessage = response.message ?? defaultSuccessMessage;
      _subCatByCatIdResponse = response;
      setSecondState(ViewState.retrieved);
    }, onError: (error) {
      _subCategoryMessage =
          Utilities.formatMessage(error.toString(), isSuccess: false);
      setSecondState(ViewState.error);
    });
  }
}

final categoryViewModel = ChangeNotifierProvider<CategoryViewModel>((ref) {
  return CategoryViewModel();
});
