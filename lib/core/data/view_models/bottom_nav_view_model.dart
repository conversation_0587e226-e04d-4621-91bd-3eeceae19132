import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quick_retail_mobile/ui/pages/pos/pos_dashboard.dart';

class BottomNavViewModel extends ChangeNotifier {
  //current index of the bottom nav-bar
  int _currentIndex = 1; //todo:::: reset to 0
  int get currentIndex => _currentIndex;
  setCurrentIndex(int value, {bool refreshUi = true}) {
    _currentIndex = value;
    if (refreshUi) {
      notifyListeners();
    }
  }

  //children of the bottom Nav
  final List<Widget> _children = [
    Container(),
    PosDashboard(),
    Container(),
    Container(),
    Container(),
  ];

  List<Widget> get children => _children;

  //updates the current index of the bottom nav
  updateIndex(int index) {
    _currentIndex = index;
    notifyListeners();
  }
}

final bottomNavViewModel =
    ChangeNotifierProvider.autoDispose<BottomNavViewModel>((ref) {
  return BottomNavViewModel();
});
