import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quick_retail_mobile/core/constants/app_constants.dart';
import 'package:quick_retail_mobile/core/data/data_provider/auth_data_provider/auth_data_provider.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/models/user.dart';
import 'package:quick_retail_mobile/core/data/states/base_state.dart';
import 'package:quick_retail_mobile/core/utilities/secure_storage/secure_storage_utils.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/locator.dart';

class AuthViewModel extends BaseState {
  final AuthDataProvider _authDataProvider = locator<AuthDataProvider>();

  //message
  String _message = '';
  String get message => _message;

  User? _user;
  User? get user => _user;
  set user(User? user) {
    _user = user;
    notifyListeners();
  }

  login({required String email, required String password}) async {
    setState(ViewState.busy);
    final details = {
      "email": email, //server
      "password": password
    };
    await _authDataProvider.login(details: details).then((response) async {
      _message = response.message ?? defaultSuccessMessage;
      await SecureStorageUtils.saveUserDetailsToStorage(
          response: response, password: '');
      _user = response.data?.user;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }

  forgotPassword(
      {String otpMedium = 'email address',
      String? email,
      String? phoneno}) async {
    setState(ViewState.busy);
    Map<String, dynamic> details = {};
    if (otpMedium == "email address") {
      details['email'] = email ?? '';
    }
    if (otpMedium == "phone number") {
      details['phoneno'] = phoneno ?? '';
    }

    print(email);
    print(phoneno);

    await _authDataProvider.forgotPassword(details: details).then((response) {
      _message = response.message ?? defaultSuccessMessage;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }
}

final authViewModel = ChangeNotifierProvider<AuthViewModel>((ref) {
  return AuthViewModel();
});
