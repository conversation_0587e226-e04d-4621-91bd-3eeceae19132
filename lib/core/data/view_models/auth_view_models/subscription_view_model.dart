import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quick_retail_mobile/core/constants/app_constants.dart';
import 'package:quick_retail_mobile/core/data/data_provider/auth_data_provider/auth_data_provider.dart';
import 'package:quick_retail_mobile/core/data/data_provider/general_data/general_data_provider.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/models/responses/auth/register_response.dart';
import 'package:quick_retail_mobile/core/data/models/responses/auth/verify_payment_response.dart';
import 'package:quick_retail_mobile/core/data/models/responses/general/all_questions_response.dart';
import 'package:quick_retail_mobile/core/data/models/responses/general/company_sizes_responses.dart';
import 'package:quick_retail_mobile/core/data/models/subscription_data.dart';
import 'package:quick_retail_mobile/core/data/states/base_state.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/locator.dart';

class SubscriptionViewModel extends BaseState {
  //authentication data provider
  final GeneralDataProvider _dataProvider = locator<GeneralDataProvider>();
  final AuthDataProvider _authDataProvider = locator<AuthDataProvider>();

  //message
  String _message = '';
  String get message => _message;

  List<SubscriptionData> _trialSubscriptions = [];
  List<SubscriptionData> get trialSubscription => _trialSubscriptions;

  List<SubscriptionData> _monthlySubscriptions = [];
  List<SubscriptionData> get monthlySubscriptions => _monthlySubscriptions;

  List<SubscriptionData> _annualSubscriptions = [];
  List<SubscriptionData> get annualSubscriptions => _annualSubscriptions;

  List<SubscriptionData> selectedSubscriptions = [];

  RegisterData? _subscriptionCheckoutData;
  RegisterData? get subscriptionCheckoutData => _subscriptionCheckoutData;

  // company sizes
  List<ListItem> _companySizes = [];
  List<ListItem> get companySizes => _companySizes;

  ListItem? _selectedCompanySize;
  ListItem? get selectedCompanySize => _selectedCompanySize;
  set selectedCompanySize(ListItem? value) {
    _selectedCompanySize = value;
    notifyListeners();
  }

  String selectedBillingType = "trial";

  VerifyPaymentData? _verifyPaymentData;
  VerifyPaymentData? get verifyPaymentData => _verifyPaymentData;
  set verifyPaymentData(VerifyPaymentData? value) {
    _verifyPaymentData = value;
    notifyListeners();
  }

  fetchAvailableSubscriptions({String? billType}) async {
    setState(ViewState.busy);
    await _dataProvider.fetchAvailableSubscriptions(billType: billType).then(
        (response) {
      _message = response.message ?? defaultSuccessMessage;
      if (billType == "trial") {
        _trialSubscriptions = response.data ?? [];
      } else if (billType == "monthly") {
        _monthlySubscriptions = response.data ?? [];
      } else if (billType == "yearly") {
        _annualSubscriptions = response.data ?? [];
      }
      if (billType == null) {
        _trialSubscriptions = response.data ?? [];
        _monthlySubscriptions = response.data ?? [];
        _annualSubscriptions = response.data ?? [];
      }
      setState(ViewState.retrieved);
    }, onError: (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }

  registerAccount({
    required String firstname,
    required String lastname,
    required String email,
    required String phoneno,
    required String companyName,
  }) async {
    setState(ViewState.busy);
    final details = {
      "firstname": firstname,
      "lastname": lastname,
      "email": email,
      "phoneno": phoneno,
      "company_name": companyName,
      "company_size_id": _selectedCompanySize?.id, //3,4
      "billing_type": selectedBillingType, //trial,monthly,yearly,
      "payment_method": "paystack",
      "password_url":
          "https://api-quick-retail.sbscuk.co.uk/public", //https://api-quick-retail.sbscuk.co.uk/public
      "paystack_complete_callback":
          "https://api-quick-retail.sbscuk.co.uk/public",
      "applications": parseSelectedSubscriptions()
    };
    log(details.toString());
    await _authDataProvider.register(details: details).then((response) {
      _message = response.message ?? defaultSuccessMessage;
      _subscriptionCheckoutData = response.data;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }

  fetchCompanySizes() async {
    setState(ViewState.busy);
    await _dataProvider.fetchCompanySizes().then((response) {
      _message = response.message ?? defaultSuccessMessage;
      _companySizes = response.data ?? [];
      _selectedCompanySize = null;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }



  verifyPayment() async {
    setState(ViewState.busy);
    await _authDataProvider
        .verifyPayment(reference: subscriptionCheckoutData?.reference ?? '')
        .then((response) {
      _message = response.message ?? defaultSuccessMessage;
      _verifyPaymentData = response.data;
      _selectedCompanySize = null;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }

  List parseSelectedSubscriptions() {
    List applications = [];
    for (var sub in selectedSubscriptions) {
      applications.add({
        "subscription_id": sub.id,
        "application_id": sub.applicationId,
        "amount": sub.amount,
        "additional_seat": sub.totalAdditionalSeats
      });
    }
    return applications;
  }

  QuestionData? _selectedSecurityQuestion;
  QuestionData? get selectedSecurityQuestion => _selectedSecurityQuestion;
  set selectedSecurityQuestion(QuestionData? value) {
    _selectedSecurityQuestion = value;
    notifyListeners();
  }

  List<QuestionData> _securityQuestions = [];
  List<QuestionData> get securityQuestions => _securityQuestions;

  List<String> reducedSecurityQuestions() {
    return _securityQuestions.map((e) => e.question ?? '').toList();
  }

  fetchSecurityQuestions() async {
    setState(ViewState.busy);
    await _dataProvider.fetchAllQuestions().then((response) {
      _message = response.message ?? defaultSuccessMessage;
      _securityQuestions = response.data ?? [];
      _selectedSecurityQuestion = null;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }

//todo::: confirm missing data
  createPassword({
    required String password,
    required String confirmPassword,
    required String securityAnswer
  })async{
    setState(ViewState.busy);
    final details = {
    "password":password,
    "password_confirmation":confirmPassword,
    "security_question":_selectedSecurityQuestion?.question ?? '',
    "security_answer":securityAnswer,
    "token":"ojLnftCc9iWsua1deFY1fFJPUdTXxPuN6ydANio9jEB9TxV3dihSnYNecKxx",
    "email":"<EMAIL>"
};
    await _authDataProvider.createPassword(details: details).then((response) {
      _message = response.message ?? defaultSuccessMessage;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }
}

final subscriptionViewModel =
    ChangeNotifierProvider<SubscriptionViewModel>((ref) {
  return SubscriptionViewModel();
});
