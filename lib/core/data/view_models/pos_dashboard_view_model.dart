import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quick_retail_mobile/core/constants/app_constants.dart';
import 'package:quick_retail_mobile/core/data/data_provider/pos_dashboard/pos_dashboard_provider.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/models/responses/pos_dashboard/pos_dashboard.dart';
import 'package:quick_retail_mobile/core/data/states/base_state.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/locator.dart';

class PosDashboardViewModel extends BaseState {
  //pos dashboard data provider
  final PosDashboardProvider _posDashboardProvider =
      locator<PosDashboardProvider>();

  //message
  String _message = '';
  String get message => _message;

  AnalysisOverviewResponse? _analysisOverviewResponse;
  AnalysisOverview? get analysisOverview => _analysisOverviewResponse?.data;

  fetchAnalysisOverview({
    String? startDate,
    String? endDate,
    bool showBusyState = true,
  }) async {
    if (showBusyState) setState(ViewState.busy);
    await _posDashboardProvider.fetchAnalysisOverview().then((response) {
      _message = response.message ?? defaultSuccessMessage;
      _analysisOverviewResponse = response;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }

  CustomerStatsResponse? _customerStatsResponse;
  CustomerStats? get customerStatsData => _customerStatsResponse?.data;

  fetchCustomerStats({
    bool showBusyState = true,
  }) async {
    if (showBusyState) setState(ViewState.busy);
    await _posDashboardProvider.fetchCustomerStats().then((response) {
      _message = response.message ?? defaultSuccessMessage;
      _customerStatsResponse = response;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }

  SalesOverviewResponse? _salesOverviewResponse;
  SalesOverview? get salesOverviewResponse => _salesOverviewResponse?.data;

  fetchSalesOverview({
    bool showBusyState = true,
  }) async {
    if (showBusyState) setState(ViewState.busy);
    await _posDashboardProvider.fetchSalesOverview().then((response) {
      _message = response.message ?? defaultSuccessMessage;
      _salesOverviewResponse = response;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }
}

final posDashboardViewModel =
    ChangeNotifierProvider<PosDashboardViewModel>((ref) {
  return PosDashboardViewModel();
});
