import 'dart:developer';

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quick_retail_mobile/core/constants/app_constants.dart';
import 'package:quick_retail_mobile/core/data/data_provider/pos_dashboard/sales_provider.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/models/query_args.dart';
import 'package:quick_retail_mobile/core/data/models/sales_order_response.dart';
import 'package:quick_retail_mobile/core/data/states/base_state.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/locator.dart';

class SalesViewModel extends BaseState {
  final SalesProvider _salesProvider = locator<SalesProvider>();

  //message
  String _message = '';
  String get message => _message;

  SalesOrderResponse? _salesOrderResponse;
  List<SalesData> get salesOrderResponse =>
      _salesOrderResponse?.data?.sales?.data ?? [];
  List<SalesData> get recentSalesOrder => salesOrderResponse.take(3).toList();

  fetchAllSalesOrder({
    QueryArgs? queryArgs,
    bool showBusyState = true,
  }) async {
    queryArgs ??= QueryArgs();
    final body = queryArgs.toMap();
    body.removeWhere((k, v) => v == null || v == '');
    log("fetchAllSalesOrder body $body");
    if (showBusyState) setState(ViewState.busy);
    await _salesProvider.fetchAllSalesOrder(body).then((response) {
      _message = response.message ?? defaultSuccessMessage;
      _salesOrderResponse = response;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }
}

final salesViewModel = ChangeNotifierProvider<SalesViewModel>((ref) {
  return SalesViewModel();
});
