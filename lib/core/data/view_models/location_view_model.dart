import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:quick_retail_mobile/core/constants/app_constants.dart';
import 'package:quick_retail_mobile/core/data/data_provider/pos_dashboard/location_provider.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/models/location_target_response.dart';
import 'package:quick_retail_mobile/core/data/models/query_args.dart';
import 'package:quick_retail_mobile/core/data/states/base_state.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/locator.dart';

class LocationViewModel extends BaseState {
  final _locationProvider = locator<LocationProvider>();

  //message
  String _message = '';
  String get message => _message;

  //categories
  LocationTargetResponse? _locationResponse;
  List<Store> get locations => _locationResponse?.data?.stores ?? [];

  fetchAllStoreLocations({
    QueryArgs? queryArgs,
    bool showBusyState = true,
  }) async {
    queryArgs ??= QueryArgs();
    final body = queryArgs.toMap();
    body.removeWhere((k, v) => v == null || v == '');
    if (showBusyState) setState(ViewState.busy);
    await _locationProvider.fetchAllStoreLocations({
      "search": "",
      "sort_by": "", //asc,desc
      "per_page": "", //1,2,3,4
      "limit": "", // 5,10, 20
      "paginate": "" //false,true
    }).then((response) {
      _message = response.message ?? defaultSuccessMessage;
      _locationResponse = response;
      setState(ViewState.retrieved);
    }, onError: (error) {
      _message = Utilities.formatMessage(error.toString(), isSuccess: false);
      setState(ViewState.error);
    });
  }
}

final locationViewModel = ChangeNotifierProvider<LocationViewModel>((ref) {
  return LocationViewModel();
});
