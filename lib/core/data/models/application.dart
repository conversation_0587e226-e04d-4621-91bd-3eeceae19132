import 'package:meta/meta.dart';

class Application {
  final int? id;
  final String? name;
  final String? slug;
  final String? description;
  final int? freeAccessUsers;
  final String? additionalSeatAmount;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final DateTime? deletedAt;

  Application({
    this.id,
    this.name,
    this.slug,
    this.description,
    this.freeAccessUsers,
    this.additionalSeatAmount,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
  });

  factory Application.fromJson(Map<String, dynamic> json) {
    return Application(
      id: json['id'],
      name: json['name'],
      slug: json['slug'],
      description: json['description'],
      freeAccessUsers: json['free_access_users'],
      additionalSeatAmount: json['additional_seat_amount'],
      createdAt: json['created_at'] != null ? DateTime.tryParse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.tryParse(json['updated_at']) : null,
      deletedAt: json['deleted_at'] != null ? DateTime.tryParse(json['deleted_at']) : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'slug': slug,
      'description': description,
      'free_access_users': freeAccessUsers,
      'additional_seat_amount': additionalSeatAmount,
      'created_at': createdAt?.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
      'deleted_at': deletedAt?.toIso8601String(),
    };
  }
}