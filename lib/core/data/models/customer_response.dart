import 'dart:convert';

CustomerResponse customerResponseFromJson(String str) =>
    CustomerResponse.fromJson(json.decode(str));

String customerResponseToJson(CustomerResponse data) =>
    json.encode(data.toJson());

class CustomerResponse {
  final bool? error;
  final String? message;
  final Data? data;

  CustomerResponse({
    this.error,
    this.message,
    this.data,
  });

  factory CustomerResponse.fromJson(Map<String, dynamic> json) =>
      CustomerResponse(
        error: json["error"],
        message: json["message"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "error": error,
        "message": message,
        "data": data?.toJson(),
      };
}

class Data {
  final Customers? customers;
  final int? total;

  Data({
    this.customers,
    this.total,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        customers: json["customers"] == null
            ? null
            : Customers.fromJson(json["customers"]),
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
        "customers": customers?.toJson(),
        "total": total,
      };
}

class Customers {
  final int? currentPage;
  final List<CustomerData>? data;
  final String? firstPageUrl;
  final int? from;
  final int? lastPage;
  final String? lastPageUrl;
  final List<Link>? links;
  final dynamic nextPageUrl;
  final String? path;
  final int? perPage;
  final dynamic prevPageUrl;
  final int? to;
  final int? total;

  Customers({
    this.currentPage,
    this.data,
    this.firstPageUrl,
    this.from,
    this.lastPage,
    this.lastPageUrl,
    this.links,
    this.nextPageUrl,
    this.path,
    this.perPage,
    this.prevPageUrl,
    this.to,
    this.total,
  });

  factory Customers.fromJson(Map<String, dynamic> json) => Customers(
        currentPage: json["current_page"],
        data: json["data"] == null
            ? []
            : List<CustomerData>.from(
                json["data"]!.map((x) => CustomerData.fromJson(x))),
        firstPageUrl: json["first_page_url"],
        from: json["from"],
        lastPage: json["last_page"],
        lastPageUrl: json["last_page_url"],
        links: json["links"] == null
            ? []
            : List<Link>.from(json["links"]!.map((x) => Link.fromJson(x))),
        nextPageUrl: json["next_page_url"],
        path: json["path"],
        perPage: json["per_page"],
        prevPageUrl: json["prev_page_url"],
        to: json["to"],
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
        "current_page": currentPage,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "first_page_url": firstPageUrl,
        "from": from,
        "last_page": lastPage,
        "last_page_url": lastPageUrl,
        "links": links == null
            ? []
            : List<dynamic>.from(links!.map((x) => x.toJson())),
        "next_page_url": nextPageUrl,
        "path": path,
        "per_page": perPage,
        "prev_page_url": prevPageUrl,
        "to": to,
        "total": total,
      };
}

class CustomerData {
  final String? customerId;
  final String? customerName;
  final String? customerEmail;
  final String? customerPhone;
  final String? customerAddress;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int? salesOrdersCount;
  final String? salesOrdersSumOrderTotal;
  final String? status;
  final DateTime? lastVisit;
  final List<SalesOrder>? salesOrders;

  CustomerData({
    this.customerId,
    this.customerName,
    this.customerEmail,
    this.customerPhone,
    this.customerAddress,
    this.createdAt,
    this.updatedAt,
    this.salesOrdersCount,
    this.salesOrdersSumOrderTotal,
    this.status,
    this.lastVisit,
    this.salesOrders,
  });

  factory CustomerData.fromJson(Map<String, dynamic> json) => CustomerData(
        customerId: json["customerID"],
        customerName: json["customer_name"],
        customerEmail: json["customer_email"],
        customerPhone: json["customer_phone"],
        customerAddress: json["customer_address"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        salesOrdersCount: json["sales_orders_count"],
        salesOrdersSumOrderTotal: json["sales_orders_sum_order_total"],
        status: json["status"],
        lastVisit: json["last_visit"] == null
            ? null
            : DateTime.parse(json["last_visit"]),
        salesOrders: json["sales_orders"] == null
            ? []
            : List<SalesOrder>.from(
                json["sales_orders"]!.map((x) => SalesOrder.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "customerID": customerId,
        "customer_name": customerName,
        "customer_email": customerEmail,
        "customer_phone": customerPhone,
        "customer_address": customerAddress,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "sales_orders_count": salesOrdersCount,
        "sales_orders_sum_order_total": salesOrdersSumOrderTotal,
        "status": status,
        "last_visit": lastVisit?.toIso8601String(),
        "sales_orders": salesOrders == null
            ? []
            : List<dynamic>.from(salesOrders!.map((x) => x.toJson())),
      };
}

class SalesOrder {
  final int? id;
  final String? orderId;
  final String? customerName;
  final String? orderTotal;
  final String? amountPaid;
  final String? fees;
  final String? orderNumber;
  final String? receiptNo;
  final DateTime? dateCompleted;
  final String? status;
  final String? paymentStatus;
  final String? paymentMethod;
  final dynamic cancellationReason;
  final dynamic discountId;
  final int? customerId;
  final int? locationId;
  final dynamic shippingAddressId;
  final dynamic billingAddressId;
  final int? staffId;
  final String? balance;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  SalesOrder({
    this.id,
    this.orderId,
    this.customerName,
    this.orderTotal,
    this.amountPaid,
    this.fees,
    this.orderNumber,
    this.receiptNo,
    this.dateCompleted,
    this.status,
    this.paymentStatus,
    this.paymentMethod,
    this.cancellationReason,
    this.discountId,
    this.customerId,
    this.locationId,
    this.shippingAddressId,
    this.billingAddressId,
    this.staffId,
    this.balance,
    this.createdAt,
    this.updatedAt,
  });

  factory SalesOrder.fromJson(Map<String, dynamic> json) => SalesOrder(
        id: json["id"],
        orderId: json["orderID"],
        customerName: json["customer_name"],
        orderTotal: json["order_total"],
        amountPaid: json["amount_paid"],
        fees: json["fees"],
        orderNumber: json["order_number"],
        receiptNo: json["receipt_no"],
        dateCompleted: json["date_completed"] == null
            ? null
            : DateTime.parse(json["date_completed"]),
        status: json["status"],
        paymentStatus: json["payment_status"],
        paymentMethod: json["payment_method"],
        cancellationReason: json["cancellation_reason"],
        discountId: json["discount_id"],
        customerId: json["customer_id"],
        locationId: json["location_id"],
        shippingAddressId: json["shipping_address_id"],
        billingAddressId: json["billing_address_id"],
        staffId: json["staff_id"],
        balance: json["balance"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "orderID": orderId,
        "customer_name": customerName,
        "order_total": orderTotal,
        "amount_paid": amountPaid,
        "fees": fees,
        "order_number": orderNumber,
        "receipt_no": receiptNo,
        "date_completed": dateCompleted?.toIso8601String(),
        "status": status,
        "payment_status": paymentStatus,
        "payment_method": paymentMethod,
        "cancellation_reason": cancellationReason,
        "discount_id": discountId,
        "customer_id": customerId,
        "location_id": locationId,
        "shipping_address_id": shippingAddressId,
        "billing_address_id": billingAddressId,
        "staff_id": staffId,
        "balance": balance,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}

class Link {
  final String? url;
  final String? label;
  final bool? active;

  Link({
    this.url,
    this.label,
    this.active,
  });

  factory Link.fromJson(Map<String, dynamic> json) => Link(
        url: json["url"],
        label: json["label"],
        active: json["active"],
      );

  Map<String, dynamic> toJson() => {
        "url": url,
        "label": label,
        "active": active,
      };
}
