import 'dart:convert';

LocationTargetResponse locationTargetResponseFromJson(String str) =>
    LocationTargetResponse.fromJson(json.decode(str));

String locationTargetResponseToJson(LocationTargetResponse data) =>
    json.encode(data.toJson());

class LocationTargetResponse {
  final bool? error;
  final String? message;
  final Data? data;

  LocationTargetResponse({
    this.error,
    this.message,
    this.data,
  });

  factory LocationTargetResponse.fromJson(Map<String, dynamic> json) =>
      LocationTargetResponse(
        error: json["error"],
        message: json["message"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "error": error,
        "message": message,
        "data": data?.toJson(),
      };
}

class Data {
  final List<Store>? stores;
  final int? totalStores;

  Data({
    this.stores,
    this.totalStores,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        stores: json["stores"] == null
            ? []
            : List<Store>.from(json["stores"]!.map((x) => Store.fromJson(x))),
        totalStores: json["total_stores"],
      );

  Map<String, dynamic> toJson() => {
        "stores": stores == null
            ? []
            : List<dynamic>.from(stores!.map((x) => x.toJson())),
        "total_stores": totalStores,
      };
}

class Store {
  final int? id;
  final String? locationId;
  final dynamic countryId;
  final dynamic stateId;
  final dynamic lgaId;
  final String? storeId;
  final String? name;
  final String? address;
  final dynamic email;
  final dynamic phone;
  final String? country;
  final String? state;
  final String? lga;
  final String? gla;
  final String? gsa;
  final String? duration;
  final int? avgTransactionValue;
  final String? conversionRate;
  final int? isActive;
  final int? isDefault;
  final String? locationType;
  final dynamic longitude;
  final dynamic latitude;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final int? staffNo;
  final int? totalCustomers;

  Store({
    this.id,
    this.locationId,
    this.countryId,
    this.stateId,
    this.lgaId,
    this.storeId,
    this.name,
    this.address,
    this.email,
    this.phone,
    this.country,
    this.state,
    this.lga,
    this.gla,
    this.gsa,
    this.duration,
    this.avgTransactionValue,
    this.conversionRate,
    this.isActive,
    this.isDefault,
    this.locationType,
    this.longitude,
    this.latitude,
    this.createdAt,
    this.updatedAt,
    this.staffNo,
    this.totalCustomers,
  });

  factory Store.fromJson(Map<String, dynamic> json) => Store(
        id: json["id"],
        locationId: json["locationID"],
        countryId: json["country_id"],
        stateId: json["state_id"],
        lgaId: json["lga_id"],
        storeId: json["storeID"],
        name: json["name"],
        address: json["address"],
        email: json["email"],
        phone: json["phone"],
        country: json["country"],
        state: json["state"],
        lga: json["lga"],
        gla: json["gla"],
        gsa: json["gsa"],
        duration: json["duration"],
        avgTransactionValue: json["avg_transaction_value"],
        conversionRate: json["conversion_rate"],
        isActive: json["is_active"],
        isDefault: json["is_default"],
        locationType: json["location_type"],
        longitude: json["longitude"],
        latitude: json["latitude"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        staffNo: json["staff_no"],
        totalCustomers: json["total_customers"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "locationID": locationId,
        "country_id": countryId,
        "state_id": stateId,
        "lga_id": lgaId,
        "storeID": storeId,
        "name": name,
        "address": address,
        "email": email,
        "phone": phone,
        "country": country,
        "state": state,
        "lga": lga,
        "gla": gla,
        "gsa": gsa,
        "duration": duration,
        "avg_transaction_value": avgTransactionValue,
        "conversion_rate": conversionRate,
        "is_active": isActive,
        "is_default": isDefault,
        "location_type": locationType,
        "longitude": longitude,
        "latitude": latitude,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "staff_no": staffNo,
        "total_customers": totalCustomers,
      };
}
