import 'dart:convert';

import 'package:quick_retail_mobile/core/data/models/product_response.dart';

List<ProductVariation> productVariationFromJson(String str) =>
    List<ProductVariation>.from(
        json.decode(str).map((x) => ProductVariation.fromJson(x)));

class ProductVariation {
  final int? id;
  final String? variationId;
  final String? name;
  final String? sku;
  final String? ean;
  final String? code;
  final String? costPrice;
  final String? sellingPrice;
  final int? quantity;
  final String? reorderLevel;
  final String? imagePath;
  final dynamic reason;
  final int? quantitySupplied;
  final int? quantityAvailable;
  final int? quantitySold;
  final int? quantityAvailableInStores;
  final String? status;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? stockStatus;
  final List<VariationAttribute>? variationAttributes;

  ProductVariation({
    this.id,
    this.variationId,
    this.name,
    this.sku,
    this.ean,
    this.code,
    this.costPrice,
    this.sellingPrice,
    this.quantity,
    this.reorderLevel,
    this.imagePath,
    this.reason,
    this.quantitySupplied,
    this.quantityAvailable,
    this.quantitySold,
    this.quantityAvailableInStores,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.stockStatus,
    this.variationAttributes,
  });

  factory ProductVariation.fromJson(Map<String, dynamic> json) =>
      ProductVariation(
        id: json["id"],
        variationId: json["variationID"],
        name: json["name"],
        sku: json["sku"],
        ean: json["ean"],
        code: json["code"],
        costPrice: json["cost_price"],
        sellingPrice: json["selling_price"],
        quantity: json["quantity"],
        reorderLevel: json["reorder_level"],
        imagePath: json["image_path"],
        reason: json["reason"],
        quantitySupplied: json["quantity_supplied"],
        quantityAvailable: json["quantity_available"],
        quantitySold: json["quantity_sold"],
        quantityAvailableInStores: json["quantity_available_in_stores"],
        status: json["status"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        stockStatus: json["stock_status"],
        variationAttributes: json["variation_attributes"] == null
            ? []
            : List<VariationAttribute>.from(json["variation_attributes"]!
                .map((x) => VariationAttribute.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "variationID": variationId,
        "name": name,
        "sku": sku,
        "ean": ean,
        "code": code,
        "cost_price": costPrice,
        "selling_price": sellingPrice,
        "quantity": quantity,
        "reorder_level": reorderLevel,
        "image_path": imagePath,
        "reason": reason,
        "quantity_supplied": quantitySupplied,
        "quantity_available": quantityAvailable,
        "quantity_sold": quantitySold,
        "quantity_available_in_stores": quantityAvailableInStores,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "stock_status": stockStatus,
        "variation_attributes": variationAttributes == null
            ? []
            : List<dynamic>.from(variationAttributes!.map((x) => x.toJson())),
      };
}
