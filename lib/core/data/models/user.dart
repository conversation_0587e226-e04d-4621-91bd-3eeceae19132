class User {
  final int? id;
  final String? userId;
  final String? firstname;
  final String? lastname;
  final String? phoneno;
  final String? phoneNumber;
  final String? email;
  final String? emailVerifiedAt;
  final int? canLogin;
  final int? isVerified;
  final int? isActive;
  final String? tenantId;
  final String? securityQuestion;
  final String? securityAnswer;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<Role>? roles;
  final List<Tenant>? tenants;

  User({
    this.id,
    this.userId,
    this.firstname,
    this.lastname,
    this.phoneno,
    this.phoneNumber,
    this.email,
    this.emailVerifiedAt,
    this.canLogin,
    this.isVerified,
    this.isActive,
    this.tenantId,
    this.securityQuestion,
    this.securityAnswer,
    this.createdAt,
    this.updatedAt,
    this.roles,
    this.tenants,
  });

  factory User.fromJson(Map<String, dynamic> json) => User(
        id: json['id'] as int?,
        userId: json['userId'] as String?,
        firstname: json['firstname'] as String?,
        lastname: json['lastname'] as String?,
        phoneno: json['phoneno'] as String?,
        phoneNumber: json['phone_number'] as String?,
        email: json['email'] as String?,
        emailVerifiedAt: json['email_verified_at'] as String?,
        canLogin: json['can_login'] as int?,
        isVerified: json['is_verified'] as int?,
        isActive: json['is_active'] as int?,
        tenantId: json['tenant_id'] as String?,
        securityQuestion: json['security_question'] as String?,
        securityAnswer: json['security_answer'] as String?,
        createdAt: json['created_at'] != null
            ? DateTime.tryParse(json['created_at'])
            : null,
        updatedAt: json['updated_at'] != null
            ? DateTime.tryParse(json['updated_at'])
            : null,
        roles: (json['roles'] as List<dynamic>?)
            ?.map((e) => Role.fromJson(e))
            .toList(),
        tenants: (json['tenants'] as List<dynamic>?)
            ?.map((e) => Tenant.fromJson(e))
            .toList(),
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'userId': userId,
        'firstname': firstname,
        'lastname': lastname,
        'phoneno': phoneno,
        'phone_number': phoneNumber,
        'email': email,
        'email_verified_at': emailVerifiedAt,
        'can_login': canLogin,
        'is_verified': isVerified,
        'is_active': isActive,
        'tenant_id': tenantId,
        'security_question': securityQuestion,
        'security_answer': securityAnswer,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'roles': roles?.map((e) => e.toJson()).toList(),
        'tenants': tenants?.map((e) => e.toJson()).toList(),
      };
}

class Role {
  final int? id;
  final String? name;
  final String? slug;
  final String? description;
  final int? level;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? deletedAt;
  final RolePivot? pivot;

  Role({
    this.id,
    this.name,
    this.slug,
    this.description,
    this.level,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.pivot,
  });

  factory Role.fromJson(Map<String, dynamic> json) => Role(
        id: json['id'] as int?,
        name: json['name'] as String?,
        slug: json['slug'] as String?,
        description: json['description'] as String?,
        level: json['level'] as int?,
        createdAt: json['created_at'] != null
            ? DateTime.tryParse(json['created_at'])
            : null,
        updatedAt: json['updated_at'] != null
            ? DateTime.tryParse(json['updated_at'])
            : null,
        deletedAt: json['deleted_at'] as String?,
        pivot: json['pivot'] != null ? RolePivot.fromJson(json['pivot']) : null,
      );

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'slug': slug,
        'description': description,
        'level': level,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'deleted_at': deletedAt,
        'pivot': pivot?.toJson(),
      };
}

class RolePivot {
  final int? userId;
  final int? roleId;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  RolePivot({
    this.userId,
    this.roleId,
    this.createdAt,
    this.updatedAt,
  });

  factory RolePivot.fromJson(Map<String, dynamic> json) => RolePivot(
        userId: json['user_id'] is int
            ? json['user_id'] as int?
            : int.tryParse(json['user_id']?.toString() ?? ''),
        roleId: json['role_id'] is int
            ? json['role_id'] as int?
            : int.tryParse(json['role_id']?.toString() ?? ''),
        createdAt: json['created_at'] != null
            ? DateTime.tryParse(json['created_at'])
            : null,
        updatedAt: json['updated_at'] != null
            ? DateTime.tryParse(json['updated_at'])
            : null,
      );

  Map<String, dynamic> toJson() => {
        'user_id': userId,
        'role_id': roleId,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
      };
}

class Tenant {
  final String? uuid;
  final int? companySizeId;
  final String? name;
  final String? domain;
  final String? database;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<Subscription>? subscriptions;

  Tenant({
    this.uuid,
    this.companySizeId,
    this.name,
    this.domain,
    this.database,
    this.createdAt,
    this.updatedAt,
    this.subscriptions,
  });

  factory Tenant.fromJson(Map<String, dynamic> json) => Tenant(
        uuid: json["uuid"],
        companySizeId: json["company_size_id"],
        name: json["name"],
        domain: json["domain"],
        database: json["database"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        subscriptions: json["subscriptions"] == null
            ? []
            : List<Subscription>.from(
                json["subscriptions"]!.map((x) => Subscription.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "uuid": uuid,
        "company_size_id": companySizeId,
        "name": name,
        "domain": domain,
        "database": database,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "subscriptions": subscriptions == null
            ? []
            : List<dynamic>.from(subscriptions!.map((x) => x.toJson())),
      };
}

class Subscription {
  final int? id;
  final String? tenantUuid;
  final String? fullName;
  final String? phoneno;
  final String? email;
  final String? billingType;
  final bool? isTrial;
  final DateTime? billingStart;
  final DateTime? billingEnd;
  final String? totalAmount;
  final DateTime? paidAt;
  final String? channel;
  final String? paymentMethod;
  final String? paymentStatus;
  final String? status;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final dynamic deletedAt;

  Subscription({
    this.id,
    this.tenantUuid,
    this.fullName,
    this.phoneno,
    this.email,
    this.billingType,
    this.isTrial,
    this.billingStart,
    this.billingEnd,
    this.totalAmount,
    this.paidAt,
    this.channel,
    this.paymentMethod,
    this.paymentStatus,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
  });

  factory Subscription.fromJson(Map<String, dynamic> json) => Subscription(
        id: json["id"],
        tenantUuid: json["tenant_uuid"],
        fullName: json["full_name"],
        phoneno: json["phoneno"],
        email: json["email"],
        billingType: json["billing_type"],
        isTrial: json["is_trial"],
        billingStart: json["billing_start"] == null
            ? null
            : DateTime.parse(json["billing_start"]),
        billingEnd: json["billing_end"] == null
            ? null
            : DateTime.parse(json["billing_end"]),
        totalAmount: json["total_amount"],
        paidAt:
            json["paid_at"] == null ? null : DateTime.parse(json["paid_at"]),
        channel: json["channel"],
        paymentMethod: json["payment_method"],
        paymentStatus: json["payment_status"],
        status: json["status"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        deletedAt: json["deleted_at"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "tenant_uuid": tenantUuid,
        "full_name": fullName,
        "phoneno": phoneno,
        "email": email,
        "billing_type": billingType,
        "is_trial": isTrial,
        "billing_start": billingStart?.toIso8601String(),
        "billing_end": billingEnd?.toIso8601String(),
        "total_amount": totalAmount,
        "paid_at": paidAt?.toIso8601String(),
        "channel": channel,
        "payment_method": paymentMethod,
        "payment_status": paymentStatus,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "deleted_at": deletedAt,
      };
}
