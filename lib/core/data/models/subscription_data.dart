import 'package:quick_retail_mobile/core/data/models/application.dart';

class SubscriptionData {
  final int? id;
  final int? userId;
  final int? applicationId;
  final String? monthlyAmount;
  final String? yearlyAmount;
  final String? monthlyDiscount;
  final String? yearlyDiscount;
  final String? monthlyNumberOfDays;
  final String? yearlyNumberOfDays;
  final String? totalMonthlyAmount;
  final String? totalYearlyAmount;
  final String? trialPeriod;
  final String? trialDays;
  final String? userManagement;
  final String? complementary;
  final String? additionalUserSeat;
  final int? additionalUserSeatLimit;
  final dynamic pricePerSeat;
  final String? status;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? deletedAt;
  final dynamic amount;
  final Application? application;
  int totalAdditionalSeats;

  SubscriptionData({
    this.id,
    this.userId,
    this.applicationId,
    this.monthlyAmount,
    this.yearlyAmount,
    this.monthlyDiscount,
    this.yearlyDiscount,
    this.monthlyNumberOfDays,
    this.yearlyNumberOfDays,
    this.totalMonthlyAmount,
    this.totalYearlyAmount,
    this.trialPeriod,
    this.trialDays,
    this.userManagement,
    this.complementary,
    this.additionalUserSeat,
    this.additionalUserSeatLimit,
    this.pricePerSeat,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.amount,
    this.application,
    this.totalAdditionalSeats = 0,
  });

  factory SubscriptionData.fromJson(Map<String, dynamic> json) {
    return SubscriptionData(
      id: json['id'],
      userId: json['user_id'],
      applicationId: json['application_id'],
      monthlyAmount: json['monthly_amount'],
      yearlyAmount: json['yearly_amount'],
      monthlyDiscount: json['monthly_discount'],
      yearlyDiscount: json['yearly_discount'],
      monthlyNumberOfDays: json['monthly_number_of_days'],
      yearlyNumberOfDays: json['yearly_number_of_days'],
      totalMonthlyAmount: json['total_monthly_amount'],
      totalYearlyAmount: json['total_yearly_amount'],
      trialPeriod: json['trial_period'],
      trialDays: json['trial_days'],
      userManagement: json['user_management'],
      complementary: json['complementary'],
      additionalUserSeat: json['additional_user_seat'],
      additionalUserSeatLimit: json['additional_user_seat_limit'],
      pricePerSeat: json['price_per_seat'],
      status: json['status'],
      createdAt: json['created_at'] != null ? DateTime.tryParse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.tryParse(json['updated_at']) : null,
      deletedAt: json['deleted_at'],
      amount: json['amount'],
      application: json['application'] != null ? Application.fromJson(json['application']) : null,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'user_id': userId,
        'application_id': applicationId,
        'monthly_amount': monthlyAmount,
        'yearly_amount': yearlyAmount,
        'monthly_discount': monthlyDiscount,
        'yearly_discount': yearlyDiscount,
        'monthly_number_of_days': monthlyNumberOfDays,
        'yearly_number_of_days': yearlyNumberOfDays,
        'total_monthly_amount': totalMonthlyAmount,
        'total_yearly_amount': totalYearlyAmount,
        'trial_period': trialPeriod,
        'trial_days': trialDays,
        'user_management': userManagement,
        'complementary': complementary,
        'additional_user_seat': additionalUserSeat,
        'additional_user_seat_limit': additionalUserSeatLimit,
        'price_per_seat': pricePerSeat,
        'status': status,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'deleted_at': deletedAt,
        'amount': amount,
        'application': application?.toJson(),
      };
}