import 'dart:convert';

CustomerStatsResponse customerStatsResponseFromJson(String str) =>
    CustomerStatsResponse.fromJson(json.decode(str));

String customerStatsResponseToJson(CustomerStatsResponse data) =>
    json.encode(data.toJson());

class CustomerStatsResponse {
  final bool? error;
  final String? message;
  final CustomerStats? data;

  CustomerStatsResponse({
    this.error,
    this.message,
    this.data,
  });

  factory CustomerStatsResponse.fromJson(Map<String, dynamic> json) =>
      CustomerStatsResponse(
        error: json["error"],
        message: json["message"],
        data:
            json["data"] == null ? null : CustomerStats.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "error": error,
        "message": message,
        "data": data?.toJson(),
      };
}

class CustomerStats {
  final int? newCustomers;
  final int? existingCustomers;
  final int? newCustomersPercentage;
  final int? existingCustomersPercentage;

  CustomerStats({
    this.newCustomers,
    this.existingCustomers,
    this.newCustomersPercentage,
    this.existingCustomersPercentage,
  });

  factory CustomerStats.fromJson(Map<String, dynamic> json) => CustomerStats(
        newCustomers: json["new_customers"],
        existingCustomers: json["existing_customers"],
        newCustomersPercentage: json["new_customers_percentage"],
        existingCustomersPercentage: json["existing_customers_percentage"],
      );

  Map<String, dynamic> toJson() => {
        "new_customers": newCustomers,
        "existing_customers": existingCustomers,
        "new_customers_percentage": newCustomersPercentage,
        "existing_customers_percentage": existingCustomersPercentage,
      };
}
