import 'dart:convert';

AnalysisOverviewResponse analysisOverviewResponseFromJson(String str) =>
    AnalysisOverviewResponse.fromJson(json.decode(str));

String analysisOverviewResponseToJson(AnalysisOverviewResponse data) =>
    json.encode(data.toJson());

class AnalysisOverviewResponse {
  final bool? error;
  final String? message;
  final AnalysisOverview? data;

  AnalysisOverviewResponse({
    this.error,
    this.message,
    this.data,
  });

  factory AnalysisOverviewResponse.fromJson(Map<String, dynamic> json) =>
      AnalysisOverviewResponse(
        error: json["error"],
        message: json["message"],
        data: json["data"] == null
            ? null
            : AnalysisOverview.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "error": error,
        "message": message,
        "data": data?.toJson(),
      };
}

class AnalysisOverview {
  final String? totalRevenue;
  final int? totalOrders;
  final int? totalCustomers;

  AnalysisOverview({
    this.totalRevenue,
    this.totalOrders,
    this.totalCustomers,
  });

  factory AnalysisOverview.fromJson(Map<String, dynamic> json) =>
      AnalysisOverview(
        totalRevenue: json["totalRevenue"],
        totalOrders: json["totalOrders"],
        totalCustomers: json["totalCustomers"],
      );

  Map<String, dynamic> toJson() => {
        "totalRevenue": totalRevenue,
        "totalOrders": totalOrders,
        "totalCustomers": totalCustomers,
      };
}
