import 'dart:convert';

SalesOverviewResponse salesOverviewResponseFromJson(String str) =>
    SalesOverviewResponse.fromJson(json.decode(str));

String salesOverviewResponseToJson(SalesOverviewResponse data) =>
    json.encode(data.toJson());

class SalesOverviewResponse {
  final bool? error;
  final String? message;
  final SalesOverview? data;

  SalesOverviewResponse({
    this.error,
    this.message,
    this.data,
  });

  factory SalesOverviewResponse.fromJson(Map<String, dynamic> json) =>
      SalesOverviewResponse(
        error: json["error"],
        message: json["message"],
        data:
            json["data"] == null ? null : SalesOverview.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "error": error,
        "message": message,
        "data": data?.toJson(),
      };
}

class SalesOverview {
  final int? year;
  final List<Datum>? data;

  SalesOverview({
    this.year,
    this.data,
  });

  factory SalesOverview.fromJson(Map<String, dynamic> json) => SalesOverview(
        year: json["year"],
        data: json["data"] == null
            ? []
            : List<Datum>.from(json["data"]!.map((x) => Datum.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "year": year,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Datum {
  final String? month;
  final double? revenue;

  Datum({
    this.month,
    this.revenue,
  });

  factory Datum.fromJson(Map<String, dynamic> json) => Datum(
        month: json["month"],
        revenue: json["revenue"] is int
            ? json["revenue"].toDouble()
            : json["revenue"],
      );

  Map<String, dynamic> toJson() => {
        "month": month,
        "revenue": revenue,
      };
}
