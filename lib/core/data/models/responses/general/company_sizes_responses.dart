class CompanySizesResponses {
      final bool? error;
  final String? message;
  final List<ListItem>? data;

    CompanySizesResponses({
    this.error,
    this.message,
    this.data,
  });

    factory CompanySizesResponses.fromJson(Map<String, dynamic> json) => CompanySizesResponses(
    error: json["error"],
    message: json["message"],
    data: json["data"] == null ? null : List.from(json["data"]).map((x) => ListItem.fromJson(x)).toList(),
  );

  Map<String, dynamic> toJson() => {
    "error": error,
    "message": message,
    "data": data?.map((x) => x.toJson()).toList(),
  };
}

class ListItem{
  final int? id;
  final String? label;
  final String? description;
  final String? createdAt;
  final String? updatedAt;

  ListItem({
    this.id,
    this.label,
    this.description,
    this.createdAt,
    this.updatedAt,
  });

  factory ListItem.fromJson(Map<String, dynamic> json) => ListItem(
    id: json["id"],
    label: json["label"],
    description: json["description"],
    createdAt: json["created_at"],
    updatedAt: json["updated_at"],
  );

  Map<String, dynamic> toJson() => {
    "id": id,
    "label": label,
    "description": description,
    "created_at": createdAt,
    "updated_at": updatedAt,
  };
}