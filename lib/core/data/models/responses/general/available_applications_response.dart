import 'package:quick_retail_mobile/core/data/models/application.dart';

class AvailableApplicationsResponse {
    final bool? error;
  final String? message;
  final List<Application>? data;

    AvailableApplicationsResponse({
    this.error,
    this.message,
    this.data,
  });

    factory AvailableApplicationsResponse.fromJson(Map<String, dynamic> json) => AvailableApplicationsResponse(
    error: json["error"],
    message: json["message"],
    data: json["data"] == null ? null : List.from(json["data"]).map((x) => Application.fromJson(x)).toList(),
  );

  Map<String, dynamic> toJson() => {
    "error": error,
    "message": message,
    "data": data?.map((x) => x.toJson()).toList(),
  };

}