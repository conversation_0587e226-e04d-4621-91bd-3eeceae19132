
import 'package:quick_retail_mobile/core/data/models/subscription_data.dart';

class AvailableSubscriptionsResponse {
  final bool? error;
  final String? message;
  final List<SubscriptionData>? data;

  AvailableSubscriptionsResponse({
    this.error,
    this.message,
    this.data,
  });

  factory AvailableSubscriptionsResponse.fromJson(Map<String, dynamic> json) {
    return AvailableSubscriptionsResponse(
      error: json['error'],
      message: json['message'],
      data: (json['data'] as List<dynamic>?)
          ?.map((e) => SubscriptionData.fromJson(e))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'error': error,
        'message': message,
        'data': data?.map((e) => e.toJson()).toList(),
      };
}

