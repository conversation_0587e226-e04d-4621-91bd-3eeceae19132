class AllQuestionsResponse {
  final bool? error;
  final String? message;
  final List<QuestionData>? data;

  AllQuestionsResponse({
    this.error,
    this.message,
    this.data,
  });

  factory AllQuestionsResponse.fromJson(Map<String, dynamic> json) {
    return AllQuestionsResponse(
      error: json['error'],
      message: json['message'],
      data: (json['data'] as List?)
          ?.map((e) => QuestionData.fromJson(e))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'error': error,
        'message': message,
        'data': data?.map((e) => e.toJson()).toList(),
      };
}

class QuestionData {
  final int? id;
  final String? question;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  QuestionData({
    this.id,
    this.question,
    this.createdAt,
    this.updatedAt,
  });

  factory QuestionData.fromJson(Map<String, dynamic> json) {
    return QuestionData(
      id: json['id'],
      question: json['question'],
      createdAt: json['created_at'] != null
          ? DateTime.tryParse(json['created_at'])
          : null,
      updatedAt: json['updated_at'] != null
          ? DateTime.tryParse(json['updated_at'])
          : null,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'question': question,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
      };
}
