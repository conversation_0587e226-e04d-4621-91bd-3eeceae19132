class CreatePasswordResponse {
  final bool? error;
  final String? message;
  final CreatePasswordData? data;

  CreatePasswordResponse({
    this.error,
    this.message,
    this.data,
  });

  factory CreatePasswordResponse.fromJson(Map<String, dynamic> json) {
    return CreatePasswordResponse(
      error: json['error'],
      message: json['message'],
      data: json['data'] != null ? CreatePasswordData.fromJson(json['data']) : null,
    );
  }

  Map<String, dynamic> toJson() => {
        'error': error,
        'message': message,
        'data': data?.toJson(),
      };
}

class CreatePasswordData {
  final int? id;
  final String? userId;
  final String? firstname;
  final String? lastname;
  final String? phoneno;
  final String? email;
  final String? emailVerifiedAt;
  final int? canLogin;
  final int? isVerified;
  final int? isActive;
  final dynamic tenantId;
  final String? securityQuestion;
  final String? securityAnswer;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<Role>? roles;

  CreatePasswordData({
    this.id,
    this.userId,
    this.firstname,
    this.lastname,
    this.phoneno,
    this.email,
    this.emailVerifiedAt,
    this.canLogin,
    this.isVerified,
    this.isActive,
    this.tenantId,
    this.securityQuestion,
    this.securityAnswer,
    this.createdAt,
    this.updatedAt,
    this.roles,
  });

  factory CreatePasswordData.fromJson(Map<String, dynamic> json) {
    return CreatePasswordData(
      id: json['id'],
      userId: json['userId'],
      firstname: json['firstname'],
      lastname: json['lastname'],
      phoneno: json['phoneno'],
      email: json['email'],
      emailVerifiedAt: json['email_verified_at'],
      canLogin: json['can_login'],
      isVerified: json['is_verified'],
      isActive: json['is_active'],
      tenantId: json['tenant_id'],
      securityQuestion: json['security_question'],
      securityAnswer: json['security_answer'],
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      roles: json['roles'] != null
          ? List<Role>.from(json['roles'].map((x) => Role.fromJson(x)))
          : null,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'userId': userId,
        'firstname': firstname,
        'lastname': lastname,
        'phoneno': phoneno,
        'email': email,
        'email_verified_at': emailVerifiedAt,
        'can_login': canLogin,
        'is_verified': isVerified,
        'is_active': isActive,
        'tenant_id': tenantId,
        'security_question': securityQuestion,
        'security_answer': securityAnswer,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'roles': roles?.map((x) => x.toJson()).toList(),
      };
}

class Role {
  final int? id;
  final String? name;
  final String? slug;
  final String? description;
  final int? level;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? deletedAt;
  final Pivot? pivot;

  Role({
    this.id,
    this.name,
    this.slug,
    this.description,
    this.level,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
    this.pivot,
  });

  factory Role.fromJson(Map<String, dynamic> json) {
    return Role(
      id: json['id'],
      name: json['name'],
      slug: json['slug'],
      description: json['description'],
      level: json['level'],
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
      deletedAt: json['deleted_at'],
      pivot: json['pivot'] != null ? Pivot.fromJson(json['pivot']) : null,
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'slug': slug,
        'description': description,
        'level': level,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
        'deleted_at': deletedAt,
        'pivot': pivot?.toJson(),
      };
}

class Pivot {
  final int? userId;
  final int? roleId;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Pivot({
    this.userId,
    this.roleId,
    this.createdAt,
    this.updatedAt,
  });

  factory Pivot.fromJson(Map<String, dynamic> json) {
    return Pivot(
      userId: json['user_id'],
      roleId: json['role_id'],
      createdAt: json['created_at'] != null ? DateTime.parse(json['created_at']) : null,
      updatedAt: json['updated_at'] != null ? DateTime.parse(json['updated_at']) : null,
    );
  }

  Map<String, dynamic> toJson() => {
        'user_id': userId,
        'role_id': roleId,
        'created_at': createdAt?.toIso8601String(),
        'updated_at': updatedAt?.toIso8601String(),
      };
}