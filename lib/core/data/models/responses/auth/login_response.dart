import 'package:quick_retail_mobile/core/data/models/user.dart';

class LoginResponse {
  final bool? error;
  final String? message;
  final LoginData? data;

  LoginResponse({this.error, this.message, this.data});

  factory LoginResponse.fromJson(Map<String, dynamic> json) => LoginResponse(
        error: json['error'] as bool?,
        message: json['message'] as String?,
        data: json['data'] != null ? LoginData.fromJson(json['data']) : null,
      );

  Map<String, dynamic> toJson() => {
        'error': error,
        'message': message,
        'data': data?.toJson(),
      };
}

class LoginData {
  final User? user;
  final String? accessToken;
  final String? tokenType;

  LoginData({this.user, this.accessToken, this.tokenType});

  factory LoginData.fromJson(Map<String, dynamic> json) => LoginData(
        user: json['user'] != null ? User.fromJson(json['user']) : null,
        accessToken: json['accessToken'] as String?,
        tokenType: json['tokenType'] as String?,
      );

  Map<String, dynamic> toJson() => {
        'user': user?.toJson(),
        'accessToken': accessToken,
        'tokenType': tokenType,
      };
}


