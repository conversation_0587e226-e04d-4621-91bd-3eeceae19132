import 'package:quick_retail_mobile/core/data/models/responses/general/company_sizes_responses.dart';

class VerifyPaymentResponse {
  final bool? error;
  final String? message;
  final VerifyPaymentData? data;

  VerifyPaymentResponse({
    this.error,
    this.message,
    this.data,
  });

  factory VerifyPaymentResponse.fromJson(Map<String, dynamic> json) {
    return VerifyPaymentResponse(
      error: json['error'],
      message: json['message'],
      data: json['data'] != null
          ? VerifyPaymentData.fromJson(json['data'])
          : null,
    );
  }

  Map<String, dynamic> toJson() => {
        'error': error,
        'message': message,
        'data': data?.toJson(),
      };
}

class VerifyPaymentData {
  final String? userUuid;
  final String? firstname;
  final String? lastname;
  final String? email;
  final String? phoneNumber;
  final String? updatedAt;
  final String? createdAt;
  final List<Tenant>? tenants;
  final List<Role>? roles;

  VerifyPaymentData({
    this.userUuid,
    this.firstname,
    this.lastname,
    this.email,
    this.phoneNumber,
    this.updatedAt,
    this.createdAt,
    this.tenants,
    this.roles,
  });

  factory VerifyPaymentData.fromJson(Map<String, dynamic> json) {
    return VerifyPaymentData(
      userUuid: json['user_uuid'],
      firstname: json['firstname'],
      lastname: json['lastname'],
      email: json['email'],
      phoneNumber: json['phone_number'],
      updatedAt: json['updated_at'],
      createdAt: json['created_at'],
      tenants: (json['tenants'] as List?)
          ?.map((e) => Tenant.fromJson(e))
          .toList(),
      roles: (json['roles'] as List?)
          ?.map((e) => Role.fromJson(e))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'user_uuid': userUuid,
        'firstname': firstname,
        'lastname': lastname,
        'email': email,
        'phone_number': phoneNumber,
        'updated_at': updatedAt,
        'created_at': createdAt,
        'tenants': tenants?.map((e) => e.toJson()).toList(),
        'roles': roles?.map((e) => e.toJson()).toList(),
      };
}

class Tenant {
  final String? uuid;
  final int? companySizeId;
  final String? name;
  final String? domain;
  final String? database;
  final String? createdAt;
  final String? updatedAt;
  final ListItem? companySize;
  final List<Subscription>? subscriptions;

  Tenant({
    this.uuid,
    this.companySizeId,
    this.name,
    this.domain,
    this.database,
    this.createdAt,
    this.updatedAt,
    this.companySize,
    this.subscriptions,
  });

  factory Tenant.fromJson(Map<String, dynamic> json) {
    return Tenant(
      uuid: json['uuid'],
      companySizeId: json['company_size_id'],
      name: json['name'],
      domain: json['domain'],
      database: json['database'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      companySize: json['company_size'] != null
          ? ListItem.fromJson(json['company_size'])
          : null,
      subscriptions: (json['subscriptions'] as List?)
          ?.map((e) => Subscription.fromJson(e))
          .toList(),
    );
  }

  Map<String, dynamic> toJson() => {
        'uuid': uuid,
        'company_size_id': companySizeId,
        'name': name,
        'domain': domain,
        'database': database,
        'created_at': createdAt,
        'updated_at': updatedAt,
        'company_size': companySize?.toJson(),
        'subscriptions': subscriptions?.map((e) => e.toJson()).toList(),
      };
}

// Add the Role, CompanySize, and Subscription classes below, making all fields nullable as well.

class Role {
  final int? id;
  final String? name;
  final String? displayName;
  final String? description;
  final String? createdAt;
  final String? updatedAt;

  Role({
    this.id,
    this.name,
    this.displayName,
    this.description,
    this.createdAt,
    this.updatedAt,
  });

  factory Role.fromJson(Map<String, dynamic> json) {
    return Role(
      id: json['id'],
      name: json['name'],
      displayName: json['display_name'],
      description: json['description'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'name': name,
        'display_name': displayName,
        'description': description,
        'created_at': createdAt,
        'updated_at': updatedAt,
      };
}

// You can add CompanySize and Subscription classes similarly, with all fields nullable and without unnecessary casting.
class Subscription {
  final int? id;
  final String? tenantUuid;
  final String? fullName;
  final String? phoneno;
  final String? email;
  final String? billingType;
  final bool? isTrial;
  final String? billingStart;
  final String? billingEnd;
  final String? totalAmount;
  final String? paidAt;
  final String? channel;
  final String? paymentMethod;
  final String? paymentStatus;
  final String? status;
  final String? createdAt;
  final String? updatedAt;
  final String? deletedAt;

  Subscription({
    this.id,
    this.tenantUuid,
    this.fullName,
    this.phoneno,
    this.email,
    this.billingType,
    this.isTrial,
    this.billingStart,
    this.billingEnd,
    this.totalAmount,
    this.paidAt,
    this.channel,
    this.paymentMethod,
    this.paymentStatus,
    this.status,
    this.createdAt,
    this.updatedAt,
    this.deletedAt,
  });

  factory Subscription.fromJson(Map<String, dynamic> json) {
    return Subscription(
      id: json['id'],
      tenantUuid: json['tenant_uuid'],
      fullName: json['full_name'],
      phoneno: json['phoneno'],
      email: json['email'],
      billingType: json['billing_type'],
      isTrial: json['is_trial'],
      billingStart: json['billing_start'],
      billingEnd: json['billing_end'],
      totalAmount: json['total_amount'],
      paidAt: json['paid_at'],
      channel: json['channel'],
      paymentMethod: json['payment_method'],
      paymentStatus: json['payment_status'],
      status: json['status'],
      createdAt: json['created_at'],
      updatedAt: json['updated_at'],
      deletedAt: json['deleted_at'],
    );
  }

  Map<String, dynamic> toJson() => {
        'id': id,
        'tenant_uuid': tenantUuid,
        'full_name': fullName,
        'phoneno': phoneno,
        'email': email,
        'billing_type': billingType,
        'is_trial': isTrial,
        'billing_start': billingStart,
        'billing_end': billingEnd,
        'total_amount': totalAmount,
        'paid_at': paidAt,
        'channel': channel,
        'payment_method': paymentMethod,
        'payment_status': paymentStatus,
        'status': status,
        'created_at': createdAt,
        'updated_at': updatedAt,
        'deleted_at': deletedAt,
      };
}