class RegisterResponse {
  final bool? error;
  final String? message;
  final RegisterData? data;

  RegisterResponse({
     this.error,
     this.message,
    this.data,
  });

  factory RegisterResponse.fromJson(Map<String, dynamic> json) {
    return RegisterResponse(
      error: json['error'],
      message: json['message'],
      data: json['data'] != null
          ? RegisterData.fromJson(json['data'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'error': error,
      'message': message,
      'data': data?.toJson(),
    };
  }
}

class RegisterData {
  final String? authUrl;
  final String? reference;

  RegisterData({
    this.authUrl,
    this.reference,
  });

  factory RegisterData.fromJson(Map<String, dynamic> json) {
    return RegisterData(
      authUrl: json['auth_url'],
      reference: json['reference'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'auth_url': authUrl,
      'reference': reference,
    };
  }
}