class QueryArgs {
  QueryArgs({
    this.search,
    this.sortBy,
    this.perPage = 30,
    this.limit = "",
    this.paginate = true,
  });

  final String? search;
  final String? sortBy;
  final int? perPage;
  final String? limit;
  final bool paginate;

  Map<String, dynamic> toMap() {
    return {
      'search': search,
      'sortBy': sortBy,
      'per_page': perPage,
      'limit': limit,
      'paginate': paginate,
    };
  }
}
