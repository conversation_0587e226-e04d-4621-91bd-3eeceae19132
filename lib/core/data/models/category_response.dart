import 'dart:convert';

CategoryResponse categoryResponseFromJson(String str) =>
    CategoryResponse.fromJson(json.decode(str));

String categoryResponseToJson(CategoryResponse data) =>
    json.encode(data.toJson());

class CategoryResponse {
  final bool? error;
  final String? message;
  final List<Category>? data;

  CategoryResponse({
    this.error,
    this.message,
    this.data,
  });

  factory CategoryResponse.fromJson(Map<String, dynamic> json) =>
      CategoryResponse(
        error: json["error"],
        message: json["message"],
        data: json["data"] == null
            ? []
            : List<Category>.from(
                json["data"]!.map((x) => Category.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "error": error,
        "message": message,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
      };
}

class Category {
  final int? id;
  final int? createdBy;
  final int? categoryId;
  final String? name;
  final String? slug;
  final String? shortDescription;
  final String? filePath;
  final int? isActive;
  final String? status;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  Category({
    this.id,
    this.createdBy,
    this.categoryId,
    this.name,
    this.slug,
    this.shortDescription,
    this.filePath,
    this.isActive,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  factory Category.fromJson(Map<String, dynamic> json) => Category(
        id: json["id"],
        createdBy: json["created_by"],
        categoryId: json["category_id"],
        name: json["name"],
        slug: json["slug"],
        shortDescription: json["short_description"],
        filePath: json["file_path"],
        isActive: json["is_active"],
        status: json["status"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "created_by": createdBy,
        "category_id": categoryId,
        "name": name,
        "slug": slug,
        "short_description": shortDescription,
        "file_path": filePath,
        "is_active": isActive,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}
