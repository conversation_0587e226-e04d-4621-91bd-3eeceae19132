import 'dart:convert';

SalesOrderResponse salesOrderResponseFromJson(String str) =>
    SalesOrderResponse.fromJson(json.decode(str));

String salesOrderResponseToJson(SalesOrderResponse data) =>
    json.encode(data.toJson());

class SalesOrderResponse {
  final bool? error;
  final String? message;
  final Data? data;

  SalesOrderResponse({
    this.error,
    this.message,
    this.data,
  });

  factory SalesOrderResponse.fromJson(Map<String, dynamic> json) =>
      SalesOrderResponse(
        error: json["error"],
        message: json["message"],
        data: json["data"] == null ? null : Data.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "error": error,
        "message": message,
        "data": data?.toJson(),
      };
}

class Data {
  final Sales? sales;
  final int? totalSales;

  Data({
    this.sales,
    this.totalSales,
  });

  factory Data.fromJson(Map<String, dynamic> json) => Data(
        sales: json["sales"] == null ? null : Sales.fromJson(json["sales"]),
        totalSales: json["total_sales"],
      );

  Map<String, dynamic> toJson() => {
        "sales": sales?.toJson(),
        "total_sales": totalSales,
      };
}

class Sales {
  final int? currentPage;
  final List<SalesData>? data;
  final String? firstPageUrl;
  final int? from;
  final int? lastPage;
  final String? lastPageUrl;
  final List<Link>? links;
  final String? nextPageUrl;
  final String? path;
  final int? perPage;
  final dynamic prevPageUrl;
  final int? to;
  final int? total;

  Sales({
    this.currentPage,
    this.data,
    this.firstPageUrl,
    this.from,
    this.lastPage,
    this.lastPageUrl,
    this.links,
    this.nextPageUrl,
    this.path,
    this.perPage,
    this.prevPageUrl,
    this.to,
    this.total,
  });

  factory Sales.fromJson(Map<String, dynamic> json) => Sales(
        currentPage: json["current_page"],
        data: json["data"] == null
            ? []
            : List<SalesData>.from(
                json["data"]!.map((x) => SalesData.fromJson(x))),
        firstPageUrl: json["first_page_url"],
        from: json["from"],
        lastPage: json["last_page"],
        lastPageUrl: json["last_page_url"],
        links: json["links"] == null
            ? []
            : List<Link>.from(json["links"]!.map((x) => Link.fromJson(x))),
        nextPageUrl: json["next_page_url"],
        path: json["path"],
        perPage: json["per_page"],
        prevPageUrl: json["prev_page_url"],
        to: json["to"],
        total: json["total"],
      );

  Map<String, dynamic> toJson() => {
        "current_page": currentPage,
        "data": data == null
            ? []
            : List<dynamic>.from(data!.map((x) => x.toJson())),
        "first_page_url": firstPageUrl,
        "from": from,
        "last_page": lastPage,
        "last_page_url": lastPageUrl,
        "links": links == null
            ? []
            : List<dynamic>.from(links!.map((x) => x.toJson())),
        "next_page_url": nextPageUrl,
        "path": path,
        "per_page": perPage,
        "prev_page_url": prevPageUrl,
        "to": to,
        "total": total,
      };
}

class SalesData {
  final int? id;
  final String? orderId;
  final String? customerName;
  final String? orderTotal;
  final String? amountPaid;
  final String? fees;
  final String? orderNumber;
  final String? receiptNo;
  final DateTime? dateCompleted;
  final String? status;
  final String? paymentStatus;
  final String? paymentMethod;
  final dynamic cancellationReason;
  final dynamic discountId;
  final int? customerId;
  final int? locationId;
  final dynamic shippingAddressId;
  final dynamic billingAddressId;
  final int? staffId;
  final String? balance;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final List<SaleOrderDetail>? saleOrderDetails;
  final Customer? customer;
  final Cashier? cashier;

  SalesData({
    this.id,
    this.orderId,
    this.customerName,
    this.orderTotal,
    this.amountPaid,
    this.fees,
    this.orderNumber,
    this.receiptNo,
    this.dateCompleted,
    this.status,
    this.paymentStatus,
    this.paymentMethod,
    this.cancellationReason,
    this.discountId,
    this.customerId,
    this.locationId,
    this.shippingAddressId,
    this.billingAddressId,
    this.staffId,
    this.balance,
    this.createdAt,
    this.updatedAt,
    this.saleOrderDetails,
    this.customer,
    this.cashier,
  });

  factory SalesData.fromJson(Map<String, dynamic> json) => SalesData(
        id: json["id"],
        orderId: json["orderID"],
        customerName: json["customer_name"],
        orderTotal: json["order_total"],
        amountPaid: json["amount_paid"],
        fees: json["fees"],
        orderNumber: json["order_number"],
        receiptNo: json["receipt_no"],
        dateCompleted: json["date_completed"] == null
            ? null
            : DateTime.parse(json["date_completed"]),
        status: json["status"],
        paymentStatus: json["payment_status"],
        paymentMethod: json["payment_method"],
        cancellationReason: json["cancellation_reason"],
        discountId: json["discount_id"],
        customerId: json["customer_id"],
        locationId: json["location_id"],
        shippingAddressId: json["shipping_address_id"],
        billingAddressId: json["billing_address_id"],
        staffId: json["staff_id"],
        balance: json["balance"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        saleOrderDetails: json["sale_order_details"] == null
            ? []
            : List<SaleOrderDetail>.from(json["sale_order_details"]!
                .map((x) => SaleOrderDetail.fromJson(x))),
        customer: json["customer"] == null
            ? null
            : Customer.fromJson(json["customer"]),
        cashier:
            json["cashier"] == null ? null : Cashier.fromJson(json["cashier"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "orderID": orderId,
        "customer_name": customerName,
        "order_total": orderTotal,
        "amount_paid": amountPaid,
        "fees": fees,
        "order_number": orderNumber,
        "receipt_no": receiptNo,
        "date_completed": dateCompleted?.toIso8601String(),
        "status": status,
        "payment_status": paymentStatus,
        "payment_method": paymentMethod,
        "cancellation_reason": cancellationReason,
        "discount_id": discountId,
        "customer_id": customerId,
        "location_id": locationId,
        "shipping_address_id": shippingAddressId,
        "billing_address_id": billingAddressId,
        "staff_id": staffId,
        "balance": balance,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "sale_order_details": saleOrderDetails == null
            ? []
            : List<dynamic>.from(saleOrderDetails!.map((x) => x.toJson())),
        "customer": customer?.toJson(),
        "cashier": cashier?.toJson(),
      };
}

class Cashier {
  final String? firstname;
  final String? lastname;

  Cashier({
    this.firstname,
    this.lastname,
  });

  factory Cashier.fromJson(Map<String, dynamic> json) => Cashier(
        firstname: json["firstname"],
        lastname: json["lastname"],
      );

  Map<String, dynamic> toJson() => {
        "firstname": firstname,
        "lastname": firstname,
      };
}

class Customer {
  final String? customerId;
  final String? customerName;
  final String? customerEmail;
  final String? customerPhone;
  final String? customerAddress;
  final DateTime? createdAt;
  final DateTime? updatedAt;
  final String? status;
  final DateTime? lastVisit;

  Customer({
    this.customerId,
    this.customerName,
    this.customerEmail,
    this.customerPhone,
    this.customerAddress,
    this.createdAt,
    this.updatedAt,
    this.status,
    this.lastVisit,
  });

  factory Customer.fromJson(Map<String, dynamic> json) => Customer(
        customerId: json["customerID"],
        customerName: json["customer_name"],
        customerEmail: json["customer_email"],
        customerPhone: json["customer_phone"],
        customerAddress: json["customer_address"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
        status: json["status"],
        lastVisit: json["last_visit"] == null
            ? null
            : DateTime.parse(json["last_visit"]),
      );

  Map<String, dynamic> toJson() => {
        "customerID": customerId,
        "customer_name": customerName,
        "customer_email": customerEmail,
        "customer_phone": customerPhone,
        "customer_address": customerAddress,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
        "status": status,
        "last_visit": lastVisit?.toIso8601String(),
      };
}

class SaleOrderDetail {
  final String? orderDetailId;
  final String? unitPrice;
  final String? totalPrice;
  final int? quantityOrdered;
  final int? quantityReturned;
  final dynamic discountId;
  final int? creditNoteGenerated;
  final String? status;
  final DateTime? createdAt;
  final DateTime? updatedAt;

  SaleOrderDetail({
    this.orderDetailId,
    this.unitPrice,
    this.totalPrice,
    this.quantityOrdered,
    this.quantityReturned,
    this.discountId,
    this.creditNoteGenerated,
    this.status,
    this.createdAt,
    this.updatedAt,
  });

  factory SaleOrderDetail.fromJson(Map<String, dynamic> json) =>
      SaleOrderDetail(
        orderDetailId: json["order_detail_id"],
        unitPrice: json["unit_price"],
        totalPrice: json["total_price"],
        quantityOrdered: json["quantity_ordered"],
        quantityReturned: json["quantity_returned"],
        discountId: json["discount_id"],
        creditNoteGenerated: json["credit_note_generated"],
        status: json["status"],
        createdAt: json["created_at"] == null
            ? null
            : DateTime.parse(json["created_at"]),
        updatedAt: json["updated_at"] == null
            ? null
            : DateTime.parse(json["updated_at"]),
      );

  Map<String, dynamic> toJson() => {
        "order_detail_id": orderDetailId,
        "unit_price": unitPrice,
        "total_price": totalPrice,
        "quantity_ordered": quantityOrdered,
        "quantity_returned": quantityReturned,
        "discount_id": discountId,
        "credit_note_generated": creditNoteGenerated,
        "status": status,
        "created_at": createdAt?.toIso8601String(),
        "updated_at": updatedAt?.toIso8601String(),
      };
}

class Link {
  final String? url;
  final String? label;
  final bool? active;

  Link({
    this.url,
    this.label,
    this.active,
  });

  factory Link.fromJson(Map<String, dynamic> json) => Link(
        url: json["url"],
        label: json["label"],
        active: json["active"],
      );

  Map<String, dynamic> toJson() => {
        "url": url,
        "label": label,
        "active": active,
      };
}
