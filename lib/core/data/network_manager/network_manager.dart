import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:pretty_dio_logger/pretty_dio_logger.dart';
import 'package:quick_retail_mobile/core/constants/app_config.dart';
import 'package:quick_retail_mobile/core/data/enum/request_type.dart';
import 'package:quick_retail_mobile/core/utilities/secure_storage/secure_storage_utils.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';

class NetworkManager {
  static BaseOptions options = BaseOptions(
      connectTimeout: const Duration(minutes: 5),
      receiveTimeout: const Duration(minutes: 5),
      headers: {
        HttpHeaders.acceptHeader: 'application/json',
        HttpHeaders.contentTypeHeader: 'application/json',
      });

  Dio? client = Dio(options);

  final prettyLogger = PrettyDioLogger(
    requestHeader: true,
    requestBody: true,
    responseBody: true,
    responseHeader: false,
    error: true,
    compact: true,
    maxWidth: 10090,
  );

  Future<Map<String, dynamic>> networkRequestManager(
    RequestType requestType,
    String requestUrl, {
    dynamic body,
    queryParameters,
    bool useAuth = true,
    File? backFile,
    bool retrieveResponse = false,
    bool retrieveUnauthorizedResponse = false,
  }) async {
    Map<String, dynamic> apiResponse;

    String? token;
    String? tenantId;
    if (useAuth) {
      token = await SecureStorageUtils.retrieveToken();
      tenantId = await SecureStorageUtils.retrieveTenantId();
    }
    var baseUrl = AppConfig.baseUrl;
    String url = '$baseUrl$requestUrl';

    print("Bearer: $token, Url: $url, Body: $body, Query: $queryParameters");
    client?.interceptors.add(prettyLogger);
    if (useAuth) {
      client!.interceptors.add(InterceptorsWrapper(onRequest:
          (RequestOptions options, RequestInterceptorHandler handler) async {
        options.headers["Authorization"] = "Bearer $token";
        options.headers["X-Tenant-ID"] = tenantId;
        return handler.next(options);
      }));
    }
    try {
      switch (requestType) {
        case RequestType.get:
          var response =
              await client!.get(url, queryParameters: queryParameters);
          log("get ....: ${response.data.toString()}");
          apiResponse = response.data;
          break;
        case RequestType.post:
          var response = await client!
              .post(url, data: body, queryParameters: queryParameters);
          log("post ....: ${response.data.toString()}");
          apiResponse = response.data;
          break;
        case RequestType.multiPartPost:
          client!.interceptors.add(InterceptorsWrapper(onRequest:
              (RequestOptions options,
                  RequestInterceptorHandler handler) async {
            return handler.next(options);
          }));
          var response = await client!
              .post(url, data: body, queryParameters: queryParameters);
          apiResponse = response.data;
          break;
        case RequestType.put:
          var response = await client!
              .put(url, data: body, queryParameters: queryParameters);
          log("put ....: ${response.data.toString()}");
          apiResponse = response.data;
          break;
        case RequestType.patch:
          var response = await client!
              .patch(url, data: body, queryParameters: queryParameters);
          apiResponse = response.data;
          break;
        case RequestType.delete:
          var response = await client!
              .delete(url, data: body, queryParameters: queryParameters);
          if (response.data.toString().isEmpty) {
            apiResponse = response.data;
          } else {
            apiResponse = response.data;
          }
          break;
        default:
          var response = await client!
              .post(url, data: body, queryParameters: queryParameters);
          apiResponse = response.data;
          break;
      }
      return apiResponse;
    } on TimeoutException catch (_) {
      throw ("Network timed out, please check your network connection and try again");
    } on DioException catch (e) {
      if (DioExceptionType.receiveTimeout == e.type ||
          DioExceptionType.connectionTimeout == e.type ||
          DioExceptionType.connectionError == e.type) {
        throw ("Network timed out, please check your network connection and try again");
      }
      if (DioExceptionType.unknown == e.type) {
        if (e.message!.contains('SocketException')) {
          throw ("No internet connection, please check your network connection and try again");
        } else {
          throw ("An error occurred processing this request, please try again later");
        }
      }
      debugPrint('code; ${e.response!.statusCode}>>>');
      if (e.response!.statusCode == 400) {
        //print("status code: ${e.response!.statusCode} ...... $url||||||||||||");
        if (retrieveResponse) {
          apiResponse = e.response!.data;
          return apiResponse;
        }
        apiResponse = e.response!.data;
        throw (apiResponse['message']);
      } else if (e.response!.statusCode == 401) {
        if (Utilities.unauthorizedFlag == false) {
          //push user out of the app
          sessionExpired();
        }
        apiResponse = e.response!.data;
        if (retrieveUnauthorizedResponse) {
          return apiResponse;
        } else {
          throw (apiResponse['message']);
        }
      } else if (e.response!.statusCode == 403) {
        apiResponse = e.response!.data;
        if (retrieveUnauthorizedResponse) {
          return apiResponse;
        } else {
          throw (apiResponse['message']);
        }
      } else if (e.response!.statusCode == 404) {
        apiResponse = e.response!.data;
        if (apiResponse['message'] != null) throw (apiResponse['message']);
        throw ("An error occurred, resource is currently unavailable");
      } else if (e.response!.statusCode!
          .isBetween(402, 422, e.response!.statusCode!)) {
        apiResponse = e.response!.data;
        if (apiResponse['message'] != null) {
          throw (apiResponse['message']);
        } else {
          throw ("Invalid authorization credentials, please check your details and try again!");
        }
      } else if (e.response!.statusCode!
          .isBetween(500, 599, e.response!.statusCode!)) {
        if (e.response!.statusCode == 502) {
          throw ("We are unable to process request at this time, please try again later");
        } else {
          apiResponse = e.response!.data;
          if (apiResponse['message'] != null) {
            throw (apiResponse['message']);
          } else {
            throw ("We are unable to process request at this time, please try again later");
          }
        }
      } else {
        apiResponse = e.response!.data;
        if (apiResponse['message'] != null) {
          throw ("Unable to process request, ${apiResponse['message']}");
        }

        throw ("An error occurred while processing your request, please try again");
      }
    } catch (e) {
      throw ("An error occurred while processing this request");
    }
  }
}

extension Range2 on num {
  bool isBetween(int from, int to, int statusCode) {
    return from <= statusCode && to >= statusCode;
  }
}

sessionExpired() {
  // Utilities.unauthorizedFlag = true;
  // NavigationService navigationService = locator<NavigationService>();
  // navigationService.clearAllRoutes(
  //     routeName: NamedRoutes.login,
  // );
}
