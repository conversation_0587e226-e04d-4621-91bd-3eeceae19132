// import 'package:flutter/material.dart';
// import 'package:package_info_plus/package_info_plus.dart';


// class TargetDeviceUtils{

//   static String appVersion = '';
//   static String appBuildNumber = '';

//   static void getDeviceProperties()async{

//     PackageInfo packageInfo = await PackageInfo.fromPlatform();

//     try {
//       appVersion = packageInfo.version;
//       appBuildNumber = packageInfo.buildNumber;
//       debugPrint("app version:$appVersion ...  app build:$appBuildNumber>>>>");
//     } catch (e) {
//       debugPrint("${e.toString()}>>>>");
//     }




// }
// }