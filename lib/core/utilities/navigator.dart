import 'package:flutter/material.dart';
import 'package:page_transition/page_transition.dart';

/// push
pushNavigation(
    {required BuildContext context, required Widget widget, String? routeName, PageTransitionType? transitionType, Duration? transitionDuration}) async {
  Navigator.push(
      context,
      PageTransition(
          type: transitionType ?? PageTransitionType.fade,
          settings: RouteSettings(name: routeName),
          child: widget,
          duration: transitionDuration ?? const Duration(milliseconds: 300)));
}

///push and clear
pushAndClearNavigation(
    {required BuildContext context, required Widget widget, String? routeName, String? clearRoute, PageTransitionType? transitionType, Duration? transitionDuration}) async {
  Navigator.pushAndRemoveUntil(context, PageTransition(
      type: transitionType ?? PageTransitionType.fade,
      child: widget,
      settings: RouteSettings(name: routeName),
      duration: transitionDuration ?? const Duration(milliseconds: 300)),
      ModalRoute.withName(clearRoute ?? '/'));
}

///push and clear all
pushAndClearAllNavigation(
    {required BuildContext context, required Widget widget, String? routeName, PageTransitionType? transitionType, Duration? transitionDuration}) async {
  Navigator.pushAndRemoveUntil(context, PageTransition(
      type: transitionType ?? PageTransitionType.fade,
      child: widget,
      settings: RouteSettings(name: routeName),
      duration: transitionDuration ?? const Duration(milliseconds: 300)),
          (Route<dynamic> route) => false
  );
}

/// push replacement
replaceNavigation(
    {required BuildContext context, String? routeName, required Widget widget, PageTransitionType? transitionType, Duration? transitionDuration}) async {
  Navigator.pushReplacement(
      context,
      PageTransition(
          type: transitionType ?? PageTransitionType.fade,
          child: widget,
          settings: RouteSettings(name: routeName),
          duration: transitionDuration ?? const Duration(milliseconds: 300)));
}

/// pop
popNavigation({required BuildContext context}){
  Navigator.of(context).pop();
}

///pop until
popUntilNavigation({required BuildContext context, String? route}){
  Navigator.popUntil(
      context, ModalRoute.withName(route!));
}


