import 'package:get_it/get_it.dart';
import 'package:quick_retail_mobile/core/data/data_provider/auth_data_provider/auth_data_provider.dart';
import 'package:quick_retail_mobile/core/data/data_provider/general_data/general_data_provider.dart';
import 'package:quick_retail_mobile/core/data/data_provider/pos_dashboard/category_provider.dart';
import 'package:quick_retail_mobile/core/data/data_provider/pos_dashboard/customer_provider.dart';
import 'package:quick_retail_mobile/core/data/data_provider/pos_dashboard/location_provider.dart';
import 'package:quick_retail_mobile/core/data/data_provider/pos_dashboard/pos_dashboard_provider.dart';
import 'package:quick_retail_mobile/core/data/data_provider/pos_dashboard/product_provider.dart';
import 'package:quick_retail_mobile/core/data/data_provider/pos_dashboard/sales_provider.dart';
import 'package:quick_retail_mobile/core/data/services/navigation_service.dart';

GetIt locator = GetIt.instance;

void setupLocator() {
  //register api classes
  locator
      .registerLazySingleton<GeneralDataProvider>(() => GeneralDataProvider());
  locator.registerLazySingleton<AuthDataProvider>(() => AuthDataProvider());
  locator.registerLazySingleton<PosDashboardProvider>(
      () => PosDashboardProvider());
  locator.registerLazySingleton<CustomerProvider>(() => CustomerProvider());
  locator.registerLazySingleton<SalesProvider>(() => SalesProvider());
  locator.registerLazySingleton<ProductProvider>(() => ProductProvider());
  locator.registerLazySingleton<CategoryProvider>(() => CategoryProvider());
  locator.registerLazySingleton<LocationProvider>(() => LocationProvider());

  // locator.registerLazySingleton<SetDetailsDataProvider>(
  //     () => SetDetailsDataProvider());
  // locator
  //     .registerLazySingleton<ProfileDataProvider>(() => ProfileDataProvider());
  // locator.registerLazySingleton<MemberProfileDataProvider>(
  //     () => MemberProfileDataProvider());
  // locator.registerLazySingleton<ElectionDataProvider>(
  //     () => ElectionDataProvider());
  // locator.registerLazySingleton<DuesDataProvider>(() => DuesDataProvider());
  // locator
  //     .registerLazySingleton<PaymentDataProvider>(() => PaymentDataProvider());
  // locator.registerLazySingleton<EventDataProvider>(() => EventDataProvider());
  // locator.registerLazySingleton<PayStackDataProvider>(
  //     () => PayStackDataProvider());
  // locator.registerLazySingleton<DonationDataProvider>(
  //     () => DonationDataProvider());
  // locator.registerLazySingleton<NotificationDataProvider>(
  //     () => NotificationDataProvider());
  // locator.registerLazySingleton<PostProvider>(() => PostProvider());
  // locator.registerLazySingleton<CommentProvider>(() => CommentProvider());
  // locator.registerLazySingleton<CommunityDataProvider>(() => CommunityDataProvider());
  // locator.registerLazySingleton<CommunityFeedDataProvider>(() => CommunityFeedDataProvider());
  // locator.registerLazySingleton<ChatDataProvider>(() => ChatDataProvider());

  ///services
  locator.registerLazySingleton(() => NavigationService());
  // locator.registerLazySingleton(() => PusherService());
}
