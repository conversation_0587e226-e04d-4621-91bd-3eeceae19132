import 'package:flutter/material.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';



Route<dynamic> generateRoute(RouteSettings settings) {
  switch (settings.name) {
    // case NamedRoutes.login:
    //   return _getPageRoute(
    //     routeName: settings.name!,
    //     viewToShow: const Login(),
    //   );
    // case NamedRoutes.viewElectionPosition:
    //   return _getPageRoute(
    //     routeName: settings.name!,
    //     viewToShow: const ViewElectionPosition(),
    //   );
    //todo: add more routes
    default:
      return MaterialPageRoute(
        builder: (context) => Scaffold(
          body: Center(
            child: Text('No path for ${settings.name}'),
          ),
        ),
      );



  }
}

PageRoute _getPageRoute({required String routeName, required Widget viewToShow}) {
  return MaterialPageRoute(
      settings: RouteSettings(
        name: routeName,
      ),
      builder: (_) => viewToShow);
}