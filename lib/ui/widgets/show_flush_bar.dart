import 'package:flutter/material.dart';
import 'package:another_flushbar/flushbar.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text.dart';




showFlushBar({required BuildContext context, String? fontFamily, Color bgColor = ColorPath.soapStoneRed, required String message, Color messageColor = Colors.white, FlushbarPosition position = FlushbarPosition.TOP, int duration = 3, bool isPersistent = false, bool success = true}) {

  Flushbar(
    margin: EdgeInsets.symmetric(horizontal: 16.w),
    padding: EdgeInsets.symmetric(vertical: 16.h, horizontal: 16.w),
    backgroundColor: success ? ColorPath.fetaGreen : bgColor,
    dismissDirection: FlushbarDismissDirection.HORIZONTAL,
    forwardAnimationCurve: Curves.fastLinearToSlowEaseIn,
    flushbarPosition: position,
    borderRadius: BorderRadius.all(Radius.circular(8.r)),
    borderColor: success ? ColorPath.riptideGreen : ColorPath.shirazRed,
    blockBackgroundInteraction: false,
    messageText: Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  SvgPicture.asset(
                    success ? Utilities.getSvg("success") :Utilities.getSvg("error"),
                    height: 20.h,
                    width: 20.w,
                  ),
                  Icon(Icons.close, color: success ? ColorPath.funGreen : ColorPath.thunderbirdRed,)
                ],
              ),
              SizedBox(height: 12.h,),
              CustomText(
                  text: message,
                  fontColor: success ? ColorPath.funGreen : ColorPath.thunderbirdRed,
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w400,
                  fontFamily: fontFamily,
              ),
            ],
          ),
        ),

      ],
    ),
    duration: isPersistent ? null : Duration(seconds: duration),
  ).show(context);
}