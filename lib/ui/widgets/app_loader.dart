import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/ui/widgets/render_lottie.dart';


class AppLoader extends StatelessWidget {
  final double? size;
  const AppLoader({super.key, this.size});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: SpinKitSpinningCircle(
        size: size ?? 30, //200
        itemBuilder: (BuildContext context, int index) {

          return Container(
            height: 10.h,
            width: 10.w,
            color: ColorPath.flamingo,
          );

          return const RenderLottie(
            lottieAsset: 'assets/json/Gift_congratulation.json',
            repeat: false,
          );
        },
      ),
    );
  }
}
