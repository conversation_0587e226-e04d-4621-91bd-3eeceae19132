import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_constants.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';

class DemoBookedSuccessfulDialog extends StatelessWidget {
  const DemoBookedSuccessfulDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            Image.asset(
              Utilities.getImage('jpg/success', fileformat: 'gif'),
              height: 48,
              width: 48,
            ),
          ],
        ),
        Row(
          children: [
            Text(
              "Demo Booked Successfully!!!",
              style: textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.w500,
                  fontFamily: clashDisplay,
                  letterSpacing: -1,
                  fontSize: 22.sp),
            ),
            // Expanded(
            //   child: SizedBox(
            //     width: 14.w,
            //   ),
            // ),
            // Clickable(
            //     onPressed: () {
            //       popNavigation(context: context);
            //     },
            //     child: SvgPicture.asset(Utilities.getSvg('close')))
          ],
        ),
        SizedBox(
          height: 8.h,
        ),
        Text(
          "Check your inbox!\n\nWe’ve sent you an email with exciting details. Please check your inbox (and spam folder, just in case!) to discover the incredible possibilities with Quick Retail.\n\nThank you for joining us on this Journey!Warm regards,\n\nThe Quick Retail Team.",
          style: textTheme.bodyMedium
              ?.copyWith(color: colorScheme.subTextSecondary),
        ),
        SizedBox(
          height: 28.h,
        ),
        // CustomButton(
        //   onPressed: () {
        //     popNavigation(context: context);
        //   },
        //   borderColor: ColorPath.flamingo,
        //   buttonTextColor: ColorPath.flamingo,
        //   buttonText: "Cancel",
        // ),
        SizedBox(
          height: 16.h,
        ),
        CustomButton(
          onPressed: () {
            // popUntilNavigation(context: context, route: NamedRoutes.login);
            popNavigation(context: context);
          },
          buttonText: "Explore more apps",
        ),
      ],
    );
  }
}
