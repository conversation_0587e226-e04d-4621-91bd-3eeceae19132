import 'dart:async';
import 'package:flutter/material.dart';

alertDialogWrapper(
    {required BuildContext context,
    required Widget child,
    bool barrierDismissible = true,
    bool useSafeArea = true,
    bool useRootNavigator = true,
    Color? barrierColor,
    FutureOr<void> Function()? whenCompleteAction}) {
  showDialog(
      context: context,
      barrierDismissible: barrierDismissible,
      useSafeArea: useSafeArea,
      useRootNavigator: useRootNavigator,
      barrierColor: barrierColor,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: Colors.white,
          // contentPadding: EdgeInsets.all(16),
          content: child,
        );
      }).whenComplete(whenCompleteAction ?? () {});
}
