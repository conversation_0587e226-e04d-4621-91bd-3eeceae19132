import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';

class PasswordResetSuccessfulDialog extends StatelessWidget {
  const PasswordResetSuccessfulDialog({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Row(
          children: [
            Text(
              "Password Reset Sucessful!",
              style: textTheme.titleLarge
                  ?.copyWith(letterSpacing: -1, fontSize: 20.sp),
            ),
            Expanded(
              child: SizedBox(
                width: 14.w,
              ),
            ),
            Clickable(
                onPressed: () {
                  popNavigation(context: context);
                },
                child: SvgPicture.asset(Utilities.getSvg('close')))
          ],
        ),
        SizedBox(
          height: 8.h,
        ),
        Text(
          "You have successfully changed your password. Go back to the login screen to input new password.",
          style: textTheme.bodyMedium
              ?.copyWith(color: colorScheme.subTextSecondary),
        ),
        SizedBox(
          height: 28.h,
        ),
        CustomButton(
          onPressed: () {
            popNavigation(context: context);
          },
          borderColor: ColorPath.flamingo,
          buttonTextColor: ColorPath.flamingo,
          buttonText: "Cancel",
        ),
        SizedBox(
          height: 16.h,
        ),
        CustomButton(
          onPressed: () {
            popUntilNavigation(context: context, route: NamedRoutes.login);
          },
          buttonText: "Go to login",
        ),
      ],
    );
  }
}
