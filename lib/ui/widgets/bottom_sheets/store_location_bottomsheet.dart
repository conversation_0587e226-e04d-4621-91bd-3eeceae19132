import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/models/location_target_response.dart';
import 'package:quick_retail_mobile/core/data/view_models/location_view_model.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottomsheet_header.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/empty_state.dart';
import 'package:quick_retail_mobile/ui/widgets/error_state.dart';
import 'package:quick_retail_mobile/ui/widgets/loadable_content_builder.dart';
import 'package:skeletonizer/skeletonizer.dart';

class StoreLocationBottomsheet extends ConsumerStatefulWidget {
  const StoreLocationBottomsheet({
    super.key,
    required this.returningValue,
  });

  final ValueChanged<Store> returningValue;

  @override
  ConsumerState<StoreLocationBottomsheet> createState() =>
      _StoreLocationBottomsheetState();
}

class _StoreLocationBottomsheetState
    extends ConsumerState<StoreLocationBottomsheet> {
  _getStoreLocations() async {
    await ref.read(locationViewModel).fetchAllStoreLocations();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    final locationVm = ref.watch(locationViewModel);
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.65,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const BottomSheetHeader(
            title: "Location",
            subTitle: "Select the location of this product.",
          ),
          SizedBox(height: 16.h),
          const Divider(color: ColorPath.grey100),
          Expanded(
            child: LoadableContentBuilder(
              isBusy: locationVm.state == ViewState.busy,
              isError: locationVm.state == ViewState.error,
              items: locationVm.locations,
              loadingBuilder: (context) {
                return ListView.separated(
                  padding: EdgeInsets.only(
                    top: 16.h,
                    bottom: 50.h,
                  ),
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return const Skeletonizer(
                      enabled: true,
                      child: Text("Category A Category A"),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(height: 24.h);
                  },
                  itemCount: 14,
                );
              },
              errorBuilder: (p0) {
                return SizedBox(
                  height: 175.h,
                  child: Center(
                    child: ErrorState(
                      message: locationVm.message,
                      onPressed: () {
                        _getStoreLocations();
                      },
                    ),
                  ),
                );
              },
              emptyBuilder: (ctx) {
                return Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: Padding(
                    padding: const EdgeInsets.only(top: 24.0),
                    child: EmptyState(
                      imageAsset: Utilities.getSvg("noData"),
                      showCTA: false,
                      title: "No Location",
                      subTitle:
                          "You currently have not recent location added yet",
                    ),
                  ),
                );
              },
              contentBuilder: (ctx) {
                return ListView.separated(
                  padding: EdgeInsets.only(
                    top: 16.h,
                    bottom: 50.h,
                  ),
                  itemBuilder: (ctx, i) {
                    Store order = locationVm.locations[i];
                    return Clickable(
                      onPressed: () {
                        widget.returningValue(order);
                        popNavigation(context: context);
                      },
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          vertical: 8.h,
                        ),
                        child:
                            Text(order.name ?? '', style: textTheme.bodyMedium),
                      ),
                    );
                  },
                  separatorBuilder: (ctx, i) {
                    return SizedBox(height: 16.h);
                  },
                  itemCount: locationVm.locations.length,
                );
              },
            ),
          )
        ],
      ),
    );
  }
}
