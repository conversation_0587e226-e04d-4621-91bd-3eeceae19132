import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/data/models/quick_action_item.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottomsheet_header.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';

class DiscountTypeBottomsheet extends StatelessWidget {
  DiscountTypeBottomsheet({super.key});

  final List<QuickActionsItem> quickActions = [
    QuickActionsItem(
      actionName: "Fixed Value",
      subtitle: "Enter a specific ans fixed naira value e.g ₦400 ",
      assetName: "edit",
      onPressed: () {},
    ),
    QuickActionsItem(
      actionName: "Percentage Value",
      subtitle: "Enter a specific percentage Value e.g 5%",
      assetName: "toggle",
      onPressed: () {},
    ),
  ];

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const BottomSheetHeader(
          title: "Discount Type ",
          subTitle: "Set the discount type with ease.",
        ),
        SizedBox(
          height: 24.h,
        ),
        ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              return Clickable(
                onPressed: quickActions[index].onPressed,
                child: Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                      border: Border.all(color: ColorPath.athensGrey),
                      borderRadius: BorderRadius.circular(8.r)),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(12.w),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.r),
                            color: ColorPath.flamingo.withOpacity(.2)),
                        child: SvgPicture.asset(
                            Utilities.getSvg(quickActions[index].assetName)),
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(quickActions[index].actionName),
                            Text(
                              quickActions[index].subtitle ?? '',
                              style: textTheme.bodySmall
                                  ?.copyWith(color: colorScheme.subTextPrimary),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              );
            },
            separatorBuilder: (context, index) {
              return SizedBox(
                height: 16.h,
              );
            },
            itemCount: quickActions.length),
      ],
    );
  }
}
