import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/pages/pos/pos_dashboard.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottomsheet_header.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';

class StoreFilterBottomsheet extends StatelessWidget {
  const StoreFilterBottomsheet({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const BottomSheetHeader(
          title: "Filter Store Data",
          subTitle: "Filter by following options.",
        ),
        SizedBox(
          height: 16.h,
        ),
        Container(
          height: 1.h,
          color: ColorPath.athensGrey,
        ),
        SizedBox(
          height: 24.h,
        ),
        ListView.separated(
            shrinkWrap: true,
            itemBuilder: (context, index) {
              return Clickable(
                onPressed: () {},
                child: Row(
                  children: [
                    Container(
                      height: 16,
                      width: 16,
                      decoration: BoxDecoration(
                        border:
                            Border.all(color: ColorPath.athensGrey, width: 2),
                        shape: BoxShape.circle,
                        //  color: ColorPath.athensGrey,
                      ),
                    ),
                    SizedBox(
                      width: 8.w,
                    ),
                    Text(
                      "Last 3 days",
                      style: textTheme.bodyLarge
                          ?.copyWith(color: colorScheme.text4),
                    )
                  ],
                ),
              );
            },
            separatorBuilder: (context, index) {
              return SizedBox(
                height: 12.h,
              );
            },
            itemCount: 3),
        SizedBox(
          height: 24.h,
        ),
        Clickable(
          onPressed: () {
            showDatePicker(context: context, firstDate: DateTime(2000), lastDate: DateTime.now());
          },
          child: Row(
            children: [
              Text("Enter a Custom Date"),
              SizedBox(
                width: 8.w,
              ),
              SvgPicture.asset(
                Utilities.getSvg("calendar"),
                color: ColorPath.flamingo,
              )
            ],
          ),
        )
      ],
    );
  }
}
