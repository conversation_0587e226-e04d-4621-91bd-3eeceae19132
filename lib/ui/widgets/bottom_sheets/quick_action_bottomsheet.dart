import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/data/models/quick_action_item.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottomsheet_header.dart';

class QuickActionBottomsheet extends StatelessWidget {
  QuickActionBottomsheet({
    super.key,
  });

  final List quickActions = [
    QuickActionsItem(
      actionName: "Add New Product",
      assetName: "item",
      onPressed: () {},
    ),
    QuickActionsItem(
      actionName: "Add Product Via BarCode Scan",
      assetName: "item",
      onPressed: () {},
    ),
    QuickActionsItem(
      actionName: "Create a Sales order",
      assetName: "item",
      onPressed: () {},
    ),
    QuickActionsItem(
      actionName: "Update Inventory",
      assetName: "item",
      onPressed: () {},
    ),
    QuickActionsItem(
      actionName: "Add Goods Return",
      assetName: "item",
      onPressed: () {},
    ),
    QuickActionsItem(
      actionName: "Create a Discount",
      assetName: "item",
      onPressed: () {},
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const BottomSheetHeader(
          title: "Quick Action",
          subTitle: "Create, manage with one click",
        ),
        SizedBox(
          height: 24.h,
        ),
        ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              return Container(
                padding: EdgeInsets.all(16.w),
                decoration: BoxDecoration(
                    border: Border.all(color: ColorPath.athensGrey),
                    borderRadius: BorderRadius.circular(8.r)),
                child: Row(
                  children: [
                    Container(
                      padding: EdgeInsets.all(12.w),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8.r),
                          color: ColorPath.flamingo.withOpacity(.2)),
                      child: SvgPicture.asset(
                          Utilities.getSvg(quickActions[index].assetName)),
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    Text(quickActions[index].actionName)
                  ],
                ),
              );
            },
            separatorBuilder: (context, index) {
              return SizedBox(
                height: 16.h,
              );
            },
            itemCount: quickActions.length)
      ],
    );
  }
}
