import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottomsheet_header.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';

class CategoryItem {
  final String name;
  final int subCategoryCount;
  bool isSelected;

  CategoryItem({
    required this.name,
    required this.subCategoryCount,
    this.isSelected = false,
  });
}

class AddCategoryBottomsheet extends StatefulWidget {
  final Function(List<CategoryItem>)? onCategoriesSelected;

  const AddCategoryBottomsheet({
    super.key,
    this.onCategoriesSelected,
  });

  @override
  State<AddCategoryBottomsheet> createState() => _AddCategoryBottomsheetState();
}

class _AddCategoryBottomsheetState extends State<AddCategoryBottomsheet> {
  // Mock data for categories
  final List<CategoryItem> _categories = [
    CategoryItem(name: "Category Name", subCategoryCount: 3),
    CategoryItem(name: "Electronics", subCategoryCount: 5),
    CategoryItem(name: "Furniture", subCategoryCount: 4),
    CategoryItem(name: "Clothing", subCategoryCount: 6),
  ];

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.65,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BottomSheetHeader(
            title: "Add a Category",
            subTitle: "Add a sub-category to a category",
            onPressed: () {
              popNavigation(context: context);
            },
          ),
          SizedBox(height: 24.h),

          // Category list
          // ..._categories.map((category) => _buildCategoryItem(category)),
          Expanded(
            child: ListView.separated(
              itemCount: _categories.length,
              separatorBuilder: (context, index) => SizedBox(height: 16.h),
              itemBuilder: (context, index) {
                return _buildCategoryItem(_categories[index]);
              },
            ),
          ),

          // Action buttons
          CustomButton(
            onPressed: () {
              // Get selected categories
              final selectedCategories =
                  _categories.where((cat) => cat.isSelected).toList();

              // Call the callback if provided
              if (widget.onCategoriesSelected != null) {
                widget.onCategoriesSelected!(selectedCategories);
              }

              // Close the bottom sheet
              popNavigation(context: context);
            },
            buttonText: "Add to Category",
            bgColor: ColorPath.flamingo,
          ),

          SizedBox(height: 16.h),

          CustomButton(
            onPressed: () {
              popNavigation(context: context);
            },
            buttonText: "No, Close",
            bgColor: Colors.white,
            borderColor: ColorPath.flamingo,
            buttonTextColor: ColorPath.flamingo,
          ),
          SizedBox(height: 20.h),
        ],
      ),
    );
  }

  Widget _buildCategoryItem(CategoryItem category) {
    return Padding(
      padding: EdgeInsets.only(bottom: 16.h),
      child: Clickable(
        onPressed: () {
          setState(() {
            category.isSelected = !category.isSelected;
          });
        },
        child: Container(
          padding: EdgeInsets.all(16.r),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(
              color: category.isSelected
                  ? ColorPath.flamingo
                  : ColorPath.athensGrey,
            ),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Checkbox
              Container(
                height: 16.h,
                width: 16.w,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(4.r),
                  border: Border.all(
                    color: category.isSelected
                        ? ColorPath.flamingo
                        : ColorPath.mischkaGrey,
                  ),
                  color: category.isSelected
                      ? ColorPath.flamingo
                      : Colors.transparent,
                ),
                child: category.isSelected
                    ? const Icon(
                        Icons.check,
                        color: Colors.white,
                        size: 16,
                      )
                    : null,
              ),

              SizedBox(width: 8.w),

              // Category details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      category.name,
                      style: TextStyle(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w500,
                        color: ColorPath.grey800,
                      ),
                    ),
                    SizedBox(height: 4.h),
                    RichText(
                      text: TextSpan(
                        style: TextStyle(
                          color: ColorPath.grey500,
                          fontSize: 12.sp,
                        ),
                        children: [
                          const TextSpan(text: 'No. of Sub-category: '),
                          TextSpan(
                            text: category.subCategoryCount
                                .toString()
                                .padLeft(2, '0'),
                            style: const TextStyle(
                              fontWeight: FontWeight.w500,
                              color: ColorPath.grey800,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
