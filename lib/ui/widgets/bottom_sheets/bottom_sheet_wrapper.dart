import 'dart:async';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

bottomSheetWrapper(
    {required BuildContext context,
    required Widget child,
    bool isDismissible = true,
    bool enableDrag = true,
    FutureOr<void> Function()? whenCompleteAction}) {
  showModalBottomSheet(
      isScrollControlled: true,
      enableDrag: enableDrag,
      isDismissible: isDismissible,
      useSafeArea: true,
      backgroundColor: Colors.transparent,
      context: context,
      builder: (BuildContext context) {
        return Container(
          clipBehavior: Clip.antiAlias,
          // margin: EdgeInsets.only(
          //     bottom: 40 + MediaQuery.of(context).viewInsets.bottom,
          //     left: 16,
          //     right: 16),
          // height: 500,
          width: double.infinity,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(32.r),
                  topRight: Radius.circular(32.r))),
          child: Material(
            child: Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 24.0, vertical: 24.0),
              child: child,
            ),
          ),
        );
      }).whenComplete(whenCompleteAction ?? () {});
}
