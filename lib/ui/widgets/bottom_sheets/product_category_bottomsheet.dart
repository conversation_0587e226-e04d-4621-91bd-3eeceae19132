import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/models/category_response.dart';
import 'package:quick_retail_mobile/core/data/view_models/category_view_model.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottomsheet_header.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/empty_state.dart';
import 'package:quick_retail_mobile/ui/widgets/error_state.dart';
import 'package:quick_retail_mobile/ui/widgets/loadable_content_builder.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ProductCategoryBottomsheet extends ConsumerStatefulWidget {
  const ProductCategoryBottomsheet({
    super.key,
    required this.returningValue,
    this.categoryId,
    this.isSubCat = false,
  });

  final bool isSubCat;
  final int? categoryId;
  final ValueChanged<Category> returningValue;

  @override
  ConsumerState<ProductCategoryBottomsheet> createState() =>
      _ProductCategoryBottomsheetState();
}

class _ProductCategoryBottomsheetState
    extends ConsumerState<ProductCategoryBottomsheet> {
  _getCategories() async {
    await ref.read(categoryViewModel).fetchAllCategories();
  }

  _getSubCategories() async {
    await ref.read(categoryViewModel).fetchAllSubCatByCatgoryId(
          categoryId: widget.categoryId ?? 0,
        );
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    final catVm = ref.watch(categoryViewModel);
    return SizedBox(
      height: MediaQuery.of(context).size.height * 0.65,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BottomSheetHeader(
            title: widget.isSubCat ? "Sub-Category" : "Category",
            subTitle:
                "Select the  ${widget.isSubCat ? 'sub-' : ''}category of this product.",
          ),
          SizedBox(height: 16.h),
          const Divider(color: ColorPath.grey100),
          Expanded(
            child: LoadableContentBuilder(
              isBusy: widget.isSubCat
                  ? catVm.secondState == ViewState.busy
                  : catVm.state == ViewState.busy,
              isError: widget.isSubCat
                  ? catVm.secondState == ViewState.error
                  : catVm.state == ViewState.error,
              items:
                  widget.isSubCat ? catVm.subCatByCategoryId : catVm.categories,
              loadingBuilder: (context) {
                return ListView.separated(
                  padding: EdgeInsets.only(
                    top: 16.h,
                    bottom: 50.h,
                  ),
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return const Skeletonizer(
                      enabled: true,
                      child: Text("Category A Category A"),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(height: 24.h);
                  },
                  itemCount: 14,
                );
              },
              errorBuilder: (p0) {
                return SizedBox(
                  height: 175.h,
                  child: Center(
                    child: ErrorState(
                      message: widget.isSubCat
                          ? catVm.subCategoryMessage
                          : catVm.message,
                      onPressed: () {
                        if (widget.isSubCat) {
                          _getSubCategories();
                        } else {
                          _getCategories();
                        }
                      },
                    ),
                  ),
                );
              },
              emptyBuilder: (ctx) {
                return Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: Padding(
                    padding: const EdgeInsets.only(top: 24.0),
                    child: EmptyState(
                      imageAsset: Utilities.getSvg("noData"),
                      showCTA: false,
                      title: "No ${widget.isSubCat ? 'Sub-' : ''}Category",
                      subTitle:
                          "You currently have not recent ${widget.isSubCat ? 'sub-' : ''}category added yet",
                    ),
                  ),
                );
              },
              contentBuilder: (ctx) {
                return ListView.separated(
                  padding: EdgeInsets.only(
                    top: 16.h,
                    bottom: 50.h,
                  ),
                  itemBuilder: (ctx, i) {
                    Category order = widget.isSubCat
                        ? catVm.subCatByCategoryId[i]
                        : catVm.categories[i];
                    return Clickable(
                      onPressed: () {
                        widget.returningValue(order);
                        popNavigation(context: context);
                      },
                      child: Padding(
                        padding: EdgeInsets.symmetric(
                          vertical: 8.h,
                        ),
                        child:
                            Text(order.name ?? '', style: textTheme.bodyMedium),
                      ),
                    );
                  },
                  separatorBuilder: (ctx, i) {
                    return SizedBox(height: 16.h);
                  },
                  itemCount: widget.isSubCat
                      ? catVm.subCatByCategoryId.length
                      : catVm.categories.length,
                );
              },
            ),
          )
        ],
      ),
    );
  }
}
