import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/pages/pos/product_management/product_basic_information.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottomsheet_header.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';

class CreateProductBottomsheet extends StatelessWidget {
  CreateProductBottomsheet({super.key});

  final List items = [
    {
      "asset": "edit",
      "title": "Simple Product",
      "subtitle": "Create a simple product"
    },
    {
      "asset": "toggle",
      "title": "Variable Product",
      "subtitle": "Create variable product that has color, size etc"
    },
  ];
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const BottomSheetHeader(
          title: "Create a Product",
          subTitle: "Create a new product with options below.",
        ),
        SizedBox(
          height: 24.h,
        ),
        ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            padding: EdgeInsets.only(bottom: 32.h),
            itemBuilder: (context, index) {
              return Clickable(
                onPressed: () {
                  if (index == 0) {
                    // route to simple product flow
                    replaceNavigation(
                        context: context,
                        widget: const ProductBasicInformation(),
                        routeName: NamedRoutes.productBasicInfo);
                  } else {
                    // route to varialble product flow
                    replaceNavigation(
                        context: context,
                        widget:
                            const ProductBasicInformation(isVariation: true),
                        routeName: NamedRoutes.productBasicInfo);
                  }
                },
                child: Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                      border: Border.all(color: ColorPath.athensGrey),
                      borderRadius: BorderRadius.circular(8.r)),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(12.w),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.r),
                            color: ColorPath.flamingo.withOpacity(.2)),
                        child: SvgPicture.asset(
                            Utilities.getSvg(items[index]["asset"])),
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            items[index]["title"],
                            style: textTheme.bodyMedium
                                ?.copyWith(fontWeight: FontWeight.w500),
                          ),
                          SizedBox(
                            height: 2.h,
                          ),
                          Text(
                            items[index]["subtitle"],
                            style: textTheme.bodySmall
                                ?.copyWith(color: colorScheme.text7),
                          ),
                        ],
                      )
                    ],
                  ),
                ),
              );
            },
            separatorBuilder: (context, index) {
              return SizedBox(
                height: 16.h,
              );
            },
            itemCount: items.length)
      ],
    );
  }
}
