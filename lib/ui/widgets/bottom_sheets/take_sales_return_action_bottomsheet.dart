import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/data/models/quick_action_item.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottomsheet_header.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';

class TakeSalesReturnActionBottomsheet extends StatelessWidget {
  TakeSalesReturnActionBottomsheet({super.key});

  final List<QuickActionsItem> quickActions = [
    QuickActionsItem(
      actionName: "Credit Note ",
      subtitle: "Issue a credit note for sales return  ",
      assetName: "edit",
      onPressed: () {
        // bottomSheetWrapper(
        //     context: locator<NavigationService>().navigationKey.currentContext!,
        //     child: CustomBottomSheet(
        //       title: "Save and Update New Changes ?",
        //       subTitle:
        //           "Are you sure you want to Save and update new changes on this Simple product ? Kindly note that this new changes would override the old changes. ",
        //       firstButtonText: "Yes, Save and Update New Changes",
        //       secondButtonText: "No, Close",
        //       onPressedFirst: () {},
        //       onPressedSecond: () {
        //         popNavigation(
        //           context: locator<NavigationService>()
        //               .navigationKey
        //               .currentContext!,
        //         );
        //       },
        //     ));
      },
    ),
    QuickActionsItem(
      actionName: "Cash Back",
      subtitle: "Issue a Cashback.",
      assetName: "toggle",
      onPressed: () {},
    ),
    QuickActionsItem(
      actionName: "Reject return",
      subtitle: "Reject a goods return by a Customer",
      assetName: "toggle",
      onPressed: () {},
    ),
    // QuickActionsItem(
    //   actionName: "Delete Product",
    //   subtitle: "Remove product and it details permanently",
    //   assetName: "toggle",
    //   onPressed: () {},
    // ),
  ];

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const BottomSheetHeader(
          title: "Take action",
          subTitle: "Take action on a specific sales returns today",
        ),
        SizedBox(
          height: 24.h,
        ),
        ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              return Clickable(
                onPressed: quickActions[index].onPressed,
                child: Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                      border: Border.all(color: ColorPath.athensGrey),
                      borderRadius: BorderRadius.circular(8.r)),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(12.w),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.r),
                            color: ColorPath.flamingo.withOpacity(.2)),
                        child: SvgPicture.asset(
                            Utilities.getSvg(quickActions[index].assetName)),
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(quickActions[index].actionName),
                            Text(
                              quickActions[index].subtitle ?? '',
                              style: textTheme.bodySmall
                                  ?.copyWith(color: colorScheme.subTextPrimary),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              );
            },
            separatorBuilder: (context, index) {
              return SizedBox(
                height: 16.h,
              );
            },
            itemCount: quickActions.length),
        SizedBox(
          height: 24.h,
        ),
        CustomButton(
          onPressed: () {
            popNavigation(context: context);
          },
          borderColor: ColorPath.flamingo,
          bgColor: Colors.white,
          buttonTextColor: ColorPath.flamingo,
          buttonText: "No, Close",
        )
      ],
    );
  }
}
