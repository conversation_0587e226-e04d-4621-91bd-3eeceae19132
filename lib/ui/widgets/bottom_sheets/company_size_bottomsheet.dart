import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/data/view_models/auth_view_models/subscription_view_model.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottomsheet_header.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';

class CompanySizeBottomsheet extends ConsumerStatefulWidget {
  final ValueChanged<String> returningValue;

  const CompanySizeBottomsheet({super.key, required this.returningValue});

  @override
  ConsumerState<CompanySizeBottomsheet> createState() =>
      _CompanySizeBottomsheetState();
}

class _CompanySizeBottomsheetState
    extends ConsumerState<CompanySizeBottomsheet> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    final vm = ref.watch(subscriptionViewModel);
    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const BottomSheetHeader(
          title: "Company Size ",
          subTitle: "Select your company size",
        ),
        SizedBox(
          height: 24.h,
        ),
        ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              final value = vm.companySizes[index];
              final title =
                  Utilities.capitalizeWord(vm.companySizes[index].label ?? '');
              final desc = vm.companySizes[index].description ?? '';
              return Clickable(
                onPressed: () {
                  vm.selectedCompanySize = value;
                  widget.returningValue(title);
                  popNavigation(context: context);
                },
                child: Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                      border: Border.all(color:vm.selectedCompanySize == value ? ColorPath.flamingo : ColorPath.athensGrey),
                      borderRadius: BorderRadius.circular(8.r)),
                  child: Row(
                    children: [
                      Container(
                        padding: EdgeInsets.all(12.w),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.r),
                            color: ColorPath.flamingo.withOpacity(.2)),
                        child: SvgPicture.asset(Utilities.getSvg("edit")),
                      ),
                      SizedBox(
                        width: 10.w,
                      ),
                      Expanded(
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(title),
                            Text(
                              desc,
                              style: textTheme.bodySmall
                                  ?.copyWith(color: colorScheme.subTextPrimary),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                ),
              );
            },
            separatorBuilder: (context, index) {
              return SizedBox(
                height: 16.h,
              );
            },
            itemCount: vm.companySizes.length),
      ],
    );
  }
}
