import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/render_lottie.dart';

class CustomBottomSheet extends StatelessWidget {
  final String title;
  final String subTitle;
  final String firstButtonText;
  final String secondButtonText;
  final void Function()? onPressedFirst;
  final void Function()? onPressedSecond;
  final String? lottieAsset;
  final bool? showLoader;

  const CustomBottomSheet({
    super.key,
    this.title = '',
    this.subTitle = '',
    this.firstButtonText = '',
    this.secondButtonText = 'No, Close',
    this.lottieAsset,
    this.onPressedFirst,
    this.onPressedSecond,
    this.showLoader = false,
  });

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      mainAxisSize: MainAxisSize.min,
      // crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // const BottomSheetHeader(
        //   title: "Quick Action",
        //   subTitle: "Create, manage with one click",
        // ),
        SizedBox(
          height: 24.h,
        ),
        lottieAsset != null
            ? RenderLottie(
                lottieAsset: lottieAsset ?? "assets/lottie/caution.json",
                width: 80.w,
                height: 80.w,
              )
            : Container(
                height: 80,
                width: 80,
                decoration: const BoxDecoration(
                    shape: BoxShape.circle, color: ColorPath.mischkaGrey),
              ),
        SizedBox(
          height: 32.h,
        ),
        Text(
          title,
          style: textTheme.titleMedium,
        ),
        SizedBox(
          height: 8.h,
        ),
        Text(
          subTitle,
          textAlign: TextAlign.center,
          style: textTheme.bodyMedium?.copyWith(color: colorScheme.text7),
        ),
        SizedBox(
          height: 50.h,
        ),
        if (firstButtonText.isNotEmpty)
          CustomButton(
            showLoader: showLoader,
            onPressed: onPressedFirst,
            buttonText: firstButtonText,
          ),
        if (secondButtonText.isNotEmpty)
          Column(
            children: [
              SizedBox(
                height: 16.h,
              ),
              CustomButton(
                onPressed: onPressedSecond ??
                    () {
                      popNavigation(context: context);
                    },
                buttonText: secondButtonText,
                buttonTextColor: ColorPath.flamingo,
                borderColor: ColorPath.flamingo,
                bgColor: Colors.white,
              ),
            ],
          ),
        SizedBox(
          height: 40.h,
        ),
      ],
    );
  }
}
