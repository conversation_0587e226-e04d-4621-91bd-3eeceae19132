import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

class BottomSheetHeader extends StatelessWidget {
  const BottomSheetHeader(
      {super.key, this.title = "", this.subTitle = "", this.onPressed});

  final String title;
  final String subTitle;
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: ScreenTitle(title: title, subTitle: subTitle),
        ),
        Clickable(
            onPressed: onPressed ??
                () {
                  popNavigation(context: context);
                },
            child: SvgPicture.asset(Utilities.getSvg("closeBS")))
      ],
    );
  }
}