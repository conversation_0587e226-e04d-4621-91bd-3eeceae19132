import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/app_image.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';

class CategoryConfirmBottomsheet extends StatefulWidget {
  const CategoryConfirmBottomsheet({
    super.key,
  });

  @override
  State<CategoryConfirmBottomsheet> createState() =>
      _CategoryConfirmBottomsheetState();
}

class _CategoryConfirmBottomsheetState
    extends State<CategoryConfirmBottomsheet> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(height: 10.h),
        Image.asset(
          AppImage.exclamation,
          height: 80.h,
          width: 80.w,
        ),
        SizedBox(height: 40.h),
        Text(
          "Create a Sub-Category",
          style: textTheme.titleSmall?.copyWith(
            fontWeight: FontWeight.w600,
            fontSize: 18.sp,
            color: colorScheme.subTextSecondary,
          ),
        ),
        SizedBox(
          height: 4.h,
        ),
        Text(
          "Are you sure you want to create a sub-\ncategory?",
          textAlign: TextAlign.center,
          style: textTheme.bodySmall?.copyWith(
            fontSize: 14.sp,
            fontWeight: FontWeight.w400,
            color: ColorPath.grey500,
          ),
        ),
        SizedBox(height: 40.h),

        // Category list

        // Action buttons
        CustomButton(
          onPressed: () {},
          buttonText: "Yes, Create Sub-Category",
          bgColor: ColorPath.flamingo,
        ),

        SizedBox(height: 16.h),

        CustomButton(
          onPressed: () {
            popNavigation(context: context);
          },
          buttonText: "No, Close",
          bgColor: Colors.white,
          borderColor: ColorPath.flamingo,
          buttonTextColor: ColorPath.flamingo,
        ),
        SizedBox(height: 10.h),
      ],
    );
  }
}
