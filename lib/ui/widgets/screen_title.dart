import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';

class ScreenTitle extends StatelessWidget {
  final String title;
  final String subTitle;
  final double? titleSize;
  final double? subTitleSize;
  final Color? titleColor;
  final Color? subtitleColor;
  final FontWeight? titleFontWeight;
  final FontWeight? subtitleFontWeight;
  final Color? titleTagColor;
  final String titleTagText;
  final Color? titleTagTextColor;
  final CrossAxisAlignment? crossAxisAlignment;
  final MainAxisAlignment? mainAxisAlignment;

  const ScreenTitle(
      {super.key,
      this.titleFontWeight,
      this.subtitleFontWeight,
      this.titleColor,
      this.titleSize,
      this.subTitleSize,
      this.titleTagColor,
      this.titleTagTextColor,
      this.subtitleColor,
      this.titleTagText = '',
      this.crossAxisAlignment,
      this.mainAxisAlignment,
      required this.title,
      required this.subTitle});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Column(
      crossAxisAlignment: crossAxisAlignment ?? CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: mainAxisAlignment ?? MainAxisAlignment.start,
          children: [
            Text(
              title,
              style: textTheme.titleSmall?.copyWith(
                  fontWeight: titleFontWeight ?? FontWeight.w600,
                  fontSize: titleSize?.sp,
                  color: titleColor ?? colorScheme.subTextSecondary),
            ),
            if (titleTagColor != null)
              SizedBox(
                width: 6.w,
              ),
            if (titleTagColor != null)
              Container(
                padding: EdgeInsets.symmetric(vertical: 2.h, horizontal: 8.w),
                decoration: BoxDecoration(
                    color: titleTagColor,
                    borderRadius: BorderRadius.circular(12.r)),
                child: Text(
                  titleTagText,
                  style: textTheme.bodySmall?.copyWith(
                      fontWeight: titleFontWeight ?? FontWeight.w600,
                      fontSize: titleSize?.sp,
                      color: titleTagTextColor ?? colorScheme.text6),
                ),
              )
          ],
        ),
        SizedBox(
          height: 4.h,
        ),
        Text(
          subTitle,
          style: textTheme.bodySmall?.copyWith(
              fontSize: subTitleSize?.sp,
              fontWeight: subtitleFontWeight ?? FontWeight.w400,
              color: subtitleColor ?? colorScheme.subTextSecondary),
        ),
      ],
    );
  }
}
