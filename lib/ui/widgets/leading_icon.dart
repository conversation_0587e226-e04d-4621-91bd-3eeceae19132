import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';




class LeadingIcon extends StatelessWidget {
  final VoidCallback? onPressed;
  final bool addPadding;
  const LeadingIcon({super.key, this.onPressed, this.addPadding = true});

  @override
  Widget build(BuildContext context) {
    final ModalRoute<dynamic>? parentRoute = ModalRoute.of(context);
    final bool canPop = parentRoute?.canPop ?? false;
    if(canPop){
      return GestureDetector(
        onTap:onPressed ?? (){

          Navigator.pop(context);
        },
        child: Padding(
          padding: EdgeInsets.only(left: addPadding ? 16.w : 0),
          child: SizedBox(
            height: 50.h,
            width: 30.w,
            child: Align(
                alignment: Alignment.centerLeft,
                child: Container(
                  height: 24.h,
                  width: 24.w,
                  decoration: const BoxDecoration(
                      color: ColorPath.athensGrey,
                      shape: BoxShape.circle
                  ),
                  child: Center(
                    child: Icon(Icons.arrow_back_ios_new, color: ColorPath.mirageBlack, size: 13.w,),
                  ),
                )
            ),
          ),
        ),
      );
    }
      return Container();
  }
}
