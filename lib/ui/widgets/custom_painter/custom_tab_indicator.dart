import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class CustomTabIndicator extends Decoration {
  final Color bgColor;
  final double radius;
  const CustomTabIndicator({required this.bgColor, this.radius = 3});
  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _CustomTabIndicatorPainter(bgColor: bgColor, radius: radius);
  }
}

class _CustomTabIndicatorPainter extends BoxPainter {
  final Color bgColor;
   final double radius;
  _CustomTabIndicatorPainter({required this.bgColor, required this.radius});
  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final Paint paint = Paint();
    paint.color = bgColor;
    paint.style = PaintingStyle.fill;

    final Rect rect = Offset(offset.dx, offset.dy) & Size(configuration.size!.width, configuration.size!.height);
    final RRect rRect = RRect.fromRectAndRadius(rect, Radius.circular(radius.r));
    canvas.drawRRect(rRect, paint);
  }
}