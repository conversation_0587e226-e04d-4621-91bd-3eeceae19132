import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/data/view_models/auth_view_models/auth_view_model.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';

class InAppDisplayImage extends StatelessWidget {
  final String? tag;
  final double? size;
  final bool useGradient;
  const InAppDisplayImage(
      {super.key, this.tag, this.size, this.useGradient = false});

  @override
  Widget build(BuildContext context) {
    return Consumer(
      builder: (context, ref, child) {
        // final profileVm = ref.watch(profileViewModel);
        final authVm = ref.watch(authViewModel);
        const image = ""; //profileVm.profilePhoto;
        final hasImage = image.isNotEmpty;
        final firstName = authVm.user?.firstname; //profileVm.firstname;
        final lastName = authVm.user?.lastname; //profileVm.firstname;
        return Container(
          padding: EdgeInsets.all(2.w),
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: useGradient
                ? const LinearGradient(
                    colors: [
                      ColorPath.pearYellow,
                      ColorPath.manzYellow,
                      ColorPath.danubeBlue,
                      ColorPath.hazeBlue,
                    ],
                    begin: Alignment.centerLeft, //Alignment.topLeft
                    end: Alignment.centerRight, //Alignment.bottomRight
                  )
                : null,
          ),
          child: Container(
            padding: EdgeInsets.all(1.5.w),
            decoration: const BoxDecoration(
                color: Colors.white, shape: BoxShape.circle),
            child: hasImage
                ? Container(
                    height: size?.h ?? 40.h,
                    width: size?.w ?? 40.w,
                    decoration: const BoxDecoration(
                        color: Colors.white, shape: BoxShape.circle),
                    child: CircleAvatar(
                      backgroundColor: ColorPath.athensGrey,
                      radius: size?.r ?? 40.r,
                      child: CachedNetworkImage(
                        imageUrl: image,
                        imageBuilder: (context, imageProvider) => CircleAvatar(
                          backgroundImage: imageProvider,
                          radius: size?.r ?? 40.r,
                        ),
                        placeholder: (context, url) => Container(),
                        errorWidget: (context, url, error) => Center(
                            child: Text(
                          Utilities.getNameInitials(
                              firstName: firstName, lastName: lastName),
                          style: Theme.of(context)
                              .textTheme
                              .titleMedium
                              ?.copyWith(
                                  fontWeight: FontWeight.w400,
                                  color: ColorPath.flamingo),
                        )), // Or any widget for errors
                      ),
                    ),
                  )
                : Container(
                    height: 40.h,
                    width: 40.w,
                    decoration: const BoxDecoration(
                        color: Colors.white, shape: BoxShape.circle),
                    child: CircleAvatar(
                      backgroundColor: ColorPath.athensGrey,
                      radius: 40.r,
                      child: Center(
                        child: Text(
                          Utilities.getNameInitials(
                              firstName: firstName, lastName: lastName),
                          style: Theme.of(context)
                              .textTheme
                              .titleSmall
                              ?.copyWith(
                                  fontWeight: FontWeight.w400,
                                  color: ColorPath.rockBlack),
                        ),
                      ),
                    ),
                  ),
          ),
        );
      },
    );
  }
}
