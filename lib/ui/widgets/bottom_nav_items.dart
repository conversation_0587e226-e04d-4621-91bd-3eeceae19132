import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';

///bottom nav items
bottomNavItems(BuildContext context) {
  return [
    BottomNavigationBarItem(
      activeIcon: SvgPicture.asset(
        Utilities.getSvg("overview"),
        height: 22.h,
        width: 22.w,
        colorFilter: const ColorFilter.mode(
          ColorPath.flamingo,
          BlendMode.srcIn,
        ),
      ),
      icon: SvgPicture.asset(
        Utilities.getSvg("overview"),
        height: 22.h,
        width: 22.w,
        colorFilter: const ColorFilter.mode(
          ColorPath.paleGrey,
          BlendMode.srcIn,
        ),
      ),
      label: 'Overview',
    ),
    BottomNavigationBarItem(
      activeIcon: SvgPicture.asset(
        Utilities.getSvg("pos"),
        height: 22.h,
        width: 22.w,
        colorFilter: const ColorFilter.mode(
          ColorPath.flamingo,
          BlendMode.srcIn,
        ),
      ),
      icon: SvgPicture.asset(
        Utilities.getSvg("pos"),
        color: ColorPath.paleGrey,
        height: 22.h,
        width: 22.w,
      ),
      label: 'POS',
    ),
    BottomNavigationBarItem(
      activeIcon: SvgPicture.asset(
        Utilities.getSvg("finance"),
        height: 22.h,
        width: 22.w,
        colorFilter: const ColorFilter.mode(
          ColorPath.flamingo,
          BlendMode.srcIn,
        ),
      ),
      icon: SvgPicture.asset(
        Utilities.getSvg("finance"),
        height: 22.h,
        width: 22.w,
      ),
      label: 'Finance',
    ),
    BottomNavigationBarItem(
      activeIcon: SvgPicture.asset(
        Utilities.getSvg("procurement"),
        height: 22.h,
        width: 22.w,
        colorFilter: const ColorFilter.mode(
          ColorPath.flamingo,
          BlendMode.srcIn,
        ),
      ),
      icon: SvgPicture.asset(
        Utilities.getSvg("procurement"),
        height: 22.h,
        width: 22.w,
      ),
      label: 'Procurement',
    ),
    BottomNavigationBarItem(
      activeIcon: SvgPicture.asset(
        Utilities.getSvg("asset"),
        height: 22.h,
        width: 22.w,
        colorFilter: const ColorFilter.mode(
          ColorPath.flamingo,
          BlendMode.srcIn,
        ),
      ),
      icon: SvgPicture.asset(
        Utilities.getSvg("asset"),
        height: 22.h,
        width: 22.w,
      ),
      label: 'Asset',
    ),
  ];
}
