import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';

class CustomIcon extends StatelessWidget {
  final VoidCallback? onPressed;
  final Color? bgColor;
  final String imageAsset;
  final double? height;
  final double? width;
  final double radius;
  final bool isCircular;
  const CustomIcon(
      {super.key,
      this.isCircular = false,
      this.onPressed,
      this.bgColor,
      required this.imageAsset,
      this.height,
      this.width,
      this.radius = 8});

  @override
  Widget build(BuildContext context) {
    if (isCircular) {
      return Clickable(
        onPressed: onPressed,
        child: Container(
          padding: EdgeInsets.symmetric(horizontal: 10.w, vertical: 10.h),
          decoration: BoxDecoration(
              color: bgColor ?? ColorPath.flamingo.withOpacity(.2),
              shape: BoxShape.circle),
          child: SvgPicture.asset(
            imageAsset,
            height: height?.h ?? 20.h,
            width: width?.w ?? 20.w,
          ),
        ),
      );
    }
    return Clickable(
      onPressed: onPressed,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 8.h),
        decoration: BoxDecoration(
            color: bgColor ?? ColorPath.flamingo.withOpacity(.2),
            borderRadius: BorderRadius.all(Radius.circular(radius.r))),
        child: SvgPicture.asset(
          imageAsset,
          height: height?.h ?? 16.h,
          width: width?.w ?? 16.w,
        ),
      ),
    );
  }
}
