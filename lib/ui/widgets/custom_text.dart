import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';



class CustomText extends StatelessWidget {
  final String text;
  final TextAlign? textAlign;
  final int? maxLines;
  final TextOverflow? overflow;
  final double fontSize;
  final Color fontColor;
  final FontWeight? fontWeight;
  final Color? decorationColor;
  final TextDecoration? textDecoration;
  final  TextBaseline? textBaseline;
  final String? fontFamily;
  final FontStyle? fontStyle;

  const CustomText({
    super.key,
    required this.text,
    this.textAlign = TextAlign.left,
    this.maxLines,
    this.overflow,
    this.fontSize = 16,
    this.fontColor = Colors.black,
    this.fontWeight = FontWeight.w400,
    this.decorationColor,
    this.textDecoration,
    this.textBaseline,
    this.fontFamily,
    this.fontStyle
  });

  @override
  Widget build(BuildContext context) {
    return Text(
      text,
      style: TextStyle(
        color: fontColor,
        fontFamily: fontFamily,
        fontSize: fontSize.sp,
        fontWeight: fontWeight,
        decoration: textDecoration,
        decorationColor: decorationColor,
        height: Platform.isIOS ? 0 : 1.5,
        textBaseline: textBaseline,
        fontStyle: fontStyle
      ),
      textAlign: textAlign,
      maxLines: maxLines,
      overflow: overflow,

    );
  }
}