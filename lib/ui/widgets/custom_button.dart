import 'dart:ui';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';

class CustomButton extends StatelessWidget {
  final String buttonText;
  final double? buttonWidth;
  final double? buttonHeight;
  final double buttonTextSize;
  final VoidCallback? onPressed;
  final double buttonHorizontalPadding;
  final Color bgColor;
  final Color? borderColor;
  final Color buttonTextColor;
  final FontWeight buttonTextFontWeight;
  final Color? disableBgColor;
  final bool? showLoader;
  final Widget? childWidget;
  final Color? loaderColor;
  const CustomButton(
      {super.key,
      this.buttonWidth = double.infinity,
      this.buttonTextFontWeight = FontWeight.w600,
      this.buttonTextSize = 16,
      this.borderColor,
      this.bgColor = ColorPath.flamingo,
      this.buttonTextColor = Colors.white,
      required this.onPressed,
      this.buttonText = 'Continue',
      this.buttonHeight,
      this.disableBgColor,
      this.showLoader = false,
      this.loaderColor,
      this.childWidget,
      this.buttonHorizontalPadding = 24});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: buttonHeight?.h ?? 60.h,
      width: buttonWidth,
      child: ElevatedButton(
          onPressed: onPressed,
          style: ElevatedButton.styleFrom(
            side: borderColor == null
                ? null
                : BorderSide(color: borderColor!, width: 1.w),
            padding: EdgeInsets.symmetric(
              horizontal: buttonHorizontalPadding.w,
            ),
            elevation: 0,
            backgroundColor: borderColor == null ? bgColor : Colors.transparent,
            disabledBackgroundColor: disableBgColor ?? bgColor.withOpacity(0.5),
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8.r),
            ),
          ),
          child: showLoader ?? false
              ? Container(
                  padding: EdgeInsets.all(6.w),
                  height: 30.h,
                  width: 30.h,
                  child: CircularProgressIndicator(
                      color: loaderColor ?? Colors.white))
              : childWidget ??
                  FittedBox(
                      child: Text(
                    buttonText,
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        color: buttonTextColor,
                        fontWeight: buttonTextFontWeight,
                        fontSize: buttonTextSize.sp),
                  )) // Button's label
          ),
    );
  }
}
