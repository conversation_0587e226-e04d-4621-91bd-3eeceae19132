import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';

class LgbtqContainer extends StatelessWidget {
  final bool showPadding;
  final bool useMargin;
  final Widget child;
  const LgbtqContainer(
      {super.key,
      required this.child,
      this.showPadding = true,
      this.useMargin = true});

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.only(right: useMargin ? 16.w : 0),
      padding: EdgeInsets.symmetric(
          vertical: showPadding ? 3.h : 0, horizontal: showPadding ? 3.w : 0),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.all(Radius.circular(8.r)),
        gradient: const LinearGradient(
          colors: [
            ColorPath.flamingo,
            ColorPath.flamingoRed,
          ],
          begin: Alignment.centerLeft,
          end: Alignment.centerRight,
        ),
      ),
      child: child,
    );
  }
}
