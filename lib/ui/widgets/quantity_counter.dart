import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';

class QuantityCounter extends StatelessWidget {
  final int lowerLimit, upperLimit, stepValue, value;
  final ValueChanged<dynamic> onChanged;
  const QuantityCounter(
      {super.key,
      this.value = 1,
      required this.onChanged,
      this.lowerLimit = 1,
      this.upperLimit = 1000000,
      this.stepValue = 1});

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.center,
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Clickable(
          onPressed: () {
            if (value == 1) {
              return;
            }
            onChanged(value - stepValue);
          },
          child: Opacity(
            opacity: value == 1 ? 0.4 : 1,
            child: Container(
              height: 32.h,
              width: 32.w,
              decoration: BoxDecoration(
                  color: ColorPath.lavenderPink,
                  shape: BoxShape.circle,
                  border: Border.all(color: ColorPath.pastelPink, width: 1.w)),
              child: Center(
                child: SvgPicture.asset(
                  Utilities.getSvg("minusCounter"),
                  height: 11.64.h,
                  width: 11.64.w,
                  colorFilter: const ColorFilter.mode(
                    ColorPath.amaranthRed,
                    BlendMode.srcIn,
                  ),
                ),
              ),
            ),
          ),
        ),
        SizedBox(
          width: 16.w,
        ),
        Container(
          height: 36.w,
          width: 36.w,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.all(Radius.circular(8.r)),
              border: Border.all(color: ColorPath.mischkaGrey, width: 1.w)),
          child: Center(
            child: FittedBox(
              child: Text(
                value.toString(),
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: Theme.of(context).colorScheme.text6),
              ),
            ),
          ),
        ),
        SizedBox(
          width: 16.w,
        ),
        Clickable(
          onPressed: () {
            if (value == upperLimit) {
              return;
            }
            debugPrint('value:::$value .....upper limit:::$upperLimit>>>>');
            onChanged(value + stepValue);
          },
          child: Opacity(
            opacity: value == upperLimit ? 0.4 : 1,
            child: Container(
              height: 32.h,
              width: 32.w,
              decoration: BoxDecoration(
                  color: ColorPath.lilacGrey,
                  shape: BoxShape.circle,
                  border:
                      Border.all(color: ColorPath.periwinkleGrey, width: 1.w)),
              child: Center(
                child: SvgPicture.asset(
                  Utilities.getSvg("addCounter"),
                  height: 11.64.h,
                  width: 11.64.w,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
