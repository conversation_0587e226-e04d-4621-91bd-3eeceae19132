import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';

class EmptyState extends StatelessWidget {
  final String imageAsset;
  final String? title;
  final String subTitle;
  final bool? showCTA;
  final String? buttonText;
  final VoidCallback? onPressed;
  final double? height;
  final double? width;
  const EmptyState(
      {super.key,
      this.height,
      this.width,
      required this.imageAsset,
      this.title,
      required this.subTitle,
      this.buttonText = '',
      this.onPressed,
      this.showCTA = true});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Padding(
      padding: EdgeInsets.symmetric(horizontal: 16.w),
      child: Column(
        children: [
          SvgPicture.asset(
            imageAsset,
            height: height ?? 250.h,
            width: width ?? 250.w,
          ),
          if (title != null)
            Column(
              children: [
                SizedBox(
                  height: 16.h,
                ),
                Text(
                  title ?? '',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: Theme.of(context).colorScheme.textPrimary),
                  textAlign: TextAlign.center,
                ),
              ],
            ),
          SizedBox(height: 7.h),
          Text(
            subTitle ?? '',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                fontWeight: FontWeight.w400,
                color: Theme.of(context).colorScheme.subTextPrimary),
            textAlign: TextAlign.center,
          ),
          SizedBox(height: 8.h),
          if (showCTA ?? false)
            Clickable(
              onPressed: onPressed,
              child: Container(
                padding: EdgeInsets.symmetric(horizontal: 24.w, vertical: 12.h),
                decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(color: ColorPath.athensGrey)),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Text(
                      buttonText ?? "",
                      style: textTheme.bodyMedium?.copyWith(
                          color: colorScheme.subTextSecondary,
                          fontWeight: FontWeight.w500),
                    ),
                    SizedBox(
                      width: 12.w,
                    ),
                    SvgPicture.asset(Utilities.getSvg("add"))
                  ],
                ),
              ),
            )
        ],
      ),
    );
  }
}
