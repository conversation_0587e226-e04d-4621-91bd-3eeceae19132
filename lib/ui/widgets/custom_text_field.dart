import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';

class CustomTextField extends StatefulWidget {
  final String label;
  final double labelSize;
  final FontWeight labelFontWeight;
  final Color? labelColor;
  final TextEditingController? controller;
  final ValueChanged<String>? onChanged;
  final String? Function(String?)? validator;
  final List<TextInputFormatter>? inputFormatters;
  final TextInputType? keyboardType;
  final double textSize;
  final Color textColor;
  final bool obscure;
  final Widget? suffixIcon;
  final Widget? prefixIcon;
  final String hintText;
  final String bottomHintText;
  final double hintSize;
  final Color? hintColor;
  final bool enabled;
  final bool readOnly;
  final bool isCompulsory;
  final bool hideBorder;
  final FocusNode? focusPointer;
  final int maxLines;
  final bool isMoneyValue;
  final Color? bgColor;
  final bool showLabel;
  final double? borderRadius, height;

  const CustomTextField(
      {super.key,
      this.label = '',
      this.labelSize = 14,
      this.labelFontWeight = FontWeight.w500,
      this.labelColor,
      this.controller,
      this.onChanged,
      this.validator,
      this.inputFormatters,
      this.keyboardType = TextInputType.text,
      this.textSize = 16,
      this.textColor = Colors.black,
      this.obscure = false,
      this.suffixIcon,
      this.hintText = '',
      this.hintSize = 16,
      this.hintColor,
      this.enabled = true,
      this.readOnly = false,
      this.prefixIcon,
      this.bottomHintText = '',
      this.isCompulsory = true,
      this.hideBorder = false,
      this.focusPointer,
      this.maxLines = 1,
      this.isMoneyValue = false,
      this.bgColor,
      this.showLabel = true,
      this.borderRadius,
      this.height});

  @override
  State<CustomTextField> createState() => _CustomTextFieldState();
}

class _CustomTextFieldState extends State<CustomTextField> {
  //final FocusNode _focusNode = FocusNode();
  //bool _isActive = false;

  @override
  void dispose() {
    //_focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    //  if(widget.focusPointer == null){
    //    return Focus(
    //      focusNode: _focusNode,
    //      onFocusChange: (hasFocus) {
    //        if (hasFocus) {
    //          // TextFormField is now focused
    //          setState(() {
    //            _isActive = true;
    //          });
    //        } else {
    //          // TextFormField lost focus
    //          if(widget.controller == null || widget.controller!.text.isEmpty){
    //            setState(() {
    //              _isActive = false;
    //            });
    //          }
    //        }
    //      },
    //      child: Column(
    //        crossAxisAlignment: CrossAxisAlignment.start,
    //        children: [
    //          Container(
    //            width: double.infinity,
    //            decoration: BoxDecoration(
    //                color: widget.bgColor ?? ColorPath.athensGrey4,
    //                borderRadius: BorderRadius.only(
    //                    topLeft: Radius.circular(4.r),
    //                    topRight: Radius.circular(4.r)
    //                )
    //            ),
    //            child: TextFormField(
    //                autovalidateMode: AutovalidateMode.onUserInteraction,
    //                enabled: widget.enabled,
    //                readOnly: widget.readOnly,
    //                validator: widget.validator,
    //                controller: widget.controller,
    //                obscureText: widget.obscure,
    //                style: TextStyle(fontSize: widget.textSize.sp,
    //                    color: widget.textColor,
    //                  fontFamily: widget.isMoneyValue && Platform.isAndroid ? '':null,
    //                ),
    //                onChanged: widget.onChanged,
    //                keyboardType: widget.keyboardType,
    //                inputFormatters: widget.inputFormatters,
    //                maxLines: widget.maxLines,
    //                decoration: InputDecoration(
    //                  label: Row(
    //                    children: [
    //                      CustomText(
    //                        text: widget.label,
    //                        fontSize: widget.labelSize,
    //                        fontWeight: widget.labelFontWeight,
    //                        fontColor: _isActive? widget.labelColor : widget.labelColor.withOpacity(0.6),
    //                      ),
    //                      if(widget.isCompulsory)SizedBox(width: 5.w,),
    //                      if(widget.isCompulsory)CustomText(
    //                        text: '*',
    //                        fontSize: widget.labelSize,
    //                        fontWeight: FontWeight.w500,
    //                        fontColor: _isActive ? ColorPath.alizarinRed : ColorPath.alizarinRed.withOpacity(0.3),
    //                      ),
    //                    ],
    //                  ),
    //                  errorMaxLines: 3,
    //                  hintText: widget.hintText,
    //                  hintStyle: TextStyle(fontSize: widget.hintSize.sp, color: widget.hintColor.withOpacity(0.7), height: 0),
    //                  suffixIcon: widget.suffixIcon,
    //                  suffixIconConstraints: BoxConstraints(
    //                    minWidth: 25.w,
    //                    minHeight: 25.h,
    //                  ),
    //                  prefixIcon: Opacity(
    //                      opacity: _isActive ? 1.0 : 0.3,
    //                      child: widget.prefixIcon),
    //                  prefixIconConstraints: BoxConstraints(
    //                    minWidth: 25.w,
    //                    minHeight: 25.h,
    //                  ),
    //                  floatingLabelBehavior: FloatingLabelBehavior.always,
    //                  contentPadding: EdgeInsets.only(left: 16.w, right: 16.w, top: 9.h, bottom: 9.h),
    //                  focusedBorder: UnderlineInputBorder(
    //                      borderSide: BorderSide(color: ColorPath.gullGrey, width: 1.w)),
    //                  disabledBorder: UnderlineInputBorder(
    //                      borderSide: BorderSide(color: ColorPath.gullGrey, width: 1.w)),
    //                  border: UnderlineInputBorder(
    //                      borderSide: BorderSide(color: ColorPath.gullGrey, width: 1.w)),
    //                )),
    //          ),
    //          if(widget.bottomHintText.isNotEmpty)SizedBox(height: 4.h,),
    //          if(widget.bottomHintText.isNotEmpty)Padding(
    //            padding: EdgeInsets.only(left: 16.w),
    //            child: CustomText(
    //              text: widget.bottomHintText,
    //              fontSize: 10,
    //              fontWeight: FontWeight.w400,
    //              fontColor: _isActive ? ColorPath.shipGrey : ColorPath.shipGrey.withOpacity(0.3),
    //              textAlign: TextAlign.left,
    //            ),
    //          ),
    //        ],
    //      ),
    //    );
    //  }
    //
    //
    //  return Focus(
    //    focusNode: widget.focusPointer,
    //    onFocusChange: (hasFocus) {
    //      if (hasFocus) {
    //        setState(() {
    //          _isActive = true;
    //        });
    //      } else {
    //        if(widget.controller == null || widget.controller!.text.isEmpty){
    //          setState(() {
    //            _isActive = false;
    //          });
    //        }
    //      }
    //    },
    //    child: Column(
    //      crossAxisAlignment: CrossAxisAlignment.start,
    //      children: [
    //        Container(
    //          width: double.infinity,
    //          decoration: BoxDecoration(
    //              color: ColorPath.athensGrey2,
    //              borderRadius: BorderRadius.only(
    //                  topLeft: Radius.circular(4.r),
    //                  topRight: Radius.circular(4.r)
    //              )
    //          ),
    //          child: TextFormField(
    //              autovalidateMode: AutovalidateMode.onUserInteraction,
    //              enabled: widget.enabled,
    //              readOnly: widget.readOnly,
    //              validator: widget.validator,
    //              controller: widget.controller,
    //              obscureText: widget.obscure,
    //              style: TextStyle(fontSize: widget.textSize.sp, color: widget.textColor, fontFamily: widget.isMoneyValue && Platform.isAndroid ? '':null),
    //              onChanged: widget.onChanged,
    //              keyboardType: widget.keyboardType,
    //              inputFormatters: widget.inputFormatters,
    //              decoration: InputDecoration(
    //
    //                label: Row(
    //                  children: [
    //                    CustomText(
    //                      text: widget.label,
    //                      fontSize: widget.labelSize,
    //                      fontWeight: widget.labelFontWeight,
    //                      fontColor: _isActive? widget.labelColor : widget.labelColor.withOpacity(0.3),
    //                    ),
    //                    if(widget.isCompulsory)SizedBox(width: 5.w,),
    //                    if(widget.isCompulsory)CustomText(
    //                      text: '*',
    //                      fontSize: widget.labelSize,
    //                      fontWeight: FontWeight.w500,
    //                      fontColor: _isActive ? ColorPath.alizarinRed : ColorPath.alizarinRed.withOpacity(0.3),
    //                    ),
    //                  ],
    //                ),
    //                errorMaxLines: 3,
    //                hintText: widget.hintText,
    //                hintStyle: TextStyle(fontSize: widget.hintSize.sp, color: widget.hintColor.withOpacity(0.3), height: 0),
    //                suffixIcon: widget.suffixIcon,
    //                suffixIconConstraints: BoxConstraints(
    //                  minWidth: 25.w,
    //                  minHeight: 25.h,
    //                ),
    //                prefixIcon: Opacity(
    //                    opacity: _isActive ? 1.0 : 0.3,
    //                    child: widget.prefixIcon),
    //                prefixIconConstraints: BoxConstraints(
    //                  minWidth: 25.w,
    //                  minHeight: 25.h,
    //                ),
    //                floatingLabelBehavior: FloatingLabelBehavior.always,
    //                contentPadding: EdgeInsets.only(left: 16.w, right: 16.w, top: 9.h, bottom: 9.h),
    //                focusedBorder: UnderlineInputBorder(
    //                    borderSide: BorderSide(color: ColorPath.gullGrey, width: 1.w)),
    //                disabledBorder: UnderlineInputBorder(
    //                    borderSide: BorderSide(color: ColorPath.gullGrey, width: 1.w)),
    //                border: UnderlineInputBorder(
    //                    borderSide: BorderSide(color: ColorPath.gullGrey, width: 1.w)),
    //              )),
    //        ),
    //        if(widget.bottomHintText.isNotEmpty)SizedBox(height: 4.h,),
    //        if(widget.bottomHintText.isNotEmpty)Padding(
    //          padding: EdgeInsets.only(left: 16.w),
    //          child: CustomText(
    //            text: widget.bottomHintText,
    //            fontSize: 14,
    //            fontWeight: FontWeight.w400,
    //            fontColor: _isActive ? ColorPath.shipGrey : ColorPath.shipGrey.withOpacity(0.3),
    //            textAlign: TextAlign.left,
    //          ),
    //        ),
    //      ],
    //    ),
    //  );
    //
    // }

    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.showLabel)
          Row(
            children: [
              Text(
                widget.label,
                style: textTheme.bodyMedium?.copyWith(
                    color: widget.labelColor ?? colorScheme.subTextSecondary,
                    fontSize: widget.labelSize,
                    fontWeight: widget.labelFontWeight),
              ),
              if (widget.isCompulsory)
                SizedBox(
                  width: 5.w,
                ),
              if (widget.isCompulsory)
                Text(
                  '*',
                  style: textTheme.bodyMedium?.copyWith(
                      color: ColorPath.flamingoRed,
                      fontSize: widget.labelSize,
                      fontWeight: FontWeight.w500),
                ),
            ],
          ),
        if (widget.showLabel)
          SizedBox(
            height: 6.h,
          ),
        Container(
          height: widget.maxLines > 1 ? null : (widget.height ?? 58.h),
          width: double.infinity,
          decoration: BoxDecoration(
              //color: ColorPath.athensGrey2,
              color: widget.bgColor,
              //border: Border.all(color: ColorPath.mischkaGrey, width: 1.w),
              borderRadius: BorderRadius.all(
                  Radius.circular(widget.borderRadius?.r ?? 8.r))
              // borderRadius: BorderRadius.only(
              //     topLeft: Radius.circular(8.r),
              //     topRight: Radius.circular(8.r)
              // )
              ),
          child: Center(
            child: TextFormField(
                maxLines: widget.maxLines,
                autovalidateMode: AutovalidateMode.onUserInteraction,
                enabled: widget.enabled,
                readOnly: widget.readOnly,
                validator: widget.validator,
                controller: widget.controller,
                focusNode: widget.focusPointer,
                obscureText: widget.obscure,
                style: TextStyle(
                    fontSize: widget.textSize.sp,
                    color: widget.textColor,
                    fontFamily:
                        widget.isMoneyValue && Platform.isAndroid ? '' : null),
                onChanged: widget.onChanged,
                keyboardType: widget.keyboardType,
                inputFormatters: widget.inputFormatters,
                decoration: InputDecoration(
                  isDense: false,
                  errorMaxLines: 3,
                  hintText: widget.hintText,
                  hintStyle: textTheme.bodyLarge?.copyWith(
                      color: widget.hintColor?.withOpacity(0.3) ??
                          colorScheme.subTextPrimary,
                      fontSize: widget.labelSize,
                      fontWeight: FontWeight.w500),
                  suffixIcon: widget.suffixIcon,
                  suffixIconConstraints: BoxConstraints(
                    minWidth: 25.w,
                    minHeight: 25.h,
                  ),
                  prefixIcon: widget.prefixIcon,
                  prefixIconConstraints: BoxConstraints(
                    minWidth: 25.w,
                    minHeight: 25.h,
                  ),
                  floatingLabelBehavior: FloatingLabelBehavior.always,
                  contentPadding: EdgeInsets.only(
                    top: widget.maxLines > 1 ? 12 : 0,
                    left: 16.w,
                    right: 16.w,
                  ),
                  enabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                        color: widget.hideBorder
                            ? Colors.transparent
                            : ColorPath.mischkaGrey,
                        width: 1.w),
                    borderRadius:
                        BorderRadius.circular(widget.borderRadius?.r ?? 12.r),
                  ),
                  focusedBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                        color: widget.hideBorder
                            ? Colors.transparent
                            : ColorPath.mischkaGrey,
                        width: 1.w),
                    borderRadius:
                        BorderRadius.circular(widget.borderRadius?.r ?? 8.r),
                  ),
                  disabledBorder: OutlineInputBorder(
                    borderSide: BorderSide(
                        color: widget.hideBorder
                            ? Colors.transparent
                            : ColorPath.mischkaGrey,
                        width: 1.w),
                    borderRadius:
                        BorderRadius.circular(widget.borderRadius?.r ?? 8.r),
                  ),
                  border: OutlineInputBorder(
                    borderSide: BorderSide(
                      color: widget.hideBorder
                          ? Colors.transparent
                          : ColorPath.mischkaGrey,
                      width: 1.w,
                    ),
                    borderRadius:
                        BorderRadius.circular(widget.borderRadius?.r ?? 8.r),
                  ),
                  // errorBorder: OutlineInputBorder(
                  //     borderSide: BorderSide(color: Colors.transparent, width: 1.w)),
                  // focusedErrorBorder: OutlineInputBorder(
                  //     borderSide: BorderSide(color: Colors.red, width: 1.w)),
                )),
          ),
        ),
        if (widget.bottomHintText.isNotEmpty)
          SizedBox(
            height: 4.h,
          ),
        if (widget.bottomHintText.isNotEmpty)
          Text(
            widget.bottomHintText,
            style: textTheme.bodySmall?.copyWith(
                color: colorScheme.subTextPrimary, fontWeight: FontWeight.w400),
            textAlign: TextAlign.left,
          ),
      ],
    );
  }
}

// for chat field
class AutoExpandingTextField extends StatefulWidget {
  final Function(String)? onSubmitted;
  final void Function(String)? onChanged;
  final TextEditingController? controller;
  final String? hintText;
  final double maxHeight;

  const AutoExpandingTextField({
    super.key,
    this.onSubmitted,
    this.onChanged,
    this.controller,
    this.hintText = 'Write a message...',
    this.maxHeight = 120.0,
  });

  @override
  State<AutoExpandingTextField> createState() => _AutoExpandingTextFieldState();
}

class _AutoExpandingTextFieldState extends State<AutoExpandingTextField> {
  late TextEditingController _controller;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    }
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return Container(
      constraints: BoxConstraints(
        maxHeight: widget.maxHeight,
      ),
      child: TextField(
        controller: _controller,
        maxLines: null,
        onChanged: widget.onChanged,
        textCapitalization: TextCapitalization.sentences,
        keyboardType: TextInputType.text,
        decoration: InputDecoration(
          hintText: widget.hintText,
          hintStyle: textTheme.bodyLarge?.copyWith(
              color: colorScheme.subTextPrimary,
              fontSize: 12.sp,
              fontWeight: FontWeight.w500),
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16.0,
            vertical: 8.0,
          ),
          border: OutlineInputBorder(
            borderSide: BorderSide(
              color: ColorPath.mischkaGrey,
              width: 1.w,
            ),
            borderRadius: BorderRadius.circular(16.r),
          ),
          enabledBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: ColorPath.mischkaGrey,
              width: 1.w,
            ),
            borderRadius: BorderRadius.circular(16.r),
          ),
          focusedBorder: OutlineInputBorder(
            borderSide: BorderSide(
              color: ColorPath.mischkaGrey,
              width: 1.w,
            ),
            borderRadius: BorderRadius.circular(16.r),
          ),
        ),
        onSubmitted: widget.onSubmitted,
      ),
    );
  }
}
