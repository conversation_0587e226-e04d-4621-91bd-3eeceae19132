import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottomsheet_header.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/lgbtq_container.dart';
import 'package:quick_retail_mobile/ui/widgets/search_text_field.dart';


class SelectOption extends StatefulWidget {
  final List<String> options;
  final String? initialValue;
  final ValueChanged<String> returningValue;
  final String title;
  final String subtitle;
  final bool useConfirmationCTA;
  final String? searchFieldHint;
  const SelectOption({super.key, this.searchFieldHint, this.useConfirmationCTA = true, required this.title, required this.subtitle, required this.options, this.initialValue, required this.returningValue});

  @override
  State<SelectOption> createState() => _SelectOptionState();
}

class _SelectOptionState extends State<SelectOption> {


  String selectedOption = '';
  List<String> _filteredOptions = [];

  @override
  void initState() {
      selectedOption = widget.initialValue ?? '';
      _filteredOptions = List.of(widget.options);
    super.initState();
  }

  @override
  Widget build(BuildContext context) {

    final colorScheme = Theme.of(context).colorScheme;
    final textTheme = Theme.of(context).textTheme;


    if(widget.useConfirmationCTA){
      return Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          BottomSheetHeader(
            title: widget.title,
            subTitle: widget.subtitle,
          ),
          
          SizedBox(height: 24.h,),
          Flexible(
            child: SingleChildScrollView(
              child: Column(
                children: List.generate(widget.options.length, (index){
                  final option = widget.options[index];
                  final isSelected = selectedOption == option;
                  return Clickable(
                    onPressed: (){
                      setState(() {
                        selectedOption = option;
                      });
                    },
                    child: Padding(
                      padding: EdgeInsets.only(bottom: 16.h),
                      child: LgbtqContainer(
                        showPadding: isSelected,
                        useMargin: false,
                        child: Container(
                          padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
                          width: double.infinity,
                          decoration: BoxDecoration(
                              color:  isSelected ? ColorPath.athensGrey4 : ColorPath.athensGrey5,
                              border: Border.all(color: Colors.transparent),
                              borderRadius: BorderRadius.all(Radius.circular(8.r))
                          ),
                          child: Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Expanded(
                                  child:  Text(
                                    option,
                                    style: textTheme.bodyLarge?.copyWith(
                                        fontWeight: FontWeight.w400,
                                        color: colorScheme.textPrimary
                                    ),
                                  )
                              ),
                              SizedBox(width: 10.w,),
                              Container(
                                height: 16.h,
                                width: 16.w,
                                decoration: BoxDecoration(
                                    color: Colors.transparent,
                                    border: Border.all(color: isSelected ? colorScheme.textPrimary : ColorPath.mischkaGrey, width: 1.w),
                                    borderRadius: BorderRadius.all(Radius.circular(4.r))
                                ),
                                child: Center(
                                  child: isSelected ? Icon(Icons.check, color: colorScheme.textPrimary, size: 12,
                                  ) : Container(),
                                ),
                              )
                            ],
                          ),
                        ),
                      ),
                    ),
                  );
                }),
              ),
            ),
          ),
          SizedBox(height: 8.h,),
          CustomButton(
              buttonText: 'Select an option',
              buttonTextFontWeight: FontWeight.w600,
              buttonTextSize: 16,
              buttonTextColor: Colors.white,
              bgColor: colorScheme.textPrimary,
              disableBgColor: colorScheme.textPrimary.withOpacity(0.5),
              onPressed: selectedOption.isEmpty ? null : () {
                widget.returningValue(selectedOption);
                popNavigation(context: context);
              }
          ),
          SizedBox(height: 16.h,),
          CustomButton(
              buttonText: 'No, Close',
              buttonTextFontWeight: FontWeight.w600,
              borderColor: colorScheme.textPrimary,
              buttonTextColor: colorScheme.textPrimary,
              buttonTextSize: 16,
              onPressed: () {
                popNavigation(context: context);
              }
          ),

        ],
      );
    }


    return Column(
      mainAxisSize: MainAxisSize.min,
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        BottomSheetHeader(
            title: widget.title,
            subTitle: widget.subtitle,
          ),
        Container(
          height: 1.h,
          width: double.infinity,
          color: ColorPath.athensGrey,
          margin: EdgeInsets.symmetric(vertical: 16.h),
        ),
        if(widget.options.length > 10)Padding(
          padding: EdgeInsets.only(bottom: 26.h),
          child: SearchTextField(
            hintText: widget.searchFieldHint ?? 'Enter keyword',
            //controller: _email,
            keyboardType: TextInputType.text,
            isCompulsory: false,
            fillColor: ColorPath.athensGrey2,
            onChanged: (value){
              if(value.isEmpty){
                defaultFilterList();
              }else{
                filterList(searchWord: value);
              }
            },
            //validator: EmailValidator.validateEmail,
          ),
        ),
        if(_filteredOptions.isEmpty)
          Center(
            child: Text(
              'No Results',
              style: textTheme.bodyLarge?.copyWith(
                  fontWeight: FontWeight.w400,
                  color: colorScheme.textPrimary
              ),
            ),
          )
        else Flexible(
          child: ListView.separated(
            itemCount: _filteredOptions.length,
            shrinkWrap: true,
            itemBuilder: (BuildContext context, int index) {
              final option = _filteredOptions[index];
              final isSelected = selectedOption == option;
              return Clickable(
                onPressed: (){
                  setState(() {
                    selectedOption = option;
                  });
                  widget.returningValue(selectedOption);
                  popNavigation(context: context);
                },
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                        child:  Text(
                          option,
                          style: textTheme.bodyLarge?.copyWith(
                              fontWeight: FontWeight.w400,
                              color: colorScheme.textPrimary
                          ),
                        )
                    ),
                    SizedBox(width: 10.w,),
                    Container(
                      height: 16.h,
                      width: 16.w,
                      decoration: BoxDecoration(
                          color: Colors.transparent,
                          border: Border.all(color: isSelected ? colorScheme.textPrimary : ColorPath.mischkaGrey, width: 1.w),
                          borderRadius: BorderRadius.all(Radius.circular(4.r))
                      ),
                      child: Center(
                        child: isSelected ? Icon(Icons.check, color: colorScheme.textPrimary, size: 12,
                        ) : Container(),
                      ),
                    )
                  ],
                ),
              );
            },
            separatorBuilder: (context, index) {
              return Container(
                height: 1.h,
                width: double.infinity,
                color: ColorPath.athensGrey,
                margin: EdgeInsets.symmetric(vertical: 18.h),
              );
            },
          ),
        )
      ],
    );
  }

  //filters filterable option list
  filterList({required String searchWord}) {
    _filteredOptions = widget.options
        .where((option) =>
        option.toLowerCase().contains(searchWord.toLowerCase()))
        .toList();
    setState(() {});
  }

  //resets the filter list to default
  defaultFilterList() {
    _filteredOptions = widget.options;
    setState(() {});
  }
}


