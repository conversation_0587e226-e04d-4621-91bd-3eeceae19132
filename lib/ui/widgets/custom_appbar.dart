import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/ui/widgets/leading_icon.dart';
// import 'package:icoba/core/constants/app_theme/custom_color_scheme.dart';
// import 'package:icoba/core/constants/color_path.dart';
// import 'package:icoba/ui/widgets/leading_icon.dart';

customAppBar({
  required BuildContext context,
  String? title,
  FontWeight? titleFontWeight,
  double? titleSize = 14,
  List<Widget>? actions,
  bool centerTitle = false,
  VoidCallback? leadingIconOnPressed,
  double? appBarElevation,
  final Color? textColor,
  bool useCustomTitleWidget = false,
  Widget? titleWidget,
  bool showLeadingIcon = true,
  double? leadingWidth,
  Widget? leadingIcon,
  double preferredHeight = 0,
  Color? bottomDividerColor,
  Color? backgroundColor = ColorPath.athensGrey4,
}) {
  final textTheme = Theme.of(context).textTheme;
  final colorScheme = Theme.of(context).colorScheme;
  return AppBar(
    leadingWidth: leadingWidth,
    backgroundColor: backgroundColor,
    automaticallyImplyLeading: showLeadingIcon,
    scrolledUnderElevation: 0,
    centerTitle: centerTitle,
    title: useCustomTitleWidget
        ? titleWidget
        : title != null
            ? Text(
                title,
                style: textTheme.bodyMedium?.copyWith(
                    fontSize: titleSize,
                    fontWeight: titleFontWeight ?? FontWeight.w400,
                    color: textColor ?? colorScheme.textPrimary),
              )
            : null,
    leading: showLeadingIcon
        ? leadingIcon ??
            LeadingIcon(
              onPressed: leadingIconOnPressed,
            )
        : null,
    bottom: PreferredSize(
      preferredSize: Size.fromHeight(preferredHeight.h),
      child: Container(
        color: bottomDividerColor ?? ColorPath.athensGrey2,
        height: 1.h,
      ),
    ),
    actions: actions,
  );
}
