import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';

class NairaDisplay extends StatelessWidget {
  final double amount;
  final Color? color;
  final double? fontSize;
  final FontWeight? fontWeight;
  final bool addDecimal;
  const NairaDisplay(
      {super.key,
      required this.amount,
      this.color,
      this.fontSize,
        this.addDecimal = true,
      this.fontWeight});

  @override
  Widget build(BuildContext context) {
    return FittedBox(
        child: RichText(
      textAlign: TextAlign.left,
      text: TextSpan(
        style: Theme.of(context).textTheme.titleLarge?.copyWith(
          color: color ?? Theme.of(context).colorScheme.textPrimary,
          fontSize: fontSize?.sp ?? 24.sp,
          fontWeight: fontWeight ?? FontWeight.w700
        ),
        children: [
          TextSpan(
              text: Utilities.naira,
              style:
                  TextStyle(fontFamily: Platform.isAndroid ? '' : 'BR Cobane')),
          TextSpan(
            text: Utilities.formatAmount(amount: amount, addDecimal: addDecimal),
          ),
        ],
      ),
    ));
  }
}

TextSpan textspanNairaDisplay({
  double amount = 0.0,
  Color? color,
  double? fontSize,
  FontWeight? fontWeight,
}) {
  return TextSpan(
      text: '${Utilities.nairaSign}${Utilities.formatAmount(
        amount: amount,
      )}',

      style: TextStyle(color: color,fontWeight: fontWeight,fontSize: fontSize, fontFamily: Platform.isAndroid ? '' : 'BR Cobane'));
}
