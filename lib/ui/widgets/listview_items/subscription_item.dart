import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/data/models/subscription_data.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';
import 'package:quick_retail_mobile/ui/widgets/quantity_counter.dart';

class SubscriptionItem extends StatefulWidget {
  final void Function()? onPressed;
  final SubscriptionData? subscriptionData;
  final bool isSelected;
  final bool isAnnual;

  const SubscriptionItem(
      {super.key,
      this.onPressed,
      this.isSelected = false,
      this.isAnnual = false,
      this.subscriptionData});

  @override
  State<SubscriptionItem> createState() => _SubscriptionItemState();
}

class _SubscriptionItemState extends State<SubscriptionItem> {
  // int currentValue = 1;
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Clickable(
      onPressed: widget.onPressed,
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: ColorPath.athensGrey)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // selected checkbox
            Container(
              height: 20.w,
              width: 20.w,
              decoration: BoxDecoration(
                  color: ColorPath.dynamicColor("#F9F5FF"),
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(
                      color: widget.isSelected
                          ? ColorPath.flamingo
                          : ColorPath.athensGrey)),
              child: Icon(Icons.done,
                  size: 14,
                  color: widget.isSelected
                      ? ColorPath.flamingo
                      : ColorPath.athensGrey),
            ),
            SizedBox(
              height: 12.h,
            ),
            Text(
              widget.subscriptionData?.application?.name ?? "---",
              style: textTheme.titleSmall,
            ),
            SizedBox(
              height: 4.w,
            ),
            Text(
              widget.subscriptionData?.application?.description ?? "---",
              style: textTheme.bodySmall?.copyWith(color: colorScheme.text7),
            ),
            SizedBox(
              height: 16.w,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.isAnnual ? "Billed / Year" : "Billed / Month",
                      style: textTheme.bodySmall
                          ?.copyWith(color: colorScheme.textPrimary),
                    ),
                    widget.subscriptionData?.amount == 0
                        ? Text(
                            "Free",
                            style: textTheme.bodyMedium,
                          )
                        : NairaDisplay(
                            amount: double.parse(
                                widget.subscriptionData?.amount.toString() ??
                                    "0"),
                            color: colorScheme.textPrimary,
                            fontSize: 16.sp,
                            fontWeight: FontWeight.w500,
                          ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text(
                      "Admin Seat",
                      style: textTheme.bodySmall
                          ?.copyWith(color: colorScheme.textPrimary),
                    ),
                    Text(
                      "${widget.subscriptionData?.application?.freeAccessUsers ?? 0} Seats",
                      style: textTheme.bodyMedium,
                    )
                  ],
                ),
              ],
            ),
            SizedBox(
              height: 16.w,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Additional Admin Seat",
                      style: textTheme.bodySmall
                          ?.copyWith(color: colorScheme.textPrimary),
                    ),
                    Text.rich(TextSpan(children: [
                      textspanNairaDisplay(
                        amount: double.parse(
                            widget.subscriptionData?.pricePerSeat?.toString() ??
                                "0"),
                        color: colorScheme.textPrimary,
                        fontSize: 16.sp,
                        fontWeight: FontWeight.w500,
                      ),
                      const TextSpan(
                        text: " per seat",
                        style: TextStyle(
                            fontSize: 14, fontWeight: FontWeight.w400),
                      )
                    ])),
                  ],
                ),
                QuantityCounter(
                  onChanged: (value) {
                    log(widget.subscriptionData?.totalAdditionalSeats
                            .toString() ??
                        "0");
                    setState(() {
                      widget.subscriptionData?.totalAdditionalSeats = value;
                    });
                  },
                  lowerLimit: 0,
                  upperLimit:
                      widget.subscriptionData?.additionalUserSeatLimit ?? 10,
                  value: widget.subscriptionData?.totalAdditionalSeats ?? 0,
                )
              ],
            ),
          ],
        ),
      ),
    );
  }
}
