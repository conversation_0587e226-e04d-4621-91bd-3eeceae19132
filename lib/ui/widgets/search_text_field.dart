import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';



class SearchTextField extends StatefulWidget {
  final double labelSize;
  final FontWeight labelFontWeight;
  final Color? labelColor;
  final TextEditingController? controller;
  final ValueChanged<String>? onChanged;
  final ValueChanged<String>? onSubmitted;
  final String? Function(String?)? validator;
  final List<TextInputFormatter>? inputFormatters;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final double textSize;
  final Color textColor;
  final bool obscure;
  final  Widget? suffixIcon;
  final  Widget? prefixIcon;
  final String hintText;
  final String bottomHintText;
  final double hintSize;
  final Color? hintColor;
  final bool enabled;
  final bool readOnly;
  final bool isCompulsory;
  final FocusNode? focusPointer;
  final int maxLines;
  final bool isMoneyValue;
  final Color? bgColor;
  final Color? fillColor;


  const SearchTextField(
      {super.key,
        this.labelSize = 14,
        this.labelFontWeight = FontWeight.w400,
        this.labelColor,
        this.controller,
        this.onChanged,
        this.validator,
        this.inputFormatters,
        this.keyboardType = TextInputType.text,
        this.textSize = 16,
        this.textColor = Colors.black,
        this.obscure = false,
        this.suffixIcon,
        this.hintText = '',
        this.hintSize = 16,
        this.hintColor,
        this.enabled = true,
        this.readOnly = false,
        this.prefixIcon,
        this.bottomHintText = '',
        this.isCompulsory = true,
        this.focusPointer,
        this.maxLines = 1,
        this.isMoneyValue = false,
        this.bgColor,
        this.fillColor,
        this.textInputAction,
        this.onSubmitted
      });

  @override
  State<SearchTextField> createState() => _SearchTextFieldState();
}

class _SearchTextFieldState extends State<SearchTextField> {

  //final FocusNode _focusNode = FocusNode();
  //bool _isActive = false;

  @override
  void dispose() {
    //_focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {


    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;


    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 48.h,
          width: double.infinity,
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          decoration: BoxDecoration(
              color: widget.fillColor ?? Colors.transparent,
              border: Border.all(color: ColorPath.mischkaGrey, width: 1.w),
              borderRadius: BorderRadius.all(Radius.circular(8.r))
          ),
          child:  Row(
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              SvgPicture.asset(
                'assets/icons/svg/search2.svg',
                height: 16.h,
                width: 16.w,
                fit: BoxFit.cover,
              ),
              SizedBox(width: 10.w,),
              Expanded(
                child: Center(
                  child: TextFormField(
                      autovalidateMode: AutovalidateMode.onUserInteraction,
                      enabled: widget.enabled,
                      readOnly: widget.readOnly,
                      validator: widget.validator,
                      controller: widget.controller,
                      obscureText: widget.obscure,
                      style: TextStyle(fontSize: widget.textSize.sp, color: widget.textColor, fontFamily: widget.isMoneyValue && Platform.isAndroid ? '':null),
                      onChanged: widget.onChanged,
                      onFieldSubmitted: widget.onSubmitted,
                      keyboardType: widget.keyboardType,
                      textInputAction: widget.textInputAction,
                      inputFormatters: widget.inputFormatters,
                      decoration: InputDecoration(
                        isDense: false,
                        errorMaxLines: 3,
                        hintText: widget.hintText,
                        hintStyle: textTheme.bodyLarge?.copyWith(
                            color: widget.hintColor?.withOpacity(0.3) ?? colorScheme.subTextPrimary,
                            fontSize: widget.labelSize,
                            fontWeight: FontWeight.w500
                        ),
                        suffixIcon: widget.suffixIcon,
                        suffixIconConstraints: BoxConstraints(
                          minWidth: 25.w,
                          minHeight: 25.h,
                        ),
                        prefixIcon: widget.prefixIcon,
                        prefixIconConstraints: BoxConstraints(
                          minWidth: 25.w,
                          minHeight: 25.h,
                        ),
                        floatingLabelBehavior: FloatingLabelBehavior.always,
                        enabledBorder: InputBorder.none,
                        focusedBorder: InputBorder.none,
                        disabledBorder: InputBorder.none,
                        border: InputBorder.none,
                      )
                  ),
                ),
              ),
            ],
          ),
        ),
        if(widget.bottomHintText.isNotEmpty)SizedBox(height: 4.h,),
        if(widget.bottomHintText.isNotEmpty)Text(
          widget.bottomHintText,
          style: textTheme.bodySmall?.copyWith(
              color: colorScheme.subTextPrimary,
              fontWeight: FontWeight.w400
          ),
          textAlign: TextAlign.left,
        )
      ],
    );
  }
}
