import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/ui/widgets/calendar_filter_button.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

class DashBoardHeader extends StatelessWidget {
  const DashBoardHeader(
      {super.key,
      this.title = "",
      this.subtitle = "",
      this.filterText = "",
      this.titleTagText = "",
      this.child,
      this.showCTA = true,
      this.onPressed,
      this.padding,
      this.titleTagColor});

  final String title;
  final String subtitle;
  final String filterText;
  final void Function()? onPressed;
  final Widget? child;
  final Color? titleTagColor;
  final String titleTagText;
  final bool showCTA;
  final EdgeInsetsGeometry? padding;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? EdgeInsets.only(
        left: 16.w,
        right: 16.w,
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          ScreenTitle(
            title: title,
            subTitle: subtitle,
            titleTagColor: titleTagColor,
            titleTagText: titleTagText,
          ),
          if (showCTA)
            Clickable(
              onPressed: onPressed,
              child: child ??
                  CalendarFilterButton(
                    filterText: filterText,
                  ),
            )
        ],
      ),
    );
  }
}