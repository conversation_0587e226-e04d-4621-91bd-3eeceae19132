import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_constants.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/data/enum/metric_card_type.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';

// class OrderCard extends StatelessWidget {
//   const OrderCard({
//     super.key,
//     required this.totalOrder,
//   });

//   final String totalOrder;

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;
//     final colorScheme = Theme.of(context).colorScheme;
//     return Container(
//       padding: EdgeInsets.all(18.w),
//       width: screenWidth(context) / 1.25,
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(8.r),
//         border: Border.all(color: ColorPath.athensGrey),
//         color: ColorPath.aliceBlue,
//       ),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           SvgPicture.asset(Utilities.getSvg("order")),
//           SizedBox(
//             height: 16.h,
//           ),
//           Text(
//             "TOTAL ORDER",
//             style: textTheme.bodyMedium?.copyWith(fontWeight: FontWeight.w600),
//           ),
//           SizedBox(
//             height: 4.h,
//           ),
//           Text(
//             totalOrder,
//             style: textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600),
//           ),
//           SizedBox(
//             height: 4.h,
//           ),
//           // Container(
//           //   width: 49.w,
//           //   decoration: BoxDecoration(
//           //       borderRadius: BorderRadius.circular(10.r),
//           //       color: ColorPath.panacheGreen),
//           //   child: Row(
//           //     mainAxisAlignment: MainAxisAlignment.center,
//           //     children: [
//           //       Text(
//           //         "0.5",
//           //         style: textTheme.bodySmall
//           //             ?.copyWith(color: ColorPath.funGreen),
//           //       ),
//           //      const  Icon(
//           //         Icons.north_east,
//           //         size: 12,
//           //         color: ColorPath.funGreen,
//           //       )
//           //     ],
//           //   ),
//           // )
//         ],
//       ),
//     );
//   }
// }

// class RevenueCard extends StatelessWidget {
//   const RevenueCard({
//     super.key,
//     required this.totalRevenue,
//   });

//   final String totalRevenue;

//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;
//     final colorScheme = Theme.of(context).colorScheme;
//     return Container(
//       padding: EdgeInsets.all(18.w),
//       margin: EdgeInsets.only(right: 16.w),
//       width: screenWidth(context) / 1.25,
//       decoration: BoxDecoration(
//           borderRadius: BorderRadius.circular(8.r),
//           gradient: const LinearGradient(colors: [
//             ColorPath.flamingo,
//             ColorPath.fire,
//           ])),
//       child: Column(
//         crossAxisAlignment: CrossAxisAlignment.start,
//         children: [
//           SvgPicture.asset(Utilities.getSvg("dollar")),
//           SizedBox(
//             height: 16.h,
//           ),
//           Text(
//             "TOTAL REVENUE GENERATED",
//             style: textTheme.bodyMedium?.copyWith(
//                 color: ColorPath.athensGrey, fontWeight: FontWeight.w600),
//           ),
//           SizedBox(
//             height: 4.h,
//           ),
//           Text(
//             // "\$921,345",
//             totalRevenue,
//             style: textTheme.titleLarge?.copyWith(
//                 color: ColorPath.athensGrey, fontWeight: FontWeight.w600),
//           ),
//           SizedBox(
//             height: 4.h,
//           ),
//           // Container(
//           //   width: 49.w,
//           //   decoration: BoxDecoration(
//           //       borderRadius: BorderRadius.circular(10.r),
//           //       color: ColorPath.panacheGreen),
//           //   child: Row(
//           //     mainAxisAlignment: MainAxisAlignment.center,
//           //     children: [
//           //       Text(
//           //         "0.5",
//           //         style: textTheme.bodySmall
//           //             ?.copyWith(color: ColorPath.funGreen),
//           //       ),
//           //      const  Icon(
//           //         Icons.north_east,
//           //         size: 12,
//           //         color: ColorPath.funGreen,
//           //       )
//           //     ],
//           //   ),
//           // )
//         ],
//       ),
//     );
//   }
// }

class MetricCard extends StatelessWidget {
  const MetricCard({
    super.key,
    required this.type,
    required this.title,
    required this.value,
    this.margin,
  });

  final MetricCardType type;
  final String title;
  final String value;
  final EdgeInsetsGeometry? margin;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final isRevenue = type == MetricCardType.revenue;

    return Container(
      padding: EdgeInsets.all(18.w),
      margin: margin ?? EdgeInsets.only(right: 16.w),
      width: screenWidth(context) / 1.25,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(8.r),
        border: isRevenue ? null : Border.all(color: ColorPath.athensGrey),
        color: isRevenue ? null : ColorPath.aliceBlue,
        gradient: isRevenue
            ? const LinearGradient(colors: [
                ColorPath.flamingo,
                ColorPath.fire,
              ])
            : null,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SvgPicture.asset(Utilities.getSvg(isRevenue ? "dollar" : "order")),
          SizedBox(height: 16.h),
          Text(
            // isRevenue ? "TOTAL REVENUE GENERATED" : "TOTAL ORDER",
            title,
            style: textTheme.bodyMedium?.copyWith(
              color: isRevenue ? ColorPath.athensGrey : null,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 4.h),
          Text(
            value,
            style: textTheme.titleLarge?.copyWith(
              color: isRevenue ? ColorPath.athensGrey : null,
              fontWeight: FontWeight.w600,
            ),
          ),
          SizedBox(height: 4.h),
        ],
      ),
    );
  }
}

// Usage examples:
// MetricCard(type: MetricCardType.order, value: "1,234")
// MetricCard(type: MetricCardType.revenue, value: "\$921,345")
