import 'package:flutter/material.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';

class ProductDescriptionData extends StatelessWidget {
  final String description;
  final String data;

  const ProductDescriptionData(
      {super.key, this.description = '', this.data = ''});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Text.rich(TextSpan(
        text: '$description: ',
        style: textTheme.bodySmall?.copyWith(color: colorScheme.text8),
        children: [
          TextSpan(
            text: data,
            style: textTheme.bodySmall?.copyWith(
                color: colorScheme.text4, fontWeight: FontWeight.w500),
          )
        ]));
  }
}