import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';

class ProductImageWidget extends StatelessWidget {
  const ProductImageWidget({
    super.key,
    this.imagePath,
  });

  final String? imagePath;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 45.w,
      width: 45.w,
      decoration: BoxDecoration(
          border: Border.all(color: ColorPath.athensGrey),
          borderRadius: BorderRadius.circular(8.r),
          image: DecorationImage(
              fit: BoxFit.cover,
              image: NetworkImage(imagePath ??
                  "https://d2v5dzhdg4zhx3.cloudfront.net/web-assets/images/storypages/primary/ProductShowcasesampleimages/JPEG/Product+Showcase-1.jpg"))),
    );
  }
}
