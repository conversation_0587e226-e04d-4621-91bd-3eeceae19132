// ignore_for_file: deprecated_member_use

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:skeletonizer/skeletonizer.dart';

class ReusableSalesOverviewCard extends StatefulWidget {
  final List<double> data;
  final List<String>? labels; // Made nullable
  final String title;
  final String subtitle;
  final Color barColor;
  final List<Color>? barColors; // Added for multi-color support
  final Color backgroundColor;
  final List<String> yAxisLabels;
  final double maxValue;
  final double chartHeight;
  final double barWidth;
  final EdgeInsets padding;
  final EdgeInsets margin;
  final BorderRadius? borderRadius;
  final List<BoxShadow>? boxShadow;
  final TextStyle? labelTextStyle;
  final TextStyle? titleTextStyle;
  final TextStyle? subtitleTextStyle;
  final TextStyle? yAxisTextStyle;
  final TextStyle? valueTextStyle; // Added for displaying values on top
  final Duration animationDuration;
  final Duration staggerDelay;
  final Curve animationCurve;
  final bool showValues; // Added to show/hide values on bars
  final bool showLabels; // Added to control label display
  final double? titleFontSize; // Added for title customization
  final FontWeight? titleFontWeight; // Added for title customization

  const ReusableSalesOverviewCard({
    super.key,
    required this.data,
    this.labels, // Made optional
    this.title = '',
    this.subtitle = '',
    this.barColor = const Color(0xFFE85A4F),
    this.barColors, // Multi-color support
    this.backgroundColor = Colors.white,
    this.yAxisLabels = const ['500', '400', '300', '200', '100', '0'],
    this.maxValue = 500,
    this.chartHeight = 300,
    this.barWidth = 60, // Increased default width for better visibility
    this.padding = const EdgeInsets.all(16),
    this.margin = EdgeInsets.zero,
    this.borderRadius,
    this.boxShadow,
    this.labelTextStyle,
    this.titleTextStyle,
    this.subtitleTextStyle,
    this.yAxisTextStyle,
    this.valueTextStyle,
    this.animationDuration = const Duration(milliseconds: 1200),
    this.staggerDelay = const Duration(milliseconds: 100),
    this.animationCurve = Curves.easeOutBack,
    this.showValues = false,
    this.showLabels = true,
    this.titleFontSize,
    this.titleFontWeight,
  })  : assert(labels == null || data.length == labels.length,
            'Data and labels must have the same length when labels are provided'),
        assert(barColors == null || data.length == barColors.length,
            'Data and barColors must have the same length when barColors are provided');

  @override
  State<ReusableSalesOverviewCard> createState() =>
      _ReusableSalesOverviewCardState();
}

class _ReusableSalesOverviewCardState extends State<ReusableSalesOverviewCard>
    with TickerProviderStateMixin, AutomaticKeepAliveClientMixin {
  late AnimationController _mainController;
  late AnimationController _barController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;
  late List<AnimationController> _barControllers;
  late List<Animation<double>> _barAnimations;

  // Add this flag to track if animations have been started
  bool _animationsStarted = false;

  @override
  bool get wantKeepAlive => true;

  @override
  void initState() {
    super.initState();

    // Main card animation controller
    _mainController = AnimationController(
      duration: const Duration(milliseconds: 800),
      vsync: this,
    );

    // Bar animation controller
    _barController = AnimationController(
      duration: widget.animationDuration,
      vsync: this,
    );

    // Fade and slide animations for the card
    _fadeAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.0, 0.6, curve: Curves.easeOut),
    ));

    _slideAnimation = Tween<Offset>(
      begin: const Offset(0, 0.3),
      end: Offset.zero,
    ).animate(CurvedAnimation(
      parent: _mainController,
      curve: const Interval(0.0, 0.8, curve: Curves.easeOutCubic),
    ));

    // Individual bar animations with stagger effect
    _barControllers = [];
    _barAnimations = [];

    for (int i = 0; i < widget.data.length; i++) {
      final controller = AnimationController(
        duration: widget.animationDuration,
        vsync: this,
      );

      final animation = Tween<double>(
        begin: 0.0,
        end: 1.0,
      ).animate(CurvedAnimation(
        parent: controller,
        curve: widget.animationCurve,
      ));

      _barControllers.add(controller);
      _barAnimations.add(animation);
    }

    // Start animations only once
    _startAnimations();
  }

  void _startAnimations() async {
    // Prevent multiple animation starts - check both flag and controller status
    if (_animationsStarted || _mainController.isCompleted) return;
    _animationsStarted = true;

    // Start main card animation
    _mainController.forward();

    // Wait a bit, then start staggered bar animations
    await Future.delayed(const Duration(milliseconds: 400));

    for (int i = 0; i < _barControllers.length; i++) {
      Future.delayed(widget.staggerDelay * i, () {
        if (mounted && !_barControllers[i].isCompleted) {
          _barControllers[i].forward();
        }
      });
    }
  }

  @override
  void dispose() {
    _mainController.dispose();
    _barController.dispose();
    for (var controller in _barControllers) {
      controller.dispose();
    }
    super.dispose();
  }

  // Helper method to format numbers
  String _formatValue(double value) {
    if (value >= 1000000000) {
      return '${(value / 1000000000).toStringAsFixed(1)}B';
    } else if (value >= 1000000) {
      return '${(value / 1000000).toStringAsFixed(1)}M';
    } else if (value >= 1000) {
      return '${(value / 1000).toStringAsFixed(1)}K';
    } else {
      return value.toInt().toString();
    }
  }

  @override
  Widget build(BuildContext context) {
    super.build(context); // Required for AutomaticKeepAliveClientMixin
    return AnimatedBuilder(
      animation: _mainController,
      builder: (context, child) {
        return FadeTransition(
          opacity: _fadeAnimation,
          child: SlideTransition(
            position: _slideAnimation,
            child: Container(
              margin: widget.margin,
              padding: widget.padding,
              decoration: BoxDecoration(
                color: widget.backgroundColor,
                borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
                boxShadow: widget.boxShadow ??
                    [
                      BoxShadow(
                        color: Colors.grey.withOpacity(0.1),
                        spreadRadius: 1,
                        blurRadius: 8,
                        offset: const Offset(0, 2),
                      ),
                    ],
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Header section (if title is provided)
                  if (widget.title.isNotEmpty) ...[
                    AnimatedOpacity(
                      opacity: _fadeAnimation.value.clamp(0.0, 1.0),
                      duration: const Duration(milliseconds: 300),
                      child: Text(
                        widget.title,
                        style: widget.titleTextStyle ??
                            TextStyle(
                              fontSize: widget.titleFontSize ?? 18,
                              fontWeight:
                                  widget.titleFontWeight ?? FontWeight.bold,
                              color: Colors.black87,
                            ),
                      ),
                    ),
                    const SizedBox(height: 16),
                  ],

                  // Chart section
                  SizedBox(
                    height: widget.chartHeight,
                    child: Column(
                      children: [
                        // Y-axis labels and chart
                        Expanded(
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              // Y-axis labels
                              SizedBox(
                                width: 40,
                                child: Column(
                                  mainAxisAlignment:
                                      MainAxisAlignment.spaceBetween,
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: widget.yAxisLabels
                                      .map((label) => AnimatedOpacity(
                                            opacity: _fadeAnimation.value
                                                .clamp(0.0, 1.0),
                                            duration: const Duration(
                                                milliseconds: 600),
                                            child: Text(
                                              label,
                                              style: widget.yAxisTextStyle ??
                                                  TextStyle(
                                                      color: Colors.grey[600],
                                                      fontSize: 12),
                                            ),
                                          ))
                                      .toList(),
                                ),
                              ),
                              const SizedBox(width: 16),

                              // Bar chart
                              Expanded(
                                child: _AnimatedBarChart(
                                  data: widget.data,
                                  barColor: widget.barColor,
                                  barColors: widget.barColors,
                                  barWidth: widget.barWidth,
                                  maxValue: widget.maxValue,
                                  barAnimations: _barAnimations,
                                  showValues: widget.showValues,
                                  valueTextStyle: widget.valueTextStyle,
                                  formatValue: _formatValue,
                                ),
                              ),
                            ],
                          ),
                        ),

                        // X-axis labels (only if labels are provided and showLabels is true)
                        if (widget.labels != null && widget.showLabels) ...[
                          const SizedBox(height: 16),
                          Padding(
                            padding: const EdgeInsets.only(
                                left: 56), // 40 + 16 spacing
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                              children:
                                  widget.labels!.asMap().entries.map((entry) {
                                int index = entry.key;

                                // truncate label to 3 characters
                                String label = entry.value.length > 3
                                    ? entry.value.substring(0, 3)
                                    : entry.value;

                                return Flexible(
                                  child: AnimatedBuilder(
                                    animation: _barAnimations[index],
                                    builder: (context, child) {
                                      return Transform.translate(
                                        offset: Offset(
                                            0,
                                            10 *
                                                (1 -
                                                    _barAnimations[index]
                                                        .value
                                                        .clamp(0.0, 1.0))),
                                        child: AnimatedOpacity(
                                          opacity: _barAnimations[index]
                                              .value
                                              .clamp(0.0, 1.0),
                                          duration:
                                              const Duration(milliseconds: 200),
                                          child: Text(
                                            label,
                                            textAlign: TextAlign.center,
                                            style: widget.labelTextStyle ??
                                                TextStyle(
                                                    color: Colors.grey[600],
                                                    fontSize: 12),
                                          ),
                                        ),
                                      );
                                    },
                                  ),
                                );
                              }).toList(),
                            ),
                          ),
                          const SizedBox(height: 8),
                        ],

                        // Subtitle
                        if (widget.subtitle.isNotEmpty)
                          AnimatedOpacity(
                            opacity: _fadeAnimation.value.clamp(0.0, 1.0),
                            duration: const Duration(milliseconds: 800),
                            child: Text(
                              widget.subtitle,
                              style: widget.subtitleTextStyle ??
                                  TextStyle(
                                    color: Colors.grey[600],
                                    fontSize: 14,
                                    fontWeight: FontWeight.w500,
                                  ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }
}

class _AnimatedBarChart extends StatelessWidget {
  final List<double> data;
  final Color barColor;
  final List<Color>? barColors;
  final double barWidth;
  final double maxValue;
  final List<Animation<double>> barAnimations;
  final bool showValues;
  final TextStyle? valueTextStyle;
  final String Function(double) formatValue;

  const _AnimatedBarChart({
    required this.data,
    required this.barColor,
    this.barColors,
    required this.barWidth,
    required this.maxValue,
    required this.barAnimations,
    required this.showValues,
    this.valueTextStyle,
    required this.formatValue,
  });

  Color _getBarColor(int index) {
    if (barColors != null && index < barColors!.length) {
      return barColors![index];
    }
    return barColor;
  }

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate available width and adjust bar width if necessary
        double availableWidth = constraints.maxWidth;
        double totalHorizontalMargin =
            (data.length * 4); // 2px margin on each side
        double maxBarWidth =
            (availableWidth - totalHorizontalMargin) / data.length;
        double actualBarWidth = barWidth > maxBarWidth ? maxBarWidth : barWidth;

        return Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          crossAxisAlignment: CrossAxisAlignment.end,
          children: data.asMap().entries.map((entry) {
            int index = entry.key;
            double value = entry.value;
            Color currentBarColor = _getBarColor(index);

            // Normalize the value to a percentage of maxValue
            double normalizedHeight = (value / maxValue).clamp(0.0, 1.0);

            return Flexible(
              child: AnimatedBuilder(
                animation: barAnimations[index],
                builder: (context, child) {
                  double animatedHeight = (normalizedHeight *
                          200 *
                          barAnimations[index].value.clamp(0.0, 1.0))
                      .clamp(0.0, 200.0);

                  return Container(
                    margin: const EdgeInsets.symmetric(horizontal: 2),
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        // Value display on top of bar
                        if (showValues && animatedHeight > 10)
                          AnimatedOpacity(
                            opacity: barAnimations[index].value.clamp(0.0, 1.0),
                            duration: const Duration(milliseconds: 300),
                            child: Padding(
                              padding: const EdgeInsets.only(bottom: 4),
                              child: Text(
                                formatValue(value),
                                style: valueTextStyle ??
                                    const TextStyle(
                                      fontSize: 10,
                                      fontWeight: FontWeight.w500,
                                      color: Colors.black54,
                                    ),
                              ),
                            ),
                          ),

                        // Bar
                        Skeleton.replace(
                          replacement: Bone(
                            height: 200.h,
                            width: actualBarWidth,
                            borderRadius: BorderRadius.only(
                              topLeft: Radius.circular(8.r),
                              topRight: Radius.circular(8.r),
                            ),
                          ),
                          child: Container(
                            height: animatedHeight,
                            width: actualBarWidth,
                            decoration: BoxDecoration(
                              color: currentBarColor.withOpacity((0.1 +
                                      (0.9 *
                                          barAnimations[index]
                                              .value
                                              .clamp(0.0, 1.0)))
                                  .clamp(0.0, 1.0)),
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(8.r),
                                topRight: Radius.circular(8.r),
                              ),
                              boxShadow: barAnimations[index].value > 0.5
                                  ? [
                                      BoxShadow(
                                        color: currentBarColor.withOpacity(
                                            (0.3 *
                                                    barAnimations[index]
                                                        .value
                                                        .clamp(0.0, 1.0))
                                                .clamp(0.0, 1.0)),
                                        spreadRadius: 0,
                                        blurRadius: 4,
                                        offset: const Offset(0, 2),
                                      ),
                                    ]
                                  : null,
                            ),
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            );
          }).toList(),
        );
      },
    );
  }
}

// Usage Examples:

// Example matching your screenshot - Total Customer card
class TotalCustomerCard extends StatelessWidget {
  const TotalCustomerCard({super.key});

  @override
  Widget build(BuildContext context) {
    return ReusableSalesOverviewCard(
      title: 'Total Customer',
      subtitle: '', // No subtitle for cleaner look
      data: const [50, 150, 323], // Sample data matching the visual
      labels: null, // No x-axis labels
      showLabels: false, // Hide labels
      barColors: const [
        Color(0xFFE8B4B8), // Light pink/salmon
        Color(0xFFE85A4F), // Medium coral
        Color(0xFF8B4513), // Dark brown
      ],
      backgroundColor: Colors.grey[50]!,
      yAxisLabels: const ['500', '400', '300', '200', '100', '0'],
      maxValue: 500,
      chartHeight: 280,
      barWidth: 60, // Reduced width to prevent overflow
      titleFontSize: 16,
      titleFontWeight: FontWeight.w500,
      titleTextStyle: const TextStyle(
        color: Colors.grey,
        fontSize: 16,
        fontWeight: FontWeight.w500,
      ),
      borderRadius: BorderRadius.circular(16),
      padding: const EdgeInsets.all(20),
    );
  }
}

// Multi-color bar chart with labels
class MultiColorSalesCard extends StatelessWidget {
  const MultiColorSalesCard({super.key});

  @override
  Widget build(BuildContext context) {
    return ReusableSalesOverviewCard(
      title: 'Weekly Performance',
      data: const [120, 200, 150, 300, 250, 180, 320],
      labels: const ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
      barColors: const [
        Colors.red,
        Colors.orange,
        Colors.amber,
        Colors.green,
        Colors.blue,
        Colors.indigo,
        Colors.purple,
      ],
      backgroundColor: Colors.white,
      yAxisLabels: const ['400', '320', '240', '160', '80', '0'],
      maxValue: 400,
      chartHeight: 300,
      barWidth: 30,
      showValues: true, // Show values on top of bars
      animationDuration: const Duration(milliseconds: 1000),
      staggerDelay: const Duration(milliseconds: 120),
    );
  }
}

// Simple card without labels, single color
class SimpleNoLabelsCard extends StatelessWidget {
  const SimpleNoLabelsCard({super.key});

  @override
  Widget build(BuildContext context) {
    return ReusableSalesOverviewCard(
      title: 'Revenue Trend',
      subtitle: 'Last 5 months',
      data: const [450, 520, 380, 620, 580],
      labels: null, // No labels
      showLabels: false,
      barColor: const Color(0xFF4CAF50),
      backgroundColor: Colors.grey[100]!,
      yAxisLabels: const ['700', '560', '420', '280', '140', '0'],
      maxValue: 700,
      chartHeight: 250,
      barWidth: 50,
      animationCurve: Curves.bounceOut,
    );
  }
}

class BasicSalesCard extends StatelessWidget {
  const BasicSalesCard({super.key});

  @override
  Widget build(BuildContext context) {
    return ReusableSalesOverviewCard(
      title: 'Sales Overview',
      data: const [
        750000000,
        950000000,
        550000000,
        800000000,
        520000000,
        900000000,
        720000000
      ],
      labels: const ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
    );
  }
}

// Helper method to generate Y-axis labels based on max value
List<String> generateYAxisLabels(double maxValue) {
  if (maxValue <= 1000) {
    return ['1K', '800', '600', '400', '200', '0'];
  } else if (maxValue <= 10000) {
    return ['10K', '8K', '6K', '4K', '2K', '0'];
  } else if (maxValue <= 100000) {
    // int step = (maxValue / 5).round();
    return [
      '${(maxValue / 1000).round()}K',
      '${((maxValue * 0.8) / 1000).round()}K',
      '${((maxValue * 0.6) / 1000).round()}K',
      '${((maxValue * 0.4) / 1000).round()}K',
      '${((maxValue * 0.2) / 1000).round()}K',
      '0',
    ];
  } else {
    return [
      '${(maxValue / 1000000).toStringAsFixed(1)}M',
      '${((maxValue * 0.8) / 1000000).toStringAsFixed(1)}M',
      '${((maxValue * 0.6) / 1000000).toStringAsFixed(1)}M',
      '${((maxValue * 0.4) / 1000000).toStringAsFixed(1)}M',
      '${((maxValue * 0.2) / 1000000).toStringAsFixed(1)}M',
      '0',
    ];
  }
}
