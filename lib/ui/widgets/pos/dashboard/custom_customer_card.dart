import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';

class CustomCustomerCard extends StatelessWidget {
  const CustomCustomerCard({
    super.key,
    required this.customerName,
    required this.customerEmail,
    required this.totalAmount,
    required this.totalTransactions,
  });

  final String customerName;
  final String customerEmail;
  final String totalAmount;
  final String totalTransactions;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: ColorPath.athensGrey)),
      child: Row(
        children: [
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                customerName,
                style:
                    textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w500),
              ),
              SizedBox(
                height: 8.h,
              ),
              Text.rich(
                TextSpan(
                  text: "Email: ",
                  children: [
                    TextSpan(
                        text: customerEmail,
                        style: textTheme.bodySmall?.copyWith(
                            color: colorScheme.text4,
                            fontWeight: FontWeight.bold))
                  ],
                ),
                style: textTheme.bodySmall
                    ?.copyWith(color: colorScheme.subTextSecondary),
              )
            ],
          )),
          SizedBox(
            width: 12.w,
          ),
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              NairaDisplay(
                amount: (double.tryParse(totalAmount) ?? 0.0),
                fontSize: 16.sp,
                color: colorScheme.text4,
              ),
              SizedBox(
                height: 8.h,
              ),
              Text.rich(
                TextSpan(text: "Total Transactions: ", children: [
                  TextSpan(
                    text: totalTransactions,
                    style: textTheme.bodySmall?.copyWith(
                      color: colorScheme.text4,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                ]),
                style: textTheme.bodySmall
                    ?.copyWith(color: colorScheme.subTextSecondary),
              )
            ],
          )),
        ],
      ),
    );
  }
}
