import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/ui/widgets/color_tag.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';

class SalesOrderCard extends StatelessWidget {
  const SalesOrderCard({
    super.key,
    required this.orderNumber,
    required this.numberOfItem,
    required this.totalAmount,
    required this.status,
  });

  final String orderNumber;
  final String numberOfItem;
  final String totalAmount;
  final String status;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: ColorPath.athensGrey)),
      child: Row(
        children: [
          Expanded(
              child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                orderNumber,
                style:
                    textTheme.bodySmall?.copyWith(fontWeight: FontWeight.w500),
              ),
              SizedBox(
                height: 8.h,
              ),
              Text.rich(
                TextSpan(text: "Number of Item: ", children: [
                  TextSpan(
                      text: numberOfItem,
                      style: textTheme.bodySmall?.copyWith(
                          color: colorScheme.text4,
                          fontWeight: FontWeight.bold))
                ]),
                style: textTheme.bodySmall
                    ?.copyWith(color: colorScheme.subTextSecondary),
              )
            ],
          )),
          SizedBox(width: 12.w),
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              NairaDisplay(
                amount: double.parse(totalAmount),
                fontSize: 16.sp,
                color: colorScheme.text4,
              ),
              SizedBox(
                height: 8.h,
              ),
              ColorTag(
                color: 1 + 1 == 2 ? ColorPath.earlyDawn : ColorPath.foamGreen,
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.circle,
                      size: 6,
                      color: 1 + 1 == 2
                          ? ColorPath.californiaOrange
                          : ColorPath.meadowGreen,
                    ),
                    SizedBox(
                      width: 8.w,
                    ),
                    Text(
                      status,
                      style: textTheme.bodySmall?.copyWith(
                          color: 1 + 1 == 2
                              ? ColorPath.vesuvius
                              : ColorPath.funGreen),
                    ),
                  ],
                ),
              )
            ],
          ),
        ],
      ),
    );
  }
}
