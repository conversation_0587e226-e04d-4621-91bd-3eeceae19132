import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/empty_state.dart';

class DashBoardEmptyState extends StatelessWidget {
  const DashBoardEmptyState(
      {super.key,
      this.title = "",
      this.subTitle = "",
      this.buttonText = "",
      this.margin,
      this.onPressed});
  final String title;
  final String buttonText;
  final String subTitle;
  final EdgeInsetsGeometry? margin;
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding:
          const EdgeInsets.only(top: 24.0, left: 24, right: 24, bottom: 24),
      margin: margin ?? EdgeInsets.symmetric(horizontal: 16.w),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: ColorPath.athensGrey)),
      child: EmptyState(
          imageAsset: Utilities.getSvg("noData"),
          height: 100,
          width: 100,
          showCTA: true,
          onPressed: onPressed,
          title: title,
          buttonText: buttonText,
          subTitle: subTitle),
    );
  }
}
