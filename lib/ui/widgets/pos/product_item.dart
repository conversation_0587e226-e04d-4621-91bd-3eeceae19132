import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/data/models/product_response.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/color_tag.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/product_description_data.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/product_image_widget.dart';

class ProductItem extends StatefulWidget {
  const ProductItem({
    super.key,
    this.isSelectable = false,
    this.isSelected,
    required this.productData,
    this.onPressed,
  });

  final bool isSelectable;
  final bool? isSelected;
  final ProductData productData;
  final VoidCallback? onPressed;

  @override
  State<ProductItem> createState() => _ProductItemState();
}

class _ProductItemState extends State<ProductItem> {
  bool isSelected = false;

  @override
  void initState() {
    setState(() {
      isSelected = widget.isSelected ?? false;
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Clickable(
      onPressed: widget.onPressed ??
          () {
            if (!widget.isSelectable) return;
            setState(() {
              isSelected = !isSelected;
            });
          },
      child: Container(
        padding: EdgeInsets.all(16.w),
        decoration: BoxDecoration(
          border: Border.all(
              color: isSelected ? ColorPath.flamingo : ColorPath.athensGrey),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Column(
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    if (widget.isSelectable)
                      Row(
                        children: [
                          Container(
                            height: 16.w,
                            width: 16.w,
                            decoration: BoxDecoration(
                                border: Border.all(
                                    color: isSelected
                                        ? ColorPath.flamingo
                                        : ColorPath.mischkaGrey),
                                borderRadius: BorderRadius.circular(6.r)),
                            child: Icon(Icons.done,
                                size: 12,
                                color: isSelected
                                    ? ColorPath.flamingo
                                    : ColorPath.white),
                          ),
                          SizedBox(width: 6.w)
                        ],
                      ),
                    //! product image
                    ProductImageWidget(
                      imagePath: widget.productData.imagePath,
                    ),
                    SizedBox(
                      width: 8.w,
                    ),
                    // !product name and stock
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          widget.productData.name ?? 'N/A',
                          style: textTheme.bodySmall
                              ?.copyWith(fontWeight: FontWeight.w500),
                        ),
                        SizedBox(
                          height: 4.h,
                        ),
                        const ProductDescriptionData(
                          description: "Stock Level",
                          data: "56",
                        ),
                      ],
                    ),
                  ],
                ),
                Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    Text('Category:',
                        style: textTheme.bodySmall?.copyWith(
                          color: colorScheme.text8,
                        )),
                    SizedBox(height: 4.h),
                    Text(
                      widget.productData.product?.category?.name ?? 'N/A',
                      style: textTheme.bodySmall?.copyWith(
                          color: colorScheme.text4,
                          fontWeight: FontWeight.w500),
                    )
                  ],
                )
              ],
            ),
            SizedBox(height: 16.h),
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      if (widget.productData.variationAttributes?.isNotEmpty ==
                          true)
                        Column(
                          mainAxisSize: MainAxisSize.min,
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children:
                              widget.productData.variationAttributes!.map((e) {
                            return Padding(
                              padding: EdgeInsets.only(bottom: 4.h),
                              child: ProductDescriptionData(
                                description: e.optionType ?? 'N/A',
                                data: e.optionValue ?? 'N/A',
                              ),
                            );
                          }).toList(),
                        ),
                      SizedBox(height: 4.h),
                      ProductDescriptionData(
                        description: "Location",
                        data:
                            widget.productData.product?.location?.name ?? 'N/A',
                      ),
                      SizedBox(height: 4.h),
                      ProductDescriptionData(
                        description: "Category",
                        data:
                            widget.productData.product?.category?.name ?? 'N/A',
                      ),
                    ],
                  ),
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      NairaDisplay(
                        amount: double.parse(
                            widget.productData.sellingPrice ?? '0'),
                        color: colorScheme.text4,
                        fontSize: 14.sp,
                      ),
                      SizedBox(
                        height: 8.h,
                      ),
                      ColorTag(
                        color: ColorPath.foamGreen,
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            const Icon(
                              Icons.circle,
                              size: 6,
                              color: ColorPath.meadowGreen,
                            ),
                            SizedBox(
                              width: 8.w,
                            ),
                            Text(
                              widget.productData.status ?? 'N/A',
                              style: textTheme.bodySmall
                                  ?.copyWith(color: ColorPath.funGreen),
                            ),
                          ],
                        ),
                      )
                    ],
                  ),
                )
              ],
            )
          ],
        ),
      ),
    );
  }
}
