import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/data/enum/metric_card_type.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/view_models/pos_dashboard_view_model.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/cards.dart';
import 'package:skeletonizer/skeletonizer.dart';

class OverViewSection extends ConsumerWidget {
  const OverViewSection({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final posVm = ref.watch(posDashboardViewModel);
    return SizedBox(
      height: 175.h,
      child: Builder(builder: (context) {
        if (posVm.state == ViewState.busy) {
          return Skeletonizer(
            enabled: true,
            child: ListView(
              padding: EdgeInsets.only(left: 16.w, right: 16.w),
              // shrinkWrap: true,
              scrollDirection: Axis.horizontal,
              children: [
                Bone(
                  height: 175.h,
                  width: MediaQuery.of(context).size.width / 1.2,
                ),
                SizedBox(width: 16.w),
                Bone(
                  height: 175.h,
                  width: MediaQuery.of(context).size.width / 1.2,
                )
              ],
            ),
          );
        }
        return ListView(
          padding: EdgeInsets.only(left: 16.w, right: 16.w),
          // shrinkWrap: true,
          scrollDirection: Axis.horizontal,
          children: [
            MetricCard(
              type: MetricCardType.revenue,
              title: "TOTAL REVENUE GENERATED",
              value:
                  "${Utilities.dollarSign}${Utilities.formatAmount(amount: double.tryParse(posVm.analysisOverview?.totalRevenue ?? '0'))}",
            ),
            MetricCard(
              type: MetricCardType.order,
              title: "TOTAL ORDER",
              value: posVm.analysisOverview?.totalOrders?.toString() ?? '',
            ),
            MetricCard(
              type: MetricCardType.order,
              title: "TOTAL CUSTOMER",
              value: posVm.analysisOverview?.totalCustomers?.toString() ?? '',
            ),
          ],
        );
      }),
    );
  }
}
