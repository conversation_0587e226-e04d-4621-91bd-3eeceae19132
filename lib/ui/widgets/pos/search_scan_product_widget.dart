import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/pages/pos/scan_product/scan_product.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';

class SearchScanProductWidget extends StatelessWidget {
  final bool isEnable;
  const SearchScanProductWidget({super.key, this.isEnable = true});

  @override
  Widget build(BuildContext context) {
    return CustomTextField(
      isCompulsory: false,
      enabled: isEnable,
      hintText: 'Search by Name, Product.....',
      prefixIcon: Padding(
        padding: const EdgeInsets.only(left: 8.0, right: 8.0),
        child: SvgPicture.asset(Utilities.getSvg('search')),
      ),
      suffixIcon: Clickable(
        onPressed: () {
          pushNavigation(
              context: context,
              widget: const ScanProduct(),
              routeName: NamedRoutes.scanProduct);
        },
        child: Padding(
          padding: const EdgeInsets.only(left: 8.0, right: 12.0),
          child: SvgPicture.asset(Utilities.getSvg('scan')),
        ),
      ),
    );
  }
}
