import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';

class ColorTag extends StatelessWidget {
  final Color? color;
  final Widget? child;
  const ColorTag({super.key, this.color, this.child});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
      decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(16.r), color: color),
      child: child,
    );
  }
}
