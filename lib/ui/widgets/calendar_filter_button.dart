import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';

class CalendarFilterButton extends StatelessWidget {
  final String filterText;
  final void Function()? onPressed;
  const CalendarFilterButton({super.key, this.filterText = '', this.onPressed});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Clickable(
      onPressed: onPressed,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 8.h, horizontal: 12.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: ColorPath.athensGrey)),
        child: Row(
          children: [
            SvgPicture.asset(Utilities.getSvg("calendar")),
            SizedBox(
              width: 8.w,
            ),
            Text(
              filterText,
              style: textTheme.bodyMedium
                  ?.copyWith(color: colorScheme.subTextTertiary),
            )
          ],
        ),
      ),
    );
  }
}
