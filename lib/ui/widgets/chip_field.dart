import 'package:flutter/material.dart';

class TagsInputWidget extends StatefulWidget {
  final List<String> initialTags;
  final Function(List<String>) onTagsChanged;
  final String? hintText;
  final Color? chipColor;
  final Color? chipTextColor;
  final Color? deleteIconColor;
  final TextStyle? chipTextStyle;
  final InputDecoration? inputDecoration;
  final int? maxTags;
  final bool enabled;
  final String? Function(String)? tagValidator;

  const TagsInputWidget({
    super.key,
    this.initialTags = const [],
    required this.onTagsChanged,
    this.hintText = 'Add a tag...',
    this.chipColor,
    this.chipTextColor,
    this.deleteIconColor,
    this.chipTextStyle,
    this.inputDecoration,
    this.maxTags,
    this.enabled = true,
    this.tagValidator,
  });

  @override
  State<TagsInputWidget> createState() => _TagsInputWidgetState();
}

class _TagsInputWidgetState extends State<TagsInputWidget> {
  late List<String> _tags;
  final TextEditingController _textController = TextEditingController();
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _tags = List.from(widget.initialTags);
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _addTag(String tag) {
    final trimmedTag = tag.trim();
    if (trimmedTag.isEmpty) return;

    // Check if tag already exists
    if (_tags.contains(trimmedTag)) {
      _showSnackBar('Tag already exists');
      return;
    }

    // Check max tags limit
    if (widget.maxTags != null && _tags.length >= widget.maxTags!) {
      _showSnackBar('Maximum ${widget.maxTags} tags allowed');
      return;
    }

    // Validate tag if validator is provided
    if (widget.tagValidator != null) {
      final validationError = widget.tagValidator!(trimmedTag);
      if (validationError != null) {
        _showSnackBar(validationError);
        return;
      }
    }

    setState(() {
      _tags.add(trimmedTag);
    });
    _textController.clear();
    widget.onTagsChanged(_tags);
  }

  void _removeTag(int index) {
    setState(() {
      _tags.removeAt(index);
    });
    widget.onTagsChanged(_tags);
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        duration: const Duration(seconds: 2),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Tags display
        if (_tags.isNotEmpty)
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              border: Border.all(color: theme.dividerColor),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: _tags.asMap().entries.map((entry) {
                final index = entry.key;
                final tag = entry.value;

                return Chip(
                  label: Text(
                    tag,
                    style: widget.chipTextStyle ??
                        TextStyle(
                          color: widget.chipTextColor ??
                              theme.colorScheme.onSurface,
                        ),
                  ),
                  backgroundColor: widget.chipColor ??
                      theme.colorScheme.surfaceContainerHighest,
                  deleteIcon: Icon(
                    Icons.close,
                    size: 18,
                    color: widget.deleteIconColor ??
                        theme.colorScheme.onSurfaceVariant,
                  ),
                  onDeleted: widget.enabled ? () => _removeTag(index) : null,
                  materialTapTargetSize: MaterialTapTargetSize.shrinkWrap,
                );
              }).toList(),
            ),
          ),

        if (_tags.isNotEmpty) const SizedBox(height: 12),

        // Input field
        TextField(
          controller: _textController,
          focusNode: _focusNode,
          enabled: widget.enabled,
          decoration: widget.inputDecoration ??
              InputDecoration(
                hintText: widget.hintText,
                border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(8),
                ),
                suffixIcon: IconButton(
                  icon: const Icon(Icons.add),
                  onPressed: widget.enabled
                      ? () => _addTag(_textController.text)
                      : null,
                ),
              ),
          onSubmitted: widget.enabled ? _addTag : null,
          textInputAction: TextInputAction.done,
        ),
      ],
    );
  }
}

// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:quick_retail_mobile/core/constants/color_path.dart';

// /// A reusable widget for collecting email addresses and phone numbers as chips
// /// Similar to CustomTextField but specialized for invitations
// class ChipField extends StatefulWidget {
//   // Get the current state of the widget
//   ChipFieldState? of(BuildContext context) =>
//       context.findAncestorStateOfType<ChipFieldState>();

//   /// Optional controller for the text field
//   final TextEditingController? controller;

//   /// Label text displayed above the field
//   final String? labelText;

//   /// Hint text displayed inside the field when empty
//   final String? hintText;

//   /// Optional text to display next to the label (e.g. "(Optional)")
//   final String? optionalText;

//   /// Background color for the chips
//   final Color? chipColor;

//   /// Text color for the chips
//   final Color? chipTextColor;

//   /// Background color for the field
//   final Color? fillColor;

//   /// Border color for the field
//   final Color? borderColor;

//   /// Border radius for the field
//   final double? borderRadius;

//   /// Callback when the list of invitees changes
//   final Function(List<String>)? onInviteesChanged;

//   /// Validator function for the field
//   final String? Function(String?)? validator;

//   /// Whether to show the label header
//   final bool showLabelHeader;

//   /// Whether to hide the border
//   final bool hideBorder;

//   /// Initial list of invitees
//   final List<String>? initialInvitees;

//   /// Whether at least one invitee is required
//   final bool isRequired;

//   /// Maximum number of invitees allowed (null means no maximum)
//   final int? max;

//   /// Minimum number of invitees required (null means no minimum)
//   /// Note: If isRequired is true, min is at least 1
//   final int? min;

//   const ChipField({
//     super.key,
//     this.controller,
//     this.labelText,
//     this.hintText,
//     this.optionalText,
//     this.chipColor,
//     this.chipTextColor,
//     this.fillColor,
//     this.borderColor,
//     this.borderRadius,
//     this.onInviteesChanged,
//     this.validator,
//     this.showLabelHeader = true,
//     this.hideBorder = false,
//     this.initialInvitees,
//     this.isRequired = false,
//     this.max,
//     this.min,
//   });

//   @override
//   State<ChipField> createState() => ChipFieldState();
// }

// class ChipFieldState extends State<ChipField> {
//   final TextEditingController _controller = TextEditingController();
//   final FocusNode _focusNode = FocusNode();
//   List<String> _invitees = [];
//   String? _errorText;

//   /// Method to validate the field - can be called from a parent Form
//   bool validate() {
//     // Check minimum number of invitees
//     final effectiveMin = widget.min ?? (widget.isRequired ? 1 : 0);
//     if (_invitees.length < effectiveMin) {
//       setState(() {
//         if (effectiveMin == 1) {
//           _errorText = 'At least one invitee is required';
//         } else {
//           _errorText = 'At least $effectiveMin invitees are required';
//         }
//       });
//       return false;
//     }

//     // Check maximum number of invitees
//     if (widget.max != null && _invitees.length > widget.max!) {
//       setState(() {
//         _errorText = 'Maximum ${widget.max} invitees allowed';
//       });
//       return false;
//     }

//     setState(() {
//       _errorText = null;
//     });
//     return true;
//   }

//   // Patterns to validate emails and phone numbers
//   final RegExp _emailPattern =
//       RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$');
//   final RegExp _phonePattern =
//       RegExp(r'^\d{11,}$'); // At least 11 digits for phone numbers

//   TextEditingController get _effectiveController =>
//       widget.controller ?? _controller;

//   @override
//   void initState() {
//     super.initState();
//     if (widget.initialInvitees != null) {
//       _invitees = List.from(widget.initialInvitees!);
//     }
//     _effectiveController.addListener(_onTextChanged);
//   }

//   @override
//   void dispose() {
//     if (widget.controller == null) {
//       _controller.dispose();
//     } else {
//       _effectiveController.removeListener(_onTextChanged);
//     }
//     _focusNode.dispose();
//     super.dispose();
//   }

//   void _onTextChanged() {
//     final text = _effectiveController.text;

//     // Process text when space or comma is entered
//     if (text.endsWith(' ') || text.endsWith(',')) {
//       final trimmedText = text.replaceAll(',', '').trim();
//       if (trimmedText.isNotEmpty) {
//         _processText(trimmedText);
//       }
//       return;
//     }

//     // Real-time validation for better user feedback
//     if (text.isNotEmpty) {
//       final error = _getValidationError(text);
//       setState(() {
//         _errorText = error;
//       });
//     } else {
//       setState(() {
//         _errorText = null;
//       });
//     }
//   }

//   String? _getValidationError(String text) {
//     if (text.isEmpty) {
//       return null;
//     }

//     // Check if it looks like an email (contains @)
//     if (text.contains('@')) {
//       if (!_emailPattern.hasMatch(text)) {
//         return 'Invalid email format';
//       }
//     }
//     // Otherwise treat as phone number
//     else {
//       if (text.length < 11) {
//         return 'Phone number must be at least 11 digits';
//       }
//       if (!_phonePattern.hasMatch(text)) {
//         return 'Phone number must contain only digits';
//       }
//     }

//     return null;
//   }

//   void _processText(String text) {
//     final validationError = _getValidationError(text);

//     // Check if we've reached the maximum number of invitees
//     if (validationError == null) {
//       if (widget.max != null && _invitees.length >= widget.max!) {
//         setState(() {
//           _errorText = 'Maximum ${widget.max} invitees allowed';
//           _effectiveController.clear();
//         });
//         return;
//       }

//       setState(() {
//         if (!_invitees.contains(text)) {
//           _invitees.add(text);
//           widget.onInviteesChanged?.call(_invitees);
//         }
//         _effectiveController.clear();
//         _errorText = null;
//       });
//     } else {
//       setState(() {
//         _errorText = validationError;
//       });
//     }
//   }

//   void _removeInvitee(String invitee) {
//     setState(() {
//       _invitees.remove(invitee);
//       widget.onInviteesChanged?.call(_invitees);
//       // Clear error if we were at max capacity
//       if (widget.max != null && _errorText?.contains('Maximum') == true) {
//         _errorText = null;
//       }
//     });
//   }

//   /// Check if the field is at its maximum capacity
//   bool isAtMaxCapacity() {
//     return widget.max != null && _invitees.length >= widget.max!;
//   }

//   /// Get the current count of invitees
//   int getInviteeCount() {
//     return _invitees.length;
//   }

//   @override
//   Widget build(BuildContext context) {
//     return Column(
//       crossAxisAlignment: CrossAxisAlignment.start,
//       mainAxisSize: MainAxisSize.min,
//       children: [
//         if (widget.showLabelHeader)
//           Column(
//             mainAxisSize: MainAxisSize.min,
//             children: [
//               Row(
//                 mainAxisAlignment: MainAxisAlignment.spaceBetween,
//                 children: [
//                   Row(
//                     children: [
//                       Text(
//                         widget.labelText ?? 'Invite by phone number or email',
//                         style: TextStyle(
//                           color: ColorPath.neutral400,
//                           fontSize: 16.sp,
//                           fontWeight: FontWeight.w400,
//                         ),
//                       ),
//                       Text(
//                         widget.optionalText ?? '',
//                         style: TextStyle(
//                           color: ColorPath.neutral400,
//                           fontSize: 12.sp,
//                         ),
//                       ),
//                     ],
//                   ),
//                   // Show counter when max is set
//                   if (widget.max != null)
//                     Text(
//                       '${_invitees.length}/${widget.max}',
//                       style: TextStyle(
//                         color: isAtMaxCapacity()
//                             ? ColorPath.red.withAlpha(204)
//                             : ColorPath.neutral400,
//                         fontSize: 12.sp,
//                         fontWeight: FontWeight.w500,
//                       ),
//                     ),
//                 ],
//               ),
//               SizedBox(height: 4.h)
//             ],
//           ),
//         Container(
//           width: double.infinity,
//           decoration: BoxDecoration(
//             color: widget.fillColor ?? ColorPath.white,
//             borderRadius: BorderRadius.circular(widget.borderRadius ?? 8.r),
//             border: widget.hideBorder
//                 ? null
//                 : Border.all(
//                     color: widget.borderColor ?? ColorPath.secondary300,
//                     width: 1,
//                   ),
//           ),
//           padding: EdgeInsets.symmetric(horizontal: 14.w, vertical: 8.h),
//           child: Column(
//             crossAxisAlignment: CrossAxisAlignment.start,
//             children: [
//               Wrap(
//                 spacing: 8.w,
//                 runSpacing: 8.h,
//                 children:
//                     _invitees.map((invitee) => _buildChip(invitee)).toList(),
//               ),
//               TextField(
//                 controller: _effectiveController,
//                 focusNode: _focusNode,
//                 enabled: !isAtMaxCapacity(),
//                 style: TextStyle(
//                   color: isAtMaxCapacity()
//                       ? ColorPath.neutral200
//                       : ColorPath.black,
//                   fontSize: 16.sp,
//                   fontWeight: FontWeight.w400,
//                 ),
//                 decoration: InputDecoration(
//                   hintText: isAtMaxCapacity()
//                       ? 'Maximum invitees reached'
//                       : (widget.hintText ?? 'Enter email or phone number'),
//                   hintStyle: TextStyle(
//                     fontSize: 16.sp,
//                     fontWeight: FontWeight.w400,
//                     color: isAtMaxCapacity()
//                         ? ColorPath.neutral200
//                         : ColorPath.neutral100.withAlpha(51), // ~0.2 opacity
//                   ),
//                   border: InputBorder.none,
//                   isDense: true,
//                   contentPadding: EdgeInsets.symmetric(vertical: 8.h),
//                 ),
//                 onSubmitted: (value) {
//                   if (!isAtMaxCapacity() && value.trim().isNotEmpty) {
//                     _processText(value.trim());
//                   }
//                 },
//               ),
//             ],
//           ),
//         ),
//         if (_errorText != null)
//           Padding(
//             padding: EdgeInsets.only(top: 4.h, left: 4.w),
//             child: Text(
//               _errorText!,
//               style: TextStyle(
//                 color: ColorPath.red.withAlpha(204), // ~0.8 opacity
//                 fontSize: 12.sp,
//               ),
//             ),
//           ),
//       ],
//     );
//   }

//   Widget _buildChip(String invitee) {
//     return Container(
//       padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
//       decoration: BoxDecoration(
//         color: widget.chipColor ??
//             ColorPath.gray87.withValues(alpha: 0.1), // ~0.2 opacity
//         borderRadius: BorderRadius.circular(20.r),
//       ),
//       child: Row(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Text(
//             invitee,
//             style: TextStyle(
//               fontSize: 12.sp,
//               color: widget.chipTextColor ?? ColorPath.neutral300,
//             ),
//           ),
//           SizedBox(width: 4.w),
//           InkWell(
//             onTap: () => _removeInvitee(invitee),
//             child: Container(
//               padding: EdgeInsets.all(2.r),
//               decoration: const BoxDecoration(
//                 color: ColorPath.gray62,
//                 shape: BoxShape.circle,
//               ),
//               child: Icon(
//                 Icons.close,
//                 size: 14.sp,
//                 color: ColorPath.white,
//               ),
//             ),
//           ),
//         ],
//       ),
//     );
//   }
// }
