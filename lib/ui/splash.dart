import 'dart:async';

import 'package:flutter/material.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/data/models/user.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/secure_storage/secure_storage_utils.dart';
import 'package:quick_retail_mobile/ui/landing.dart';
import 'package:quick_retail_mobile/ui/pages/authentication/login.dart';

class Splash extends StatefulWidget {
  const Splash({super.key});

  @override
  State<Splash> createState() => _SplashState();
}

class _SplashState extends State<Splash> with SingleTickerProviderStateMixin {
  late AnimationController _controller;
  late Animation<Offset> _offsetAnimation;
  double _opacity = 0;
  User? _user;

  @override
  void initState() {
    super.initState();

    // Create animation controller with a duration of 1.5 seconds
    _controller = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );

    // Create a Tween animation from center (0,0) to left (-1,0)
    _offsetAnimation = Tween<Offset>(
      begin: Offset.zero,
      end: const Offset(-1.85, 0.0),
    ).animate(CurvedAnimation(
      parent: _controller,
      curve: Curves.easeInOut,
    ));

    WidgetsBinding.instance.addPostFrameCallback((_) {
      _controller.forward();
      fadeIn();
      switchNavigation();
    });
  }

  void fadeIn() async {
    Future.delayed(
      const Duration(milliseconds: 1500),
      () {
        setState(() {
          _opacity = 1.0;
        });
      },
    );
  }

  switchNavigation() async {
    _user = await SecureStorageUtils.retrieveUser();

    Timer(const Duration(milliseconds: 4000), () {
      if (_user != null) {
        replaceNavigation(
            context: context,
            widget: const Login(),
            routeName: NamedRoutes.login);
      } else {
        replaceNavigation(
            context: context,
            widget: const Landing(),
            routeName: NamedRoutes.landing);
      }
    });
  }

  @override
  void dispose() {
    _controller.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorPath.seaShellPink,
      body: Center(
        child: Stack(
          // fit: StackFit.,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                SlideTransition(
                  position: _offsetAnimation,
                  child: ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.asset(
                      'assets/images/png/q.png',
                      fit: BoxFit.cover,
                    ),
                  ),
                ),
              ],
            ),
            AnimatedOpacity(
              opacity: _opacity,
              duration: const Duration(milliseconds: 1500),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const SizedBox(
                    width: 24,
                  ),
                  ClipRRect(
                    borderRadius: BorderRadius.circular(12),
                    child: Image.asset(
                      'assets/images/png/quickretail.png',
                      // height: 70,
                      // fit: BoxFit.cover,
                      // fit: BoxFit.contain,
                    ),
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}
