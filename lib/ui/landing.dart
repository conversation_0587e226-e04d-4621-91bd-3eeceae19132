import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/ui/pages/onboarding/subscription/subscription_plans.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';

class Landing extends StatelessWidget {
  const Landing({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: ColorPath.seaShellPink,
      body: Container(
        height: double.infinity,
        width: double.infinity,
        decoration: const BoxDecoration(
            image: DecorationImage(
                image: AssetImage('assets/images/png/landing.png'),
                fit: BoxFit.cover)),
        child: Container(
          height: double.infinity,
          width: double.infinity,
          decoration: const BoxDecoration(
            gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Color.fromARGB(10, 255, 255, 255),
                  Color.fromARGB(84, 20, 20, 20),
                  ColorPath.codGray2,
                ]),
          ),
          child: Padding(
            padding: EdgeInsets.only(left: 16.w, right: 16.w),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Text.rich(
                  const TextSpan(
                      text: 'One Platform for all your\n ',
                      children: [
                        TextSpan(
                            text: 'Business Needs.',
                            style: TextStyle(color: ColorPath.flamingo))
                      ]),
                  textAlign: TextAlign.center,
                  style: TextStyle(
                      fontSize: 24.sp,
                      color: ColorPath.white,
                      fontWeight: FontWeight.w600,
                      fontFamily: 'ClashDisplay'),
                ),
                SizedBox(
                  height: 10.h,
                ),
                Text(
                  'Customizable solutions that cater to the\n unique needs of retail businesses.',
                  textAlign: TextAlign.center,
                  style: TextStyle(color: ColorPath.white, fontSize: 16.sp),
                ),
                SizedBox(
                  height: 20.h,
                ),
                CustomButton(
                  onPressed: () {
                    pushNavigation(
                        context: context,
                        widget: const SubscriptionPlans(),
                        routeName: NamedRoutes.pricing);
                    // pushNavigation(context: context, widget: const SignUp(), routeName: NamedRoutes.signup);
                    // pushNavigation(context: context, widget: const Login(), routeName: NamedRoutes.login);
                  },
                  buttonText: "Get Started",
                ),
                SizedBox(
                  height: 46.h,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
