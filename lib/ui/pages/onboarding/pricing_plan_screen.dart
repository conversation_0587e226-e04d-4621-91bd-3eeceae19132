import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_constants.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';

class PricingPlanScreen extends StatefulWidget {
  const PricingPlanScreen({super.key});

  @override
  State<PricingPlanScreen> createState() => _PricingPlanScreenState();
}

class _PricingPlanScreenState extends State<PricingPlanScreen> {
  int selection = 0;
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
          context: context, title: 'Pricing Plan', centerTitle: true),
      body: SingleChildScrollView(
        padding:
            EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h, bottom: 48.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Let’s get Started",
              style: TextStyle(fontFamily: clashDisplay, fontSize: 14.sp),
            ),
            SizedBox(
              height: 16.h,
            ),
            Text(
              "Try Premium for Free 💳  ",
              style: TextStyle(
                  fontFamily: clashDisplay,
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w600),
            ),
            SizedBox(
              height: 24.h,
            ),
            PricingItem(
              duration: "Monthly",
              subtitle: "Pay every month, cancel anytime",
              amount: "45.00",
              onPressed: () {
                setState(() {
                  selection = 1;
                });
              },
              isSelected: selection == 1,
            ),
            SizedBox(
              height: 24.h,
            ),
            PricingItem(
              duration: "Annually",
              subtitle: "Pay for a full year and save more",
              amount: "45.00",
              onPressed: () {
                setState(() {
                  selection = 2;
                });
              },
              isSelected: selection == 2,
              isTagged: true,
            ),
            SizedBox(
              height: 48.h,
            ),
            CustomButton(
              onPressed: () {
                // pushNavigation(context: context, widget: Login(), routeName: NamedRoutes.login);
              },
              bgColor:
                  1 + 1 == 3 ? ColorPath.inactiveFlamingo : ColorPath.flamingo,
              buttonText: "Start your 14-day free trial",
            )
          ],
        ),
      ),
    );
  }
}

class PricingItem extends StatelessWidget {
  final String duration;
  final String subtitle;
  final String amount;
  final bool isSelected;
  final void Function()? onPressed;
  final bool isTagged;
  const PricingItem(
      {super.key,
      required this.duration,
      required this.subtitle,
      required this.amount,
      this.isSelected = false,
      this.onPressed,
      this.isTagged = false});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Stack(
      // fit: StackFit.expand,
      children: [
        Column(
          children: [
            if (isTagged)
              SizedBox(
                height: 14.h,
              ),
            Clickable(
              onPressed: onPressed,
              child: Container(
                padding: EdgeInsets.all(10.w),
                decoration: BoxDecoration(
                    color: isSelected
                        ? ColorPath.flamingo.withOpacity(.1)
                        : Colors.transparent,
                    borderRadius: BorderRadius.circular(8.r),
                    border: Border.all(
                        color: isSelected
                            ? ColorPath.flamingo
                            : ColorPath.athensGrey5)),
                child: Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Column(
                      children: [
                        SizedBox(
                          height: 8.h,
                        ),
                        Container(
                          height: 16,
                          width: 16,
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4.r),
                              border: Border.all(
                                  color: isSelected
                                      ? ColorPath.flamingo
                                      : ColorPath.mischkaGrey)),
                          child: isSelected
                              ? const Icon(
                                  Icons.done,
                                  color: ColorPath.flamingo,
                                  size: 12,
                                )
                              : null,
                        ),
                      ],
                    ),
                    SizedBox(
                      width: 10.w,
                    ),
                    Expanded(
                      child: Row(
                        children: [
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  duration,
                                  style: textTheme.titleMedium,
                                ),
                                SizedBox(
                                  height: 4.h,
                                ),
                                Text(
                                  subtitle,
                                )
                              ],
                            ),
                          ),
                          Text(
                            "\$$amount",
                            style: textTheme.titleMedium,
                          )
                        ],
                      ),
                    )
                  ],
                ),
              ),
            ),
          ],
        ),
        if (isTagged)
          Positioned(
            right: 5,
            child: Container(
              // height: 26,
              padding: EdgeInsets.only(
                  left: 10.w, right: 10.w, top: 7.h, bottom: 7.h),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(24.r),
                  color: ColorPath.salemGreen),
              child: Row(
                children: [
                  SvgPicture.asset(Utilities.getSvg("tag")),
                  SizedBox(
                    width: 4.w,
                  ),
                  Text(
                    "Save 20%",
                    style: textTheme.bodySmall?.copyWith(color: Colors.white),
                  )
                ],
              ),
            ),
          ),
      ],
    );
  }
}
