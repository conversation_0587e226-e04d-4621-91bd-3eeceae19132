import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_constants.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/pages/authentication/login.dart';
import 'package:quick_retail_mobile/ui/pages/onboarding/create_password.dart';
import 'package:quick_retail_mobile/ui/pages/onboarding/demo.dart';
import 'package:quick_retail_mobile/ui/pages/onboarding/pricing_plan_screen.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';

class SignUp extends StatefulWidget {
  const SignUp({super.key});

  @override
  State<SignUp> createState() => _SignUpState();
}

class _SignUpState extends State<SignUp> {
  bool isEmailContentView = false;
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar:
          customAppBar(context: context, title: 'Sign Up', centerTitle: true),
      body: SingleChildScrollView(
        padding:
            EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h, bottom: 48.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Start your Free Trial Today !!!!",
              style: TextStyle(fontFamily: clashDisplay, fontSize: 14.sp),
            ),
            SizedBox(
              height: 4.h,
            ),
            isEmailContentView
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Instant Trial Access Almost Completed. ",
                        style: TextStyle(
                            fontFamily: clashDisplay,
                            fontSize: 22.sp,
                            fontWeight: FontWeight.w600),
                      ),
                      SizedBox(
                        height: 16.h,
                      ),
                      Text(
                        'Thank you for choosing Quick Retail! To ensure a seamless experience, we need to verify your email address. This is a crucial step to activate your 14-day free trial.',
                        style: textTheme.bodyMedium?.copyWith(fontSize: 16.sp),
                      ),
                      SizedBox(
                        height: 27.h,
                      ),
                      Text.rich(
                          TextSpan(
                              text: 'Please follow these simple steps: ',
                              children: [
                                TextSpan(
                                    text:
                                        '\n\nWe have sent a verification link to the email address you provided during sign-up:  ',
                                    style: textTheme.bodyMedium?.copyWith(
                                        fontSize: 16.sp,
                                        fontWeight: FontWeight.w400))
                              ]),
                          // textAlign: TextAlign.center,
                          style: textTheme.bodyMedium?.copyWith(
                              fontSize: 16.sp, fontWeight: FontWeight.w500)),
                      SizedBox(
                        height: 32.h,
                      ),
                      EmailContentListItem(
                        item: Text.rich(
                            TextSpan(text: 'Open the email: ', children: [
                              TextSpan(
                                  text: 'Look for an email from',
                                  children: [
                                    TextSpan(
                                        text: ' Quick Retail 14days Trial',
                                        style: textTheme.bodyMedium?.copyWith(
                                            fontSize: 16.sp,
                                            fontWeight: FontWeight.w400,
                                            color: ColorPath.flamingo)),
                                    const TextSpan(
                                        text:
                                            ' with the subject line "Verify Your Email for Quick Retail Software Trial"')
                                  ],
                                  style: textTheme.bodyMedium?.copyWith(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w400))
                            ]),
                            // textAlign: TextAlign.center,
                            style: textTheme.bodyMedium?.copyWith(
                                fontSize: 16.sp, fontWeight: FontWeight.w700)),
                      ),
                      SizedBox(height: 16.h),
                      EmailContentListItem(
                        item: Text.rich(
                            TextSpan(
                                text: 'Click the verification link: ',
                                children: [
                                  TextSpan(
                                      text:
                                          'This will confirm your email address and activate your free trial instantly. ',
                                      style: textTheme.bodyMedium?.copyWith(
                                          fontSize: 16.sp,
                                          fontWeight: FontWeight.w400))
                                ]),
                            // textAlign: TextAlign.center,
                            style: textTheme.bodyMedium?.copyWith(
                                fontSize: 16.sp, fontWeight: FontWeight.w700)),
                      ),
                      SizedBox(height: 16.h),
                      EmailContentListItem(
                        item: Text.rich(
                            TextSpan(text: 'Setup Password : ', children: [
                              TextSpan(
                                  text: 'For a secure access to your account',
                                  style: textTheme.bodyMedium?.copyWith(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w400))
                            ]),
                            // textAlign: TextAlign.center,
                            style: textTheme.bodyMedium?.copyWith(
                                fontSize: 16.sp, fontWeight: FontWeight.w700)),
                      ),
                      SizedBox(height: 24.h),
                      Text(
                        "Didn't receive the email?",
                        style: textTheme.bodyMedium?.copyWith(
                            fontSize: 16.sp, fontWeight: FontWeight.w500),
                      ),
                      SizedBox(height: 24.h),
                      EmailContentListItem(
                        item: Text(
                          "Check your spam or junk folder.",
                          style:
                              textTheme.bodyMedium?.copyWith(fontSize: 16.sp),
                        ),
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      EmailContentListItem(
                        padding: EdgeInsets.zero,
                        item: Text(
                          "Ensure you entered the correct email address",
                          style:
                              textTheme.bodyMedium?.copyWith(fontSize: 16.sp),
                        ),
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      EmailContentListItem(
                        item: Text.rich(
                          TextSpan(
                              text: "If you still can't find the email, ",
                              children: [
                                TextSpan(
                                    text: "click here to resend",
                                    style: textTheme.bodyMedium?.copyWith(
                                        fontSize: 16.sp,
                                        fontWeight: FontWeight.w400,
                                        color: ColorPath.flamingo)),
                                TextSpan(text: " the verification link."),
                              ]),
                          style:
                              textTheme.bodyMedium?.copyWith(fontSize: 16.sp),
                        ),
                      ),
                      SizedBox(
                        height: 48.h,
                      ),
                      Text.rich(
                        TextSpan(
                            text:
                                "Once your email is verified, you will have full access to all the premium features of Quick Retail for 14 days, absolutely free!\n\nIf you have any questions or need assistance, please feel free to reach out to our support team at [Support Email] or visit our", //todo::: request for [Support Email] replacement here
                            children: [
                              TextSpan(
                                  text: " Support Center.",
                                  style: textTheme.bodyMedium?.copyWith(
                                      fontSize: 16.sp,
                                      fontWeight: FontWeight.w400,
                                      color: ColorPath.flamingo)),
                              const TextSpan(
                                text:
                                    "\n\nAdekunle, Ibrahim Product\nDesign Team.\nSBSC UK LTD\<EMAIL>",
                              )
                            ]),
                        style: textTheme.bodyMedium?.copyWith(fontSize: 16.sp),
                      )
                    ],
                  )
                : Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Modern Day Operating System for Retail Business...",
                        style: TextStyle(
                            fontFamily: clashDisplay,
                            fontSize: 22.sp,
                            fontWeight: FontWeight.w600),
                      ),
                      SizedBox(
                        height: 16.h,
                      ),
                      Text.rich(
                        TextSpan(text: 'Already have an account? ', children: [
                          TextSpan(
                              text: 'Log in. ',
                              style: TextStyle(
                                  color: ColorPath.flamingo,
                                  fontWeight: FontWeight.w500),
                              recognizer: TapGestureRecognizer()
                                ..onTap = () {
                                  replaceNavigation(
                                      context: context,
                                      widget: Login(),
                                      routeName: NamedRoutes.login);
                                })
                        ]),
                        textAlign: TextAlign.center,
                        style: TextStyle(
                            fontSize: 14.sp, fontWeight: FontWeight.w400),
                      ),
                      SizedBox(
                        height: 32.h,
                      ),
                      CustomTextField(
                        label: 'Software Selected',
                        readOnly: true,
                        suffixIcon: Padding(
                          padding: const EdgeInsets.only(right: 16.0),
                          child: Icon(Icons.keyboard_arrow_down_outlined),
                        ),
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      Container(
                        height: 1.h,
                        width: double.infinity,
                        color: ColorPath.athensGrey,
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      CustomTextField(
                        label: 'First Name',
                        hintText: "Enter first name",
                        isCompulsory: false,
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      CustomTextField(
                        label: 'Last Name',
                        hintText: "Enter last name",
                        isCompulsory: false,
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      CustomTextField(
                        label: 'Company Name',
                        hintText: "Enter your Company Name",
                        isCompulsory: false,
                      ),
                      Text(
                        'This would be your workspace name.',
                        style: textTheme.bodyMedium
                            ?.copyWith(color: colorScheme.textPrimary),
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      CustomTextField(
                        label: 'Company URL',
                        hintText: "your-store-name.quickretail.com",
                        isCompulsory: false,
                        bgColor: ColorPath.athensGrey,
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      CustomTextField(
                        label: 'Email Address',
                        hintText: "Enter email address",
                        isCompulsory: false,
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      CustomTextField(
                        label: 'Phone Number',
                        hintText: "Enter Phone Number",
                        isCompulsory: false,
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      CustomTextField(
                        label: 'Company Size',
                        hintText: "Select Option",
                        isCompulsory: false,
                        readOnly: true,
                        suffixIcon: Padding(
                          padding: const EdgeInsets.only(right: 16.0),
                          child: Icon(Icons.keyboard_arrow_down_outlined),
                        ),
                      ),
                      SizedBox(
                        height: 24.h,
                      ),
                      CustomTextField(
                        label: 'Your Role',
                        hintText: "Select Option",
                        isCompulsory: false,
                        readOnly: true,
                        suffixIcon: Padding(
                          padding: const EdgeInsets.only(right: 16.0),
                          child: Icon(Icons.keyboard_arrow_down_outlined),
                        ),
                      ),
                    ],
                  ),
            SizedBox(
              height: 48.h,
            ),
            CustomButton(
              onPressed: () {
                if (isEmailContentView) {
                  pushNavigation(
                      context: context,
                      widget: const PricingPlanScreen(),
                      routeName: NamedRoutes.pricing);
                  // pushNavigation(
                  //     context: context,
                  //     widget: const Demo(),
                  //     routeName: NamedRoutes.demo);
                } 
                setState(() {
                  isEmailContentView = true;
                });
              },
              buttonText: "Get Our 14 Days Free Trial",
            )
          ],
        ),
      ),
    );
  }
}

class EmailContentListItem extends StatelessWidget {
  final Text item;
  final EdgeInsetsGeometry? padding;
  const EmailContentListItem({
    super.key,
    required this.item,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: padding ?? const EdgeInsets.only(right: 16.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SvgPicture.asset(Utilities.getSvg('optionCube')),
          SizedBox(
            width: 16.w,
          ),
          Flexible(
            child: item,
          ),
        ],
      ),
    );
  }
}
