import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_constants.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/pages/authentication/login.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';

class CreatePassword extends ConsumerStatefulWidget {
  const CreatePassword({super.key});

  @override
  ConsumerState<CreatePassword> createState() => _CreatePasswordState();
}

class _CreatePasswordState extends ConsumerState<CreatePassword> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar:
          customAppBar(context: context, title: 'Password', centerTitle: true),
      body: SingleChildScrollView(
        padding:
            EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h, bottom: 48.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              "Let’s get Started",
              style: TextStyle(fontFamily: clashDisplay, fontSize: 14.sp),
            ),
            SizedBox(
              height: 16.h,
            ),
            Text(
              "Victoria Store LLC 🏬  ",
              style: TextStyle(
                  fontFamily: clashDisplay,
                  fontSize: 24.sp,
                  fontWeight: FontWeight.w600),
            ),
            SizedBox(
              height: 24.h,
            ),
            CustomTextField(
              label: 'Password',
              hintText: "Enter Password",
              suffixIcon: Padding(
                padding: const EdgeInsets.only(right: 16.0),
                child: SvgPicture.asset(Utilities.getSvg('eye')),
              ),
              isCompulsory: false,
            ),
            SizedBox(
              height: 32.h,
            ),
            CustomTextField(
              label: 'Confirm Password',
              hintText: "Confirm Password",
              suffixIcon: Padding(
                padding: const EdgeInsets.only(right: 16.0),
                child: SvgPicture.asset(Utilities.getSvg('eye')),
              ),
              isCompulsory: false,
            ),
            SizedBox(
              height: 32.h,
            ),
            const CustomTextField(
              label: 'Security Question',
              isCompulsory: false,
              readOnly: true,
              suffixIcon: Padding(
                padding: EdgeInsets.only(right: 16.0),
                child: Icon(Icons.keyboard_arrow_down_outlined),
              ),
            ),
            const Text("Create an extra layer of security pass"),
            SizedBox(
              height: 32.h,
            ),
            const CustomTextField(
              label: 'Security Questions Answer',
              isCompulsory: false,
              readOnly: true,
            ),
            SizedBox(
              height: 48.h,
            ),
            CustomButton(
              onPressed: () {
                pushNavigation(
                    context: context,
                    widget: const Login(),
                    routeName: NamedRoutes.login);
              },
              bgColor:
                  1 + 1 == 3 ? ColorPath.inactiveFlamingo : ColorPath.flamingo,
              buttonText: "Create Password",
            )
          ],
        ),
      ),
    );
  }
}
