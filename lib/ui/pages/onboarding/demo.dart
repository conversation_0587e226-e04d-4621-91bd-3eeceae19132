import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_constants.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/ui/widgets/alert_dialogs/alert_dialog_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/alert_dialogs/demo_booked_successful_dialog.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';

class Demo extends StatefulWidget {
  const Demo({super.key});

  @override
  State<Demo> createState() => _DemoState();
}

class _DemoState extends State<Demo> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: customAppBar(context: context, title: 'Demo', centerTitle: true),
      body: SingleChildScrollView(
        padding:
            EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h, bottom: 48.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "Book a Demo Today",
                  style: TextStyle(
                      fontFamily: clashDisplay,
                      fontSize: 32.sp,
                      color: ColorPath.flamingo,
                      fontWeight: FontWeight.w500),
                ),
                SizedBox(
                  height: 8.h,
                ),
                Text.rich(
                  TextSpan(
                      text:
                          'Let’s us know which of our sub-system you want to see live in action',
                      children: []),
                  // textAlign: TextAlign.center,
                  style:
                      TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w400),
                ),
                SizedBox(
                  height: 32.h,
                ),
                // CustomTextField(
                //   label: 'Software Selected',
                //   readOnly: true,
                //   suffixIcon: Padding(
                //     padding: const EdgeInsets.only(right: 16.0),
                //     child: Icon(Icons.keyboard_arrow_down_outlined),
                //   ),
                // ),
                // SizedBox(
                //   height: 24.h,
                // ),
                // Container(
                //   height: 1.h,
                //   width: double.infinity,
                //   color: ColorPath.athensGrey,
                // ),
                // SizedBox(
                //   height: 24.h,
                // ),
                CustomTextField(
                  label: 'Full Name',
                  hintText: "EEnter your name",
                ),
                SizedBox(
                  height: 24.h,
                ),
                CustomTextField(
                  label: 'Email Address',
                  hintText: "Enter email",
                ),
                SizedBox(
                  height: 24.h,
                ),
                CustomTextField(
                  label: 'Phone Number',
                  hintText: "Enter phone number",
                  prefixIcon: Padding(
                    padding: EdgeInsets.only(left: 12.w),
                    child: Row(
                      children: [
                        Text("+234"),
                        Icon(
                          Icons.keyboard_arrow_down_rounded,
                          size: 18.w,
                        ),
                      ],
                    ),
                  ),
                  isCompulsory: false,
                ),
                Text(
                  'This would be your workspace name.',
                  style: textTheme.bodyMedium
                      ?.copyWith(color: colorScheme.textPrimary),
                ),
                SizedBox(
                  height: 24.h,
                ),
                CustomTextField(
                  label: 'Company Name',
                  hintText: "Enter name",
                  // bgColor: ColorPath.athensGrey,
                ),
                SizedBox(
                  height: 24.h,
                ),
                CustomTextField(
                  label: 'Industry',
                  hintText: "Select Option",
                  readOnly: true,
                  suffixIcon: Padding(
                    padding: const EdgeInsets.only(right: 16.0),
                    child: Icon(Icons.keyboard_arrow_down_outlined),
                  ),
                ),
                SizedBox(
                  height: 24.h,
                ),
                CustomTextField(
                  label: 'Business Size',
                  hintText: "Select Option",
                  readOnly: true,
                  suffixIcon: Padding(
                    padding: const EdgeInsets.only(right: 16.0),
                    child: Icon(Icons.keyboard_arrow_down_outlined),
                  ),
                ),
                SizedBox(
                  height: 24.h,
                ),
                CustomTextField(
                  label: 'How did you hear about us?',
                  hintText: "Select Option",
                  readOnly: true,
                  suffixIcon: Padding(
                    padding: const EdgeInsets.only(right: 16.0),
                    child: Icon(Icons.keyboard_arrow_down_outlined),
                  ),
                ),
                SizedBox(
                  height: 24.h,
                ),
                CustomTextField(
                  label: 'Choose Interested Apps ',
                  hintText: "Select Option",
                  readOnly: true,
                  suffixIcon: Padding(
                    padding: const EdgeInsets.only(right: 16.0),
                    child: Icon(Icons.keyboard_arrow_down_outlined),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 32.h,
            ),
            AcceptTandCItem(
              item: Text.rich(
                TextSpan(text: "Consent for Communication: ", children: [
                  TextSpan(
                      text:
                          "Explicit permission to send emails, updates, or marketing materials.",
                      style: textTheme.bodyMedium?.copyWith(
                          color: colorScheme.subTextPrimary,
                          fontWeight: FontWeight.w400))
                ]),
                style: textTheme.bodyMedium?.copyWith(
                    color: ColorPath.paleGrey, fontWeight: FontWeight.w500),
              ),
            ),
            SizedBox(
              height: 16.h,
            ),
            AcceptTandCItem(
              item: Text.rich(
                TextSpan(text: "Privacy Policy Agreement: ", children: [
                  TextSpan(
                      text:
                          " Acknowledgment that they understand and agree to the privacy policy.",
                      style: textTheme.bodyMedium?.copyWith(
                          color: colorScheme.subTextPrimary,
                          fontWeight: FontWeight.w400))
                ]),
                style: textTheme.bodyMedium?.copyWith(
                    color: ColorPath.paleGrey, fontWeight: FontWeight.w500),
              ),
            ),
            SizedBox(
              height: 48.h,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                CustomButton(
                  onPressed: () {
                    alertDialogWrapper(
                        context: context, child: DemoBookedSuccessfulDialog());
                  },
                  buttonWidth: 175.w,
                  buttonText: "Book A Demo",
                  buttonTextColor: ColorPath.codGray1,
                ),
              ],
            )
          ],
        ),
      ),
    );
  }
}

class AcceptTandCItem extends StatelessWidget {
  final Text item;
  const AcceptTandCItem({
    super.key,
    required this.item,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          height: 16,
          width: 16,
          decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(4.r),
              border: Border.all(
                  color:
                      1 + 1 == 3 ? ColorPath.flamingo : ColorPath.mischkaGrey)),
          child: 1 + 1 == 3
              ? const Icon(
                  Icons.done,
                  color: ColorPath.flamingo,
                  size: 12,
                )
              : null,
        ),
        SizedBox(
          width: 24.w,
        ),
        Flexible(
          child: item,
        )
      ],
    );
  }
}
