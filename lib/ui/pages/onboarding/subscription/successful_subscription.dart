import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/view_models/auth_view_models/subscription_view_model.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/ui/pages/authentication/create_new_password.dart';
import 'package:quick_retail_mobile/ui/widgets/busy_overlay.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';

class SuccessfulSubscription extends ConsumerStatefulWidget {
  const SuccessfulSubscription({super.key});

  @override
  ConsumerState<SuccessfulSubscription> createState() =>
      _SuccessfulSubscriptionState();
}

class _SuccessfulSubscriptionState
    extends ConsumerState<SuccessfulSubscription> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(subscriptionViewModel).verifyPayment();
    });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    final vm = ref.watch(subscriptionViewModel);
    return BusyOverlay(
      show: vm.state == ViewState.busy,
      child: Scaffold(
        appBar: customAppBar(
            context: context,
            showLeadingIcon: false,
            title: "Subscription",
            centerTitle: true),
        body: Padding(
          padding: EdgeInsets.symmetric(horizontal: 16.w),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            children: [
              const Icon(Icons.check_circle, size: 100, color: Colors.green),
              const SizedBox(height: 20),
              Text(
                "Subscription Successful",
                style:
                    textTheme.bodyLarge?.copyWith(fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 10),
              Text(
                "You have successfully subscribed.. You can choose to renew this plan or change plan later. ",
                textAlign: TextAlign.center,
                style: textTheme.bodySmall
                    ?.copyWith(color: colorScheme.textPrimary),
              ),
              SizedBox(height: 52.h),
              CustomButton(
                onPressed: () {
                  pushNavigation(
                      context: context,
                      widget: const CreateNewPassword(),
                      routeName: NamedRoutes.createNewPassword);
                },
                buttonText: "Create Password",
              )
            ],
          ),
        ),
      ),
    );
  }
}
