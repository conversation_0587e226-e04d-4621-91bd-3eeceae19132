import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/view_models/auth_view_models/subscription_view_model.dart';
import 'package:quick_retail_mobile/core/utilities/input_formatters/phone_number_input_formatter.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/core/utilities/validator.dart';
import 'package:quick_retail_mobile/ui/pages/in_app_web_view.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/company_size_bottomsheet.dart';
import 'package:quick_retail_mobile/ui/widgets/busy_overlay.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';
import 'package:quick_retail_mobile/ui/widgets/show_flush_bar.dart';

class AccountInfo extends ConsumerStatefulWidget {
  const AccountInfo({super.key});

  @override
  ConsumerState<AccountInfo> createState() => _AccountInfoState();
}

class _AccountInfoState extends ConsumerState<AccountInfo> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(subscriptionViewModel).fetchCompanySizes();
    });
    super.initState();
  }

  TextEditingController firstNameController = TextEditingController();
  TextEditingController lastNameController = TextEditingController();
  TextEditingController emailController = TextEditingController();
  TextEditingController phoneNumberController = TextEditingController();
  TextEditingController companyNameController = TextEditingController();
  TextEditingController companySizeController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  Widget build(BuildContext context) {
    final vm = ref.watch(subscriptionViewModel);
    return BusyOverlay(
      show: vm.state == ViewState.busy,
      child: Scaffold(
        appBar: customAppBar(
          context: context,
          title: "Account Set Up",
          centerTitle: true,
        ),
        body: Form(
          key: _formKey,
          child: ListView(
            padding: EdgeInsets.only(
                left: 16.w, right: 16.w, top: 32.h, bottom: 48.h),
            children: [
              const ScreenTitle(
                  title: "Account Details",
                  subTitle:
                      "Fill in the information below to create your account"),
              SizedBox(
                height: 32.h,
              ),
              CustomTextField(
                label: "First Name",
                isCompulsory: false,
                hintText: "Enter first name",
                validator: FieldValidator.validate,
                controller: firstNameController,
              ),
              SizedBox(
                height: 16.h,
              ),
              CustomTextField(
                label: "Last Name",
                isCompulsory: false,
                hintText: "Enter last name",
                validator: FieldValidator.validate,
                controller: lastNameController,
              ),
              SizedBox(
                height: 16.h,
              ),
              CustomTextField(
                label: "Email Address",
                isCompulsory: false,
                hintText: "Enter email address",
                validator: EmailValidator.validateEmail,
                controller: emailController,
              ),
              SizedBox(
                height: 16.h,
              ),
              CustomTextField(
                label: "Phone Number",
                isCompulsory: false,
                hintText: "Enter phone number",
                validator: FieldValidator.validate,
                inputFormatters: [
                  LengthLimitingTextInputFormatter(18),
                  // FilteringTextInputFormatter.digitsOnly,
                  PhoneNumberInputFormatter()
                ],
                keyboardType: TextInputType.phone,
                controller: phoneNumberController,
              ),
              SizedBox(
                height: 16.h,
              ),
              CustomTextField(
                label: "Company Name",
                isCompulsory: false,
                hintText: "Enter company name",
                controller: companyNameController,
                validator: FieldValidator.validate,
              ),
              SizedBox(
                height: 16.h,
              ),
              Clickable(
                onPressed: () {
                  bottomSheetWrapper(
                      context: context,
                      child: CompanySizeBottomsheet(
                        returningValue: (value) {
                          companySizeController.text = value;
                          setState(() {});
                        },
                      )
                      // SelectOption(
                      //     title: "Company Size",
                      //     subtitle: "Select option from ranges",
                      //     options: const ["One", "two", "three"],
                      //     returningValue: (val) {})
                      );
                },
                child: CustomTextField(
                  label: "Company Size",
                  enabled: false,
                  isCompulsory: false,
                  hintText: "Select company size",
                  controller: companySizeController,
                  suffixIcon: const Padding(
                    padding: EdgeInsets.only(right: 16.0),
                    child: Icon(Icons.keyboard_arrow_down_outlined),
                  ),
                ),
              ),
              SizedBox(
                height: 48.h,
              ),
              CustomButton(onPressed: () async {
                final validate = _formKey.currentState!.validate();
                if (validate) {
                  if (vm.selectedCompanySize == null) {
                    showFlushBar(
                        context: context,
                        message: "Please select company size",
                        success: false);
                    return;
                  }
                  await vm.registerAccount(
                      firstname: firstNameController.text,
                      lastname: lastNameController.text,
                      email: emailController.text,
                      phoneno: Utilities.cleanPhoneNumber(
                          phoneNumber: phoneNumberController.text),
                      companyName: companyNameController.text);

                  if (vm.state == ViewState.retrieved) {
                    // route to paystack checkout
                    pushNavigation(
                        context: context,
                        widget: InAppWebView(
                          url: vm.subscriptionCheckoutData?.authUrl ?? '',
                          title: 'Paystack Checkout',
                        ),
                        routeName: NamedRoutes.inAppWebView);
                  } else {
                    //display error message
                    showFlushBar(
                        context: context, message: vm.message, success: false);
                  }
                }
                // pushNavigation(
                //     context: context,
                //     widget: const PaymentSummary(),
                //     routeName: NamedRoutes.paymentSummary);
              })
            ],
          ),
        ),
      ),
    );
  }
}
