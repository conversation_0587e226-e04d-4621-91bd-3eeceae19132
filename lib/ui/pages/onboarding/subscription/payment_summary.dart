import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

class PaymentSummary extends StatefulWidget {
  const PaymentSummary({super.key});

  @override
  State<PaymentSummary> createState() => _PaymentSummaryState();
}

class _PaymentSummaryState extends State<PaymentSummary> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
        context: context,
        title: "Payment",
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding:
            EdgeInsets.only(left: 16.w, right: 16.w, top: 32.h, bottom: 48.h),
        child: Column(
          children: [
            const ScreenTitle(title: "Payment Summary", subTitle: ""),
            SizedBox(
              height: 16.h,
            ),
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Number of Apps",
                    style: textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500, color: colorScheme.text7),
                  ),
                  Text(
                    "3",
                    style: textTheme.bodyLarge?.copyWith(
                      fontWeight: FontWeight.w700,
                      color: colorScheme.text6,
                    ),
                  ),
                ],
              ),
            ),
            Container(
              height: 1,
              color: ColorPath.mischkaGrey,
            ),
            SizedBox(
              height: 16.h,
            ),
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "Total Cost",
                    style: textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500, color: colorScheme.text7),
                  ),
                  NairaDisplay(
                    amount: 30000,
                    fontWeight: FontWeight.w700,
                    color: colorScheme.text6,
                    fontSize: 16,
                  ),
                ],
              ),
            ),
            Container(
              height: 1,
              color: ColorPath.mischkaGrey,
            ),
            SizedBox(
              height: 16.h,
            ),
            Padding(
              padding:
                  const EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    "V.A.T (7.5%) ",
                    style: textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w500, color: colorScheme.text7),
                  ),
                  NairaDisplay(
                    amount: 3225,
                    fontWeight: FontWeight.w700,
                    color: colorScheme.text6,
                    fontSize: 16,
                  ),
                ],
              ),
            ),
            Container(
              height: 1,
              color: ColorPath.mischkaGrey,
            ),
            SizedBox(
              height: 32.h,
            ),
            const ScreenTitle(
                title: "Payment Method",
                subTitle:
                    "You will be debited from your card. You can choose to change or add a new card for this transaction."),
            SizedBox(
              height: 24.h,
            ),
            const CustomTextField(
              label: "Card Name",
              // isCompulsory: false,
              hintText: "Enter card name",
            ),
            SizedBox(
              height: 16.h,
            ),
            const CustomTextField(
              label: "Card Number",
              // isCompulsory: false,
              hintText: "Enter card nuber",
            ),
            SizedBox(
              height: 16.h,
            ),
            const CustomTextField(
              label: "Expiration",
              // isCompulsory: false,
              hintText: "11/11",
            ),
            SizedBox(
              height: 16.h,
            ),
            const CustomTextField(
              label: "CCV",
              // isCompulsory: false,
              hintText: "123",
            ),
            SizedBox(
              height: 56.h,
            ),
            CustomButton(
              onPressed: () {},
              buttonText: "Pay N32,250 Now",
            )
          ],
        ),
      ),
    );
  }
}
