import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/view_models/auth_view_models/subscription_view_model.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/ui/pages/onboarding/subscription/account_info.dart';
import 'package:quick_retail_mobile/ui/pages/onboarding/subscription/annual.dart';
import 'package:quick_retail_mobile/ui/pages/onboarding/subscription/free_trial.dart';
import 'package:quick_retail_mobile/ui/pages/onboarding/subscription/monthly.dart';
import 'package:quick_retail_mobile/ui/widgets/busy_overlay.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_painter/custom_tab_indicator.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';
import 'package:quick_retail_mobile/ui/widgets/quantity_counter.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

class SubscriptionPlans extends ConsumerStatefulWidget {
  const SubscriptionPlans({super.key});

  @override
  ConsumerState<SubscriptionPlans> createState() => _SubscriptionPlansState();
}

class _SubscriptionPlansState extends ConsumerState<SubscriptionPlans>
    with SingleTickerProviderStateMixin {
  late TabController _controller;

  @override
  void initState() {
    _controller = TabController(
      length: 3,
      vsync: this,
    );

    _controller.addListener(() {
      // if (_controller.index == 0) {
        ref.watch(subscriptionViewModel).selectedSubscriptions.clear();
      if (_controller.index == 0) {
        ref.watch(subscriptionViewModel).selectedBillingType = "trial";
      }
      if (_controller.index == 1) {
        ref.watch(subscriptionViewModel).selectedBillingType = "monthly";
      }
      if (_controller.index == 2) {
        ref.watch(subscriptionViewModel).selectedBillingType = "yearly";
      }


        setState(() {});
      // }
    });

    // WidgetsBinding.instance.addPostFrameCallback((_) {
    //   //fetch profile(about me)
    //   ref.read(subscriptionViewModel).fetchAvailableSubscriptions();
    // });

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final vm = ref.watch(subscriptionViewModel);
    return BusyOverlay(
      show: vm.state == ViewState.busy,
      child: Scaffold(
        appBar: customAppBar(
          context: context,
          title: "Subscribe to QuickRetail",
          centerTitle: true,
        ),
        body: Padding(
          padding:
              EdgeInsets.only(left: 16.w, right: 16.w, top: 32.h, bottom: 48.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const ScreenTitle(
                  title: "Subscription Details",
                  subTitle:
                      "Select a subscription plan to sign up to QuickRetail mobile Uncheck modules that you do not want."),
              SizedBox(
                height: 16.h,
              ),
              tabBar(context: context),
              Expanded(
                child: TabBarView(
                  controller: _controller,
                  //physics: const NeverScrollableScrollPhysics(),
                  children: const [
                    // free
                    FreeTrial(),
                    // monthly
                    Monthly(),

                    // annual
                    Annual(),
                  ],
                ),
              )
            ],
          ),
        ),
      ),
    );
  }

  //tab bar
  tabBar({required BuildContext context}) => Align(
        alignment: Alignment.center,
        child: Container(
          width: double.infinity,
          // margin:
          //     EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h, bottom: 16.h),
          padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 4.h),
          decoration: BoxDecoration(
              color: ColorPath.dynamicColor("#F7F6FB"),
              borderRadius: BorderRadius.all(Radius.circular(6.r))),
          child: TabBar(
            splashFactory: NoSplash.splashFactory,
            overlayColor: WidgetStateProperty.all(Colors.transparent),
            isScrollable: false,
            labelColor: ColorPath.oxfordBlue,
            labelStyle: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: Theme.of(context).colorScheme.textPrimary),
            labelPadding: EdgeInsets.only(
              top: 8.h,
              bottom: 8.h,
              left: 2.w,
              right: 2.w,
            ),
            indicatorPadding: EdgeInsets.zero,
            unselectedLabelStyle: Theme.of(context)
                .textTheme
                .bodyMedium
                ?.copyWith(
                    fontWeight: FontWeight.w400,
                    color: Theme.of(context).colorScheme.subTextPrimary),
            indicatorSize: TabBarIndicatorSize.tab,
            dividerColor: Colors.transparent,
            //isScrollable: true,
            tabs: const [
              Text(
                "Free trial",
              ),
              Text(
                "Billed Monthly",
              ),
              Text(
                "Billed Annually",
              ),
            ],
            indicatorColor: ColorPath.hazeBlue,
            indicator: const CustomTabIndicator(bgColor: ColorPath.athensGrey6),
            indicatorWeight: 2.w,
            controller: _controller,
          ),
        ),
      );
}
