import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/view_models/auth_view_models/subscription_view_model.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/ui/pages/onboarding/subscription/account_info.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/listview_items/subscription_item.dart';
import 'package:quick_retail_mobile/ui/widgets/show_flush_bar.dart';

class Annual extends ConsumerStatefulWidget {
  const Annual({super.key});

  @override
  ConsumerState<Annual> createState() => _AnnualState();
}

class _AnnualState extends ConsumerState<Annual> {
  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (ref.read(subscriptionViewModel).annualSubscriptions.isEmpty) {
        ref
            .read(subscriptionViewModel)
            .fetchAvailableSubscriptions(billType: "yearly");
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final colorScheme = Theme.of(context).colorScheme;
    final vm = ref.watch(subscriptionViewModel);
    return ListView(
      padding: EdgeInsets.symmetric(vertical: 24.h),
      children: [
        if (vm.state == ViewState.busy)
          SizedBox(
            height: 120.h,
          ),
        ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemBuilder: (context, index) {
              final subscriptionData = vm.annualSubscriptions[index];
              return SubscriptionItem(
                onPressed: () {
                  if (vm.selectedSubscriptions.contains(subscriptionData)) {
                    vm.selectedSubscriptions.remove(subscriptionData);
                  } else {
                    vm.selectedSubscriptions.add(subscriptionData);
                  }
                  setState(() {});
                },
                subscriptionData: subscriptionData,
                isSelected: vm.selectedSubscriptions.contains(subscriptionData),
                isAnnual: true,
              );
            },
            separatorBuilder: (context, index) {
              return SizedBox(
                height: 16.h,
              );
            },
            itemCount: vm.annualSubscriptions.length),
        //todo::: COnfirm calculation of total price
        // SizedBox(
        //   height: 48.h,
        // ),
        // Container(
        //   padding: EdgeInsets.symmetric(vertical: 32.h, horizontal: 16.w),
        //   margin: EdgeInsets.only(right: 16.w),
        //   decoration: BoxDecoration(
        //       borderRadius: BorderRadius.circular(8.r),
        //       border: Border.all(color: ColorPath.athensGrey)),
        //   child: Row(
        //     mainAxisAlignment: MainAxisAlignment.spaceBetween,
        //     children: [
        //       const ScreenTitle(
        //           title: "Total Price",
        //           subTitle: "Excluding V.A.T or related tax"),
        //       SizedBox(
        //         width: 24.w,
        //       ),
        //       Expanded(
        //         child: NairaDisplay(
        //           amount: 5000,
        //           color: colorScheme.text6,
        //           fontWeight: FontWeight.w900,
        //         ),
        //       )
        //     ],
        //   ),
        // ),
        SizedBox(
          height: 48.h,
        ),
        CustomButton(
          onPressed: () {
            if (vm.selectedSubscriptions.isEmpty) {
              showFlushBar(
                  context: context,
                  message: "Kindly Select at least one Plan!",
                  success: false);
              return;
            }
            pushNavigation(
                context: context,
                widget: const AccountInfo(),
                routeName: NamedRoutes.accountSetup);
          },
          buttonText: "Continue",
        )
      ],
    );
  }
}
