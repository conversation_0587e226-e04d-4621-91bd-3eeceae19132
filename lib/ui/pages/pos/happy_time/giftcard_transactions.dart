import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/pages/pos/transaction_management/view_transaction.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/color_tag.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_icon.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';

class GiftcardTransactions extends StatefulWidget {
  const GiftcardTransactions({super.key});

  @override
  State<GiftcardTransactions> createState() => _GiftcardTransactionsState();
}

class _GiftcardTransactionsState extends State<GiftcardTransactions> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
        context: context,
        // preferredHeight: 16,
        title: "Gift Card Transaction",
        centerTitle: true,
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(vertical: 24.h,horizontal: 16.w),
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    "Transaction",
                    style: textTheme.titleMedium,
                  ),
                  SizedBox(
                    width: 4.w,
                  ),
                  ColorTag(
                    color: ColorPath.flamingo.withOpacity(.2),
                    child: Text(
                      '100',
                      style: textTheme.bodySmall
                          ?.copyWith(color: ColorPath.flamingo),
                    ),
                  )
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: CustomIcon(
                  imageAsset: Utilities.getSvg("filter"),
                  bgColor: ColorPath.flamingo,
                  onPressed: () {
                    // bottomSheetWrapper(
                    //     context: context,
                    //     child: const StoreFilterBottomsheet());
                  },
                ),
              ),
            ],
          ),
          CustomTextField(
            isCompulsory: false,
            enabled: false,
            hintText: 'Enter key word',
            prefixIcon: Padding(
              padding: const EdgeInsets.only(left: 8.0, right: 8.0),
              child: SvgPicture.asset(Utilities.getSvg('search')),
            ),
          ),
          ListView.separated(
              padding: EdgeInsets.symmetric(vertical: 32.h),
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemBuilder: (context, index) {
                return Clickable(
                  onPressed: () {
                    pushNavigation(context: context, widget: ViewTransaction());
                  },
                  child: Container(
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(color: ColorPath.athensGrey)),
                    child: Row(
                      children: [
                        Expanded(
                            child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "Valentine Gift Card",
                              style: textTheme.bodySmall
                                  ?.copyWith(fontWeight: FontWeight.w500),
                            ),
                            SizedBox(
                              height: 8.h,
                            ),
                            Text.rich(
                              TextSpan(text: "Date: ", children: [
                                TextSpan(
                                    text: "April 11, 2025",
                                    style: textTheme.bodySmall?.copyWith(
                                        color: colorScheme.text4,
                                        fontWeight: FontWeight.bold))
                              ]),
                              style: textTheme.bodySmall
                                  ?.copyWith(color: colorScheme.subTextSecondary),
                            )
                          ],
                        )),
                        SizedBox(
                          width: 12.w,
                        ),
                        Expanded(
                            child: Column(
                          crossAxisAlignment: CrossAxisAlignment.end,
                          children: [
                            NairaDisplay(
                              amount: double.parse("200789.90"),
                              fontSize: 16.sp,
                              color: colorScheme.text4,
                            ),
                            SizedBox(
                              height: 8.h,
                            ),
                            ColorTag(
                              color: 1 + 1 == 3
                                  ? ColorPath.earlyDawn
                                  : ColorPath.foamGreen,
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Icon(
                                    Icons.circle,
                                    size: 6,
                                    color: 1 + 1 == 3
                                        ? ColorPath.californiaOrange
                                        : ColorPath.meadowGreen,
                                  ),
                                  SizedBox(
                                    width: 8.w,
                                  ),
                                  Text(
                                    "Successful",
                                    style: textTheme.bodySmall?.copyWith(
                                        color: 1 + 1 == 3
                                            ? ColorPath.vesuvius
                                            : ColorPath.funGreen),
                                  ),
                                ],
                              ),
                            )
                          ],
                        )),
                      ],
                    ),
                  ),
                );
              },
              separatorBuilder: (context, index) {
                return SizedBox(
                  height: 22.h,
                );
              },
              itemCount: 10),
        ],
      ),
    );
  }
}
