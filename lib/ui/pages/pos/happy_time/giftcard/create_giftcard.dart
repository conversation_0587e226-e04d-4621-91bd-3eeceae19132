import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';

class CreateGiftcard extends StatefulWidget {
  const CreateGiftcard({super.key});

  @override
  State<CreateGiftcard> createState() => _CreateGiftcardState();
}

class _CreateGiftcardState extends State<CreateGiftcard> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
        context: context,
        // preferredHeight: 16,
        title: "Create Gift Card",
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
              text: 'CREATE GIFT CARD',
              fontWeight: FontWeight.w500,
              fontColor: colorScheme.text4,
            ),
            SizedBox(
              height: 24.h,
            ),
            const CustomTextField(
              label: 'Gift Card Name',
              hintText: 'Enter name',
              isCompulsory: false,
            ),
            SizedBox(
              height: 24.h,
            ),
            const CustomTextField(
              label: 'Gift Card Value',
              hintText: 'Enter value',
              isCompulsory: false,
            ),
            SizedBox(
              height: 24.h,
            ),
            Clickable(
              onPressed: () {},
              child: CustomTextField(
                label: 'Product Applicable',
                hintText: 'Select Option',
                isCompulsory: false,
                enabled: false,
                suffixIcon: Padding(
                  padding: EdgeInsets.only(right: 12.w),
                  child: const Icon(
                    Icons.keyboard_arrow_down,
                    size: 16,
                  ),
                ),
              ),
            ),
            SizedBox(
              height: 24.h,
            ),
            Row(
              children: [
                Expanded(
                  child: Clickable(
                    onPressed: () {
                      showDatePicker(
                          context: context,
                          firstDate: DateTime(2000),
                          lastDate: DateTime.now());
                    },
                    child: CustomTextField(
                      label: 'Validity Start Date',
                      hintText: 'Select Date',
                      enabled: false,
                      isCompulsory: false,
                      suffixIcon: Padding(
                        padding: EdgeInsets.only(right: 12.w),
                        child: Icon(
                          Icons.calendar_month,
                          color: colorScheme.text5,
                        ),
                      ),
                    ),
                  ),
                ),
                SizedBox(
                  width: 16.w,
                ),
                Expanded(
                  child: Clickable(
                    onPressed: () {
                      showDatePicker(
                          context: context,
                          firstDate: DateTime(2000),
                          lastDate: DateTime.now());
                    },
                    child: CustomTextField(
                      label: 'Validity End Date',
                      hintText: 'Select Date',
                      enabled: false,
                      isCompulsory: false,
                      suffixIcon: Padding(
                        padding: EdgeInsets.only(right: 12.w),
                        child: Icon(
                          Icons.calendar_month,
                          color: colorScheme.text5,
                        ),
                      ),
                    ),
                  ),
                ),
              ],
            ),
            SizedBox(
              height: 24.h,
            ),
            SizedBox(
              height: 32.h,
            ),
            CustomButton(
              onPressed: () {
                // bottomSheetWrapper(
                //     context: context,
                //     child: CustomBottomSheet(
                //       title: "Create Discount ?",
                //       subTitle:
                //           "Are you sure you want to Create this discount with it associated details?",
                //       firstButtonText: "Yes, Create Discount",
                //       secondButtonText: "No, Close",
                //       onPressedFirst: () {},
                //       onPressedSecond: () {},
                //     ));

                // bottomSheetWrapper(
                //     context: context,
                //     child: CustomBottomSheet(
                //       title: "Discount Created",
                //       subTitle:
                //           "Congratulations, you have successfully Created a discount",
                //       firstButtonText: "Manage all Discount",
                //       secondButtonText: "Create New Discount ",
                //       onPressedFirst: () {},
                //       onPressedSecond: () {},
                //     ));
              },
              buttonText: "Create GiftCard",
            )
          ],
        ),
      ),
    );
  }
}
