import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/pages/pos/happy_time/happy_time_dashboard.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/take_discount_action_bottomsheet.dart';
import 'package:quick_retail_mobile/ui/widgets/color_tag.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_icon.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard/dashbaord_empty_state.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard_header.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

class ViewGiftcard extends StatefulWidget {
  const ViewGiftcard({super.key});

  @override
  State<ViewGiftcard> createState() => _ViewGiftcardState();
}

class _ViewGiftcardState extends State<ViewGiftcard> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
          context: context,
          showLeadingIcon: true,
          // leadingWidth: MediaQuery.of(context).size.width / 1.7,
          title: "Summer GiftCard ", // Discount Name
          centerTitle: true,
          actions: [
            Padding(
              padding: EdgeInsets.only(right: 16.w),
              child: Row(
                children: [
                  CustomIcon(
                    imageAsset: Utilities.getSvg("menu"),
                    bgColor: ColorPath.flamingo,
                    onPressed: () {
                      bottomSheetWrapper(
                          context: context,
                          child: TakeDiscountActionBottomsheet());
                    },
                  ),
                  SizedBox(
                    width: 8.w,
                  ),
                ],
              ),
            )
          ]),
      body: ListView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
        children: [
          const ScreenTitle(
              title: "Overview", subTitle: "Key highlighting Giftcard"),
          SizedBox(
            height: 16.w,
          ),
          const HappyTimeOverviewCard(),
          SizedBox(
            height: 32.h,
          ),
          DashBoardHeader(
            title: "GiftCard Transactions",
            subtitle: "Manage your giftcard transactions with ease",
            titleTagText: "08",
            padding: EdgeInsets.zero,
            titleTagColor: ColorPath.flamingo.withOpacity(.25),
            showCTA: false,
          ),
          1 + 1 == 3
              ? Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: DashBoardEmptyState(
                    title: "No Data",
                    margin: EdgeInsets.zero,
                    subTitle:
                        "No Transaction. Add new product to start transacting",
                    buttonText: "Add New Product",
                    onPressed: () {},
                  ),
                )
              : ListView.separated(
                  padding: EdgeInsets.only(top: 16.h),
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return Container(
                      padding: EdgeInsets.all(16.w),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8.r),
                          border: Border.all(color: ColorPath.athensGrey)),
                      child: Row(
                        children: [
                          Expanded(
                              child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "#3904",
                                style: textTheme.bodySmall
                                    ?.copyWith(fontWeight: FontWeight.w500),
                              ),
                              SizedBox(
                                height: 8.h,
                              ),
                              Text.rich(
                                TextSpan(text: "Date: ", children: [
                                  TextSpan(
                                      text: "April 11, 2025",
                                      style: textTheme.bodySmall?.copyWith(
                                          color: colorScheme.text4,
                                          fontWeight: FontWeight.bold))
                                ]),
                                style: textTheme.bodySmall?.copyWith(
                                    color: colorScheme.subTextSecondary),
                              )
                            ],
                          )),
                          SizedBox(
                            width: 12.w,
                          ),
                          Expanded(
                              child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              NairaDisplay(
                                amount: double.parse("200789.90"),
                                fontSize: 16.sp,
                                color: colorScheme.text4,
                              ),
                              SizedBox(
                                height: 8.h,
                              ),
                              ColorTag(
                                color: 1 + 1 == 3
                                    ? ColorPath.earlyDawn
                                    : ColorPath.foamGreen,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.circle,
                                      size: 6,
                                      color: 1 + 1 == 3
                                          ? ColorPath.californiaOrange
                                          : ColorPath.meadowGreen,
                                    ),
                                    SizedBox(
                                      width: 8.w,
                                    ),
                                    Text(
                                      "Successful",
                                      style: textTheme.bodySmall?.copyWith(
                                          color: 1 + 1 == 3
                                              ? ColorPath.vesuvius
                                              : ColorPath.funGreen),
                                    ),
                                  ],
                                ),
                              )
                            ],
                          )),
                        ],
                      ),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(
                      height: 16.h,
                    );
                  },
                  itemCount: 3)
        ],
      ),
    );
  }
}
