import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/pages/pos/happy_time/discount/create_discount.dart';
import 'package:quick_retail_mobile/ui/pages/pos/happy_time/discount/manage_discounts.dart';
import 'package:quick_retail_mobile/ui/pages/pos/happy_time/discount_transactions.dart';
import 'package:quick_retail_mobile/ui/pages/pos/happy_time/giftcard/create_giftcard.dart';
import 'package:quick_retail_mobile/ui/pages/pos/happy_time/giftcard/manage_giftcard.dart';
import 'package:quick_retail_mobile/ui/pages/pos/happy_time/giftcard_transactions.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/color_tag.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard/dashbaord_empty_state.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard_header.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

class HappyTimeDashboard extends StatefulWidget {
  const HappyTimeDashboard({super.key});

  @override
  State<HappyTimeDashboard> createState() => _HappyTimeDashboardState();
}

class _HappyTimeDashboardState extends State<HappyTimeDashboard> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
        context: context,
        // preferredHeight: 16,
        title: "Happy Time",
        centerTitle: true,
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
        children: [
          const ScreenTitle(
              title: "Overview",
              subTitle: "Key highlighting of Discount and Gift Card"),
          SizedBox(
            height: 16.h,
          ),
          const HappyTimeOverviewCard(),
          SizedBox(
            height: 36.h,
          ),
          const ScreenTitle(
              title: "Quick Action", subTitle: "Take Quick actions today"),
          SizedBox(
            height: 16.h,
          ),
          // QuickActions
          Column(
            children: [
              Row(
                children: [
                  Expanded(
                    child: Clickable(
                      onPressed: () {
                        pushNavigation(
                            context: context,
                            widget: const CreateGiftcard(),
                            routeName: NamedRoutes.createGiftcard);
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 18.w, vertical: 14.h),
                        decoration: BoxDecoration(
                            color: ColorPath.flamingo.withOpacity(.15),
                            borderRadius: BorderRadius.circular(8.r)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SvgPicture.asset(Utilities.getSvg("stackedSquare")),
                            SizedBox(
                              width: 8.w,
                            ),
                            Text(
                              "New GiftCard",
                              style: textTheme.bodySmall?.copyWith(
                                  color: colorScheme.text6,
                                  fontWeight: FontWeight.w500),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                  SizedBox(
                    width: 16.w,
                  ),
                  Expanded(
                    child: Clickable(
                      onPressed: () {
                        pushNavigation(
                            context: context,
                            widget: const CreateDiscount(),
                            routeName: NamedRoutes.createDiscount);
                      },
                      child: Container(
                        padding: EdgeInsets.symmetric(
                            horizontal: 18.w, vertical: 14.h),
                        decoration: BoxDecoration(
                            color: ColorPath.flamingo.withOpacity(.15),
                            borderRadius: BorderRadius.circular(8.r)),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            SvgPicture.asset(Utilities.getSvg("stackedSquare")),
                            SizedBox(
                              width: 8.w,
                            ),
                            Text(
                              "New Discount",
                              style: textTheme.bodySmall?.copyWith(
                                  color: colorScheme.text6,
                                  fontWeight: FontWeight.w500),
                            )
                          ],
                        ),
                      ),
                    ),
                  ),
                ],
              ),
              if (1 + 1 == 2) //conditional render
                Column(
                  children: [
                    SizedBox(
                      height: 16.h,
                    ),
                    Row(
                      children: [
                        Expanded(
                          child: Clickable(
                            onPressed: () {
                              pushNavigation(
                                  context: context,
                                  widget: const ManageGiftcard(),
                                  routeName: NamedRoutes.createGiftcard);
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 18.w, vertical: 14.h),
                              decoration: BoxDecoration(
                                  color: ColorPath.flamingo.withOpacity(.15),
                                  borderRadius: BorderRadius.circular(8.r)),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                      Utilities.getSvg("stackedSquare")),
                                  SizedBox(
                                    width: 8.w,
                                  ),
                                  Text(
                                    "Manage GiftCard",
                                    style: textTheme.bodySmall?.copyWith(
                                        color: colorScheme.text6,
                                        fontWeight: FontWeight.w500),
                                  )
                                ],
                              ),
                            ),
                          ),
                        ),
                        SizedBox(
                          width: 16.w,
                        ),
                        Expanded(
                          child: Clickable(
                            onPressed: () {
                              pushNavigation(
                                  context: context,
                                  widget: const ManageDiscounts(),
                                  routeName: NamedRoutes.createDiscount);
                            },
                            child: Container(
                              padding: EdgeInsets.symmetric(
                                  horizontal: 18.w, vertical: 14.h),
                              decoration: BoxDecoration(
                                  color: ColorPath.flamingo.withOpacity(.15),
                                  borderRadius: BorderRadius.circular(8.r)),
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  SvgPicture.asset(
                                      Utilities.getSvg("stackedSquare")),
                                  SizedBox(
                                    width: 8.w,
                                  ),
                                  Text(
                                    "Manage Discount",
                                    style: textTheme.bodySmall?.copyWith(
                                        color: colorScheme.text6,
                                        fontWeight: FontWeight.w500),
                                  )
                                ],
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
            ],
          ),
          SizedBox(
            height: 32.h,
          ),
          DashBoardHeader(
            title: "Discount Transactions",
            subtitle: "Manage your transaction with ease",
            titleTagText: "30",
            padding: EdgeInsets.zero,
            titleTagColor: ColorPath.flamingo.withOpacity(.25),
            onPressed: () {
              pushNavigation(
                  context: context,
                  widget: const DiscountTransactions(),
                  routeName: NamedRoutes.discountTransactions);
            },
            showCTA: 1 + 1 == 2,
            child: Row(
              children: [
                Text(
                  "View All",
                  style: textTheme.bodySmall,
                ),
                Icon(
                  Icons.north_east,
                  size: 14.w,
                  color: ColorPath.flamingo,
                )
              ],
            ),
          ),
          1 + 1 == 3
              ? Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: DashBoardEmptyState(
                    title: "No Data",
                    margin: EdgeInsets.zero,
                    subTitle:
                        "No Transaction. Add new product to start transacting",
                    buttonText: "Add New Product",
                    onPressed: () {},
                  ),
                )
              : ListView.separated(
                  padding: EdgeInsets.only(top: 16.h),
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return Container(
                      padding: EdgeInsets.all(16.w),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8.r),
                          border: Border.all(color: ColorPath.athensGrey)),
                      child: Row(
                        children: [
                          Expanded(
                              child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "#6784",
                                style: textTheme.bodySmall
                                    ?.copyWith(fontWeight: FontWeight.w500),
                              ),
                              SizedBox(
                                height: 8.h,
                              ),
                              Text.rich(
                                TextSpan(text: "Date: ", children: [
                                  TextSpan(
                                      text: "April 11, 2025",
                                      style: textTheme.bodySmall?.copyWith(
                                          color: colorScheme.text4,
                                          fontWeight: FontWeight.bold))
                                ]),
                                style: textTheme.bodySmall?.copyWith(
                                    color: colorScheme.subTextSecondary),
                              )
                            ],
                          )),
                          SizedBox(
                            width: 12.w,
                          ),
                          Expanded(
                              child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              NairaDisplay(
                                amount: double.parse("200789.90"),
                                fontSize: 16.sp,
                                color: colorScheme.text4,
                              ),
                              SizedBox(
                                height: 8.h,
                              ),
                              ColorTag(
                                color: 1 + 1 == 3
                                    ? ColorPath.earlyDawn
                                    : ColorPath.foamGreen,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.circle,
                                      size: 6,
                                      color: 1 + 1 == 3
                                          ? ColorPath.californiaOrange
                                          : ColorPath.meadowGreen,
                                    ),
                                    SizedBox(
                                      width: 8.w,
                                    ),
                                    Text(
                                      "Successful",
                                      style: textTheme.bodySmall?.copyWith(
                                          color: 1 + 1 == 3
                                              ? ColorPath.vesuvius
                                              : ColorPath.funGreen),
                                    ),
                                  ],
                                ),
                              )
                            ],
                          )),
                        ],
                      ),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(
                      height: 16.h,
                    );
                  },
                  itemCount: 2),
          SizedBox(
            height: 32.h,
          ),
          DashBoardHeader(
            title: "GiftCard Transactions",
            subtitle: "Manage your transaction with ease",
            titleTagText: "30",
            padding: EdgeInsets.zero,
            titleTagColor: ColorPath.flamingo.withOpacity(.25),
            onPressed: () {
              pushNavigation(
                  context: context,
                  widget: const GiftcardTransactions(),
                  routeName: NamedRoutes.giftcardTransactions);
            },
            showCTA: 1 + 1 == 2,
            child: Row(
              children: [
                Text(
                  "View All",
                  style: textTheme.bodySmall,
                ),
                Icon(
                  Icons.north_east,
                  size: 14.w,
                  color: ColorPath.flamingo,
                )
              ],
            ),
          ),
          1 + 1 == 3
              ? Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: DashBoardEmptyState(
                    title: "No Data",
                    margin: EdgeInsets.zero,
                    subTitle:
                        "No Transaction. Add new product to start transacting",
                    buttonText: "Add New Product",
                    onPressed: () {},
                  ),
                )
              : ListView.separated(
                  padding: EdgeInsets.only(top: 16.h),
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return Container(
                      padding: EdgeInsets.all(16.w),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8.r),
                          border: Border.all(color: ColorPath.athensGrey)),
                      child: Row(
                        children: [
                          Expanded(
                              child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                "Valentine Gift Card",
                                style: textTheme.bodySmall
                                    ?.copyWith(fontWeight: FontWeight.w500),
                              ),
                              SizedBox(
                                height: 8.h,
                              ),
                              Text.rich(
                                TextSpan(text: "Date: ", children: [
                                  TextSpan(
                                      text: "April 11, 2025",
                                      style: textTheme.bodySmall?.copyWith(
                                          color: colorScheme.text4,
                                          fontWeight: FontWeight.bold))
                                ]),
                                style: textTheme.bodySmall?.copyWith(
                                    color: colorScheme.subTextSecondary),
                              )
                            ],
                          )),
                          SizedBox(
                            width: 12.w,
                          ),
                          Expanded(
                              child: Column(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              NairaDisplay(
                                amount: double.parse("200789.90"),
                                fontSize: 16.sp,
                                color: colorScheme.text4,
                              ),
                              SizedBox(
                                height: 8.h,
                              ),
                              ColorTag(
                                color: 1 + 1 == 3
                                    ? ColorPath.earlyDawn
                                    : ColorPath.foamGreen,
                                child: Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Icon(
                                      Icons.circle,
                                      size: 6,
                                      color: 1 + 1 == 3
                                          ? ColorPath.californiaOrange
                                          : ColorPath.meadowGreen,
                                    ),
                                    SizedBox(
                                      width: 8.w,
                                    ),
                                    Text(
                                      "Successful",
                                      style: textTheme.bodySmall?.copyWith(
                                          color: 1 + 1 == 3
                                              ? ColorPath.vesuvius
                                              : ColorPath.funGreen),
                                    ),
                                  ],
                                ),
                              )
                            ],
                          )),
                        ],
                      ),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(
                      height: 16.h,
                    );
                  },
                  itemCount: 3)
        ],
      ),
    );
  }
}

class HappyTimeOverviewCard extends StatelessWidget {
  const HappyTimeOverviewCard({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
          color: ColorPath.flamingo.withOpacity(.08),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: ColorPath.flamingoRed)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "TOTAL DISCOUNT VALUE",
            style: textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600, color: colorScheme.text7),
          ),
          SizedBox(
            height: 8.h,
          ),
          NairaDisplay(
            amount: 280390,
            color: colorScheme.text6,
          ),
          SizedBox(
            height: 8.h,
          ),
          Row(
            children: [
              ColorTag(
                color: ColorPath.emeraldGreen,
                child: Row(
                  children: [
                    Text(
                      '0.9%',
                      style: textTheme.bodySmall?.copyWith(color: Colors.white),
                    ),
                    SizedBox(
                      width: 2.w,
                    ),
                    const Icon(
                      Icons.north_east,
                      size: 12,
                      color: Colors.white,
                    )
                  ],
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Text(
                'last 3 days',
                style: textTheme.bodySmall,
              )
            ],
          ),
          SizedBox(
            height: 16.h,
          ),
          Row(
            children: [
              Expanded(
                child: ScreenTitle(
                  title: 'Number of Use ',
                  titleSize: 14.sp,
                  titleFontWeight: FontWeight.w400,
                  subtitleFontWeight: FontWeight.w600,
                  subtitleColor: ColorPath.salemGreen,
                  subTitle: "504 Times",
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Expanded(
                child: ScreenTitle(
                    title: 'Product Applicable',
                    titleSize: 14.sp,
                    titleFontWeight: FontWeight.w400,
                    subtitleFontWeight: FontWeight.w600,
                    subtitleColor: ColorPath.alizarinRed,
                    subTitle: "13 Products"),
              ),
            ],
          )
        ],
      ),
    );
  }
}
