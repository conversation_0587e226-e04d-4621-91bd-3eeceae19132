import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/pages/pos/store_management/create_store.dart';
import 'package:quick_retail_mobile/ui/pages/pos/store_management/view_store.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/color_tag.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_icon.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard/dashbaord_empty_state.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard_header.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

class StoreManagement extends StatefulWidget {
  const StoreManagement({super.key});

  @override
  State<StoreManagement> createState() => _StoreManagementState();
}

class _StoreManagementState extends State<StoreManagement> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
          context: context,
          showLeadingIcon: true,
          title: "Store Management", // Discount Name
          centerTitle: true,
          actions: [
            Padding(
              padding: EdgeInsets.only(right: 16.w),
              child: Row(
                children: [
                  CustomIcon(
                    imageAsset: Utilities.getSvg("addSquare"),
                    bgColor: ColorPath.flamingo,
                    onPressed: () {
                      // bottomSheetWrapper(
                      //     context: context,
                      //     child: TakeDiscountActionBottomsheet());
                      pushNavigation(
                          context: context,
                          widget: const CreateStore(),
                          routeName: NamedRoutes.createStore);
                    },
                  ),
                  SizedBox(
                    width: 8.w,
                  ),
                ],
              ),
            )
          ]),
      body: ListView(
        padding: EdgeInsets.symmetric(vertical: 24.h, horizontal: 16.w),
        children: [
          const DiscountOverviewCard(),
          SizedBox(
            height: 32.h,
          ),
          DashBoardHeader(
            title: "List of Stores",
            subtitle: "Manage your store with ease",
            titleTagText: "08",
            padding: EdgeInsets.zero,
            titleTagColor: ColorPath.flamingo.withOpacity(.25),
            showCTA: false,
          ),
          1 + 1 == 3
              ? Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: DashBoardEmptyState(
                    title: "No Store Data",
                    margin: EdgeInsets.zero,
                    subTitle: "You currently do now have any Store added",
                    buttonText: "Add New Store",
                    onPressed: () {},
                  ),
                )
              : ListView.separated(
                  padding: EdgeInsets.only(top: 16.h),
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return Clickable(
                      onPressed: () {
                        pushNavigation(
                            context: context, widget: const ViewStore());
                      },
                      child: Container(
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.r),
                            border: Border.all(color: ColorPath.athensGrey)),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Row(
                              children: [
                                Expanded(
                                    child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      "Summer Store",
                                      style: textTheme.bodySmall?.copyWith(
                                          fontWeight: FontWeight.w500),
                                    ),
                                    SizedBox(
                                      height: 8.h,
                                    ),
                                    Text.rich(
                                      TextSpan(
                                          text: "No. of Customers: ",
                                          children: [
                                            TextSpan(
                                                text: "290",
                                                style: textTheme.bodySmall
                                                    ?.copyWith(
                                                        color:
                                                            colorScheme.text4,
                                                        fontWeight:
                                                            FontWeight.bold))
                                          ]),
                                      style: textTheme.bodySmall?.copyWith(
                                          color: colorScheme.subTextSecondary),
                                    ),
                                  ],
                                )),
                                SizedBox(
                                  width: 12.w,
                                ),
                                Expanded(
                                    child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.end,
                                  children: [
                                    Text(
                                      "Store Revenue",
                                      style: textTheme.bodySmall?.copyWith(
                                          color: colorScheme.subTextSecondary),
                                    ),
                                    SizedBox(
                                      height: 8.h,
                                    ),
                                    NairaDisplay(
                                      amount: double.parse("200789.90"),
                                      fontSize: 16.sp,
                                      color: colorScheme.text4,
                                    ),
                                  ],
                                )),
                              ],
                            ),
                            SizedBox(
                              height: 8.h,
                            ),
                            Row(
                              children: [
                                Text(
                                  "Status:",
                                  style: textTheme.bodySmall?.copyWith(
                                      color: colorScheme.subTextSecondary),
                                ),
                                SizedBox(
                                  width: 2.w,
                                ),
                                ColorTag(
                                  color: 1 + 1 == 3
                                      ? ColorPath.earlyDawn
                                      : ColorPath.foamGreen,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const Icon(
                                        Icons.circle,
                                        size: 6,
                                        color: 1 + 1 == 3
                                            ? ColorPath.californiaOrange
                                            : ColorPath.meadowGreen,
                                      ),
                                      SizedBox(
                                        width: 8.w,
                                      ),
                                      Text(
                                        "Active",
                                        style: textTheme.bodySmall?.copyWith(
                                            color: 1 + 1 == 3
                                                ? ColorPath.vesuvius
                                                : ColorPath.funGreen),
                                      ),
                                    ],
                                  ),
                                )
                              ],
                            )
                          ],
                        ),
                      ),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(
                      height: 16.h,
                    );
                  },
                  itemCount: 3)
        ],
      ),
    );
  }
}

// WIDGETS
class DiscountOverviewCard extends StatelessWidget {
  const DiscountOverviewCard({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
          color: ColorPath.flamingo.withOpacity(.08),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: ColorPath.flamingoRed)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "TOTAL STORE REVENUE",
            style: textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600, color: colorScheme.text7),
          ),
          SizedBox(
            height: 8.h,
          ),
          NairaDisplay(
            amount: 280390,
            color: colorScheme.text6,
          ),
          SizedBox(
            height: 8.h,
          ),
          Row(
            children: [
              ColorTag(
                color: ColorPath.emeraldGreen,
                child: Row(
                  children: [
                    Text(
                      '4.9%',
                      style: textTheme.bodySmall?.copyWith(color: Colors.white),
                    ),
                    SizedBox(
                      width: 2.w,
                    ),
                    const Icon(
                      Icons.north_east,
                      size: 12,
                      color: Colors.white,
                    )
                  ],
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Text(
                'last 3 days',
                style: textTheme.bodySmall,
              )
            ],
          ),
          SizedBox(
            height: 16.h,
          ),
          Row(
            children: [
              Expanded(
                child: ScreenTitle(
                  title: 'No. of Store',
                  titleSize: 14.sp,
                  titleFontWeight: FontWeight.w400,
                  subtitleFontWeight: FontWeight.w600,
                  subtitleColor: ColorPath.mirageBlack,
                  subTitle: "1,200",
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Expanded(
                child: ScreenTitle(
                  title: 'Active',
                  titleSize: 14.sp,
                  titleFontWeight: FontWeight.w400,
                  subtitleFontWeight: FontWeight.w600,
                  subtitleColor: ColorPath.salemGreen,
                  subTitle: "1,000,000",
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Expanded(
                child: ScreenTitle(
                    title: 'Inactive',
                    titleSize: 14.sp,
                    titleFontWeight: FontWeight.w400,
                    subtitleFontWeight: FontWeight.w600,
                    subtitleColor: ColorPath.alizarinRed,
                    subTitle: "200"),
              ),
            ],
          )
        ],
      ),
    );
  }
}
