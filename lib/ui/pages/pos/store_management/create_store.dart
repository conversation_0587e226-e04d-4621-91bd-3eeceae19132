import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';

class CreateStore extends StatefulWidget {
  const CreateStore({super.key});

  @override
  State<CreateStore> createState() => _CreateStoreState();
}

class _CreateStoreState extends State<CreateStore> {
  @override
  Widget build(BuildContext context) {
    // final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
        context: context,
        title: "Create new Store",
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 32.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
              text: 'CREATE STORE',
              fontWeight: FontWeight.w500,
              fontColor: colorScheme.text4,
            ),
            SizedBox(
              height: 24.h,
            ),
            const CustomTextField(
              label: 'Store Name',
              hintText: 'Enter store name',
            ),
            SizedBox(
              height: 24.h,
            ),
            const CustomTextField(
              label: 'GLA',
              hintText: 'Enter value',
            ),
            SizedBox(
              height: 24.h,
            ),
            const CustomTextField(
              label: 'GSA',
              hintText: 'Enter value',
            ),
            SizedBox(
              height: 24.h,
            ),
            const CustomTextField(
              label: 'Store ID',
              hintText: 'Enter value',
            ),
            SizedBox(
              height: 24.h,
            ),
            const CustomTextField(
              label: 'Number of Staffs',
              hintText: 'Enter value',
            ),
            SizedBox(
              height: 24.h,
            ),
            Clickable(
              onPressed: () {},
              child: const CustomTextField(
                label: 'Country',
                hintText: 'Select Country',
                enabled: false,
                suffixIcon: Padding(
                  padding: EdgeInsets.only(right: 16.0),
                  child: Icon(Icons.keyboard_arrow_down_outlined),
                ),
              ),
            ),
            SizedBox(
              height: 24.h,
            ),
            Clickable(
              onPressed: () {},
              child: const CustomTextField(
                label: 'State',
                hintText: ' Select State',
                enabled: false,
                suffixIcon: Padding(
                  padding: EdgeInsets.only(right: 16.0),
                  child: Icon(Icons.keyboard_arrow_down_outlined),
                ),
              ),
            ),
            SizedBox(
              height: 24.h,
            ),
            Clickable(
              onPressed: () {},
              child: const CustomTextField(
                label: 'Local Government',
                hintText: ' Select Local Government',
                enabled: false,
                suffixIcon: Padding(
                  padding: EdgeInsets.only(right: 16.0),
                  child: Icon(Icons.keyboard_arrow_down_outlined),
                ),
              ),
            ),
            SizedBox(
              height: 24.h,
            ),
            const CustomTextField(
              label: 'Full Address ',
              hintText: 'Enter Address',
              // isCompulsory: false,
            ),
            SizedBox(
              height: 24.h,
            ),
            Container(
              height: 1,
              color: ColorPath.athensGrey,
            ),
            SizedBox(
              height: 24.h,
            ),
            Clickable(
              onPressed: () {},
              child: const CustomTextField(
                label: 'Store Target period',
                hintText: ' Select Period',
                enabled: false,
                suffixIcon: Padding(
                  padding: EdgeInsets.only(right: 16.0),
                  child: Icon(Icons.keyboard_arrow_down_outlined),
                ),
              ),
            ),
            SizedBox(
              height: 24.h,
            ),
            const CustomTextField(
              label: 'Store Target Value  ',
              hintText: 'Enter Value',
              prefixIcon: Padding(
                padding: EdgeInsets.only(left: 12, right: 12),
                child: Text("N"),
              ),
              // isCompulsory: false,
            ),
            SizedBox(
              height: 48.h,
            ),
            CustomButton(
              onPressed: () {
                // pushNavigation(
                //     context: context,
                //     widget: ProductPricingAndInventoryInformation(),
                //     routeName: NamedRoutes.productPricingAndInventory);
              },
              bgColor:
                  1 + 1 == 2 ? ColorPath.inactiveFlamingo : ColorPath.flamingo,
              buttonText: "Create Store",
            ),
            if (1 + 1 == 2) // if Take Action
              SizedBox(
                height: 163.h,
              )
          ],
        ),
      ),
    );
  }
}
