import 'dart:ffi';

import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/pages/pos/product_management/product_variation_information.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/custom_bottom_sheet.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/product_image_widget.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

class VariationInventoryInformation extends StatefulWidget {
  const VariationInventoryInformation({super.key});

  @override
  State<VariationInventoryInformation> createState() =>
      _VariationInventoryInformationState();
}

class _VariationInventoryInformationState
    extends State<VariationInventoryInformation> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
        appBar: customAppBar(
          context: context,
          // preferredHeight: 16,
          title: "Add a Variation Product",
          centerTitle: true,
        ),
        body: SingleChildScrollView(
            padding: EdgeInsets.symmetric(vertical: 32.h, horizontal: 16.w),
            child: Column(
              children: [
                const ScreenTitle(title: "INVENTORY INFORMATION", subTitle: ""),
                Container(
                  height: 1,
                  color: ColorPath.athensGrey,
                ),
                ListView.separated(
                    padding: EdgeInsets.only(top: 24.h, bottom: 24.h),
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      return const InventoryItem();
                    },
                    separatorBuilder: (context, index) {
                      return SizedBox(
                        height: 16.h,
                      );
                    },
                    itemCount: 3),
                CustomButton(
                  onPressed: () {
                    // create variation prompt
                    // bottomSheetWrapper(
                    //     context: context,
                    //     child: CustomBottomSheet(
                    //       title: "Create Variation Product ?",
                    //       subTitle:
                    //           "Are you sure you want to Create this new product inclusive of it detail and variations ?",
                    //       firstButtonText: "Yes, Create Variation Product",
                    //       secondButtonText: "No, Close",
                    //       onPressedFirst: () {},
                    //       onPressedSecond: () {
                    //         popNavigation(context: context);
                    //       },
                    //     ));
                    //
                    // new product created prompt
                    bottomSheetWrapper(
                        context: context,
                        child: CustomBottomSheet(
                          title: "New Product Created",
                          subTitle:
                              "Congratulations, you have successfully created a new product",
                          firstButtonText: "Manage all Product",
                          secondButtonText: "Create New Product",
                          onPressedFirst: () {},
                          onPressedSecond: () {
                            popNavigation(context: context);
                          },
                        ));
                  },
                  buttonText: "Create Variation Product",
                )
              ],
            )));
  }
}

class InventoryItem extends StatefulWidget {
  const InventoryItem({
    super.key,
  });

  @override
  State<InventoryItem> createState() => _InventoryItemState();
}

class _InventoryItemState extends State<InventoryItem> {
  bool isExpanded = false;
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        border: Border.all(color: ColorPath.athensGrey),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Clickable(
            onPressed: () {
              setState(() {
                isExpanded = !isExpanded;
              });
            },
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Row(
                  children: [
                    const ProductImageWidget(),
                    SizedBox(
                      width: 8.w,
                    ),
                    Text(
                      'Ivory Shade | Small',
                      style: textTheme.bodySmall
                          ?.copyWith(fontWeight: FontWeight.w500),
                    )
                  ],
                ),
                Icon(
                  isExpanded
                      ? Icons.keyboard_arrow_up_rounded
                      : Icons.keyboard_arrow_down_rounded,
                  color: ColorPath.mirageBlack,
                  weight: 1.5,
                )
              ],
            ),
          ),
          if (isExpanded)
            Column(
              children: [
                SizedBox(
                  height: 16.h,
                ),
                Column(
                  children: [
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "Available Quantity:",
                          style: textTheme.bodySmall
                              ?.copyWith(color: colorScheme.subTextPrimary),
                        ),
                        Container(
                          width: 120,
                          padding: EdgeInsets.symmetric(
                              vertical: 8.w, horizontal: 12.w),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.r),
                              border: Border.all(color: ColorPath.athensGrey)),
                          // todo::: confirm if text is editable and update to textfield if true.
                          child: Text("32"),
                        )
                      ],
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "Cost Price:",
                          style: textTheme.bodySmall
                              ?.copyWith(color: colorScheme.subTextPrimary),
                        ),
                        Container(
                          width: 120,
                          padding: EdgeInsets.symmetric(
                              vertical: 8.w, horizontal: 12.w),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.r),
                              border: Border.all(color: ColorPath.athensGrey)),
                          // todo::: confirm if text is editable and update to textfield if true.
                          child: Text("N 14,000"),
                        )
                      ],
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "Selling Price:",
                          style: textTheme.bodySmall
                              ?.copyWith(color: colorScheme.subTextPrimary),
                        ),
                        Container(
                          width: 120,
                          padding: EdgeInsets.symmetric(
                              vertical: 8.w, horizontal: 12.w),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.r),
                              border: Border.all(color: ColorPath.athensGrey)),
                          // todo::: confirm if text is editable and update to textfield if true.
                          child: Text("N 14,000"),
                        )
                      ],
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          "Reorder Level:",
                          style: textTheme.bodySmall
                              ?.copyWith(color: colorScheme.subTextPrimary),
                        ),
                        Container(
                          width: 120,
                          padding: EdgeInsets.symmetric(
                              vertical: 8.w, horizontal: 12.w),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.r),
                              border: Border.all(color: ColorPath.athensGrey)),
                          // todo::: confirm if text is editable and update to textfield if true.
                          child: Text("3"),
                        )
                      ],
                    ),
                  ],
                )
              ],
            ),
          Column(
            children: [
              SizedBox(
                height: 16.h,
              ),
              Clickable(
                onPressed: () {
                  //todo::: Handle delete here
                  bottomSheetWrapper(
                      context: context,
                      child: CustomBottomSheet(
                        title: "Remove Variation Option Type ?",
                        subTitle:
                            "Are you sure you want to remove this Variation option Type ? ",
                        firstButtonText: "Yes, Remove ",
                        secondButtonText: "No, Close",
                        onPressedFirst: () {},
                        onPressedSecond: () {
                          popNavigation(context: context);
                        },
                      ));
                },
                child: Container(
                  padding: EdgeInsets.symmetric(vertical: 8.h),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      color: ColorPath.rose.withOpacity(.2)),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        "Remove this Variated Product",
                        style: textTheme.bodySmall
                            ?.copyWith(color: ColorPath.amaranthRed),
                      ),
                      SizedBox(
                        width: 16.w,
                      ),
                      SvgPicture.asset(Utilities.getSvg('trash'))
                    ],
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
