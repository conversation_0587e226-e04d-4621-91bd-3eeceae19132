// import 'package:flutter/material.dart';
// import 'package:flutter_screenutil/flutter_screenutil.dart';
// import 'package:flutter_svg/svg.dart';
// import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
// import 'package:quick_retail_mobile/core/constants/color_path.dart';
// import 'package:quick_retail_mobile/core/constants/named_routes.dart';
// import 'package:quick_retail_mobile/core/utilities/navigator.dart';
// import 'package:quick_retail_mobile/core/utilities/utilities.dart';
// import 'package:quick_retail_mobile/ui/pages/pos/product_management/variation_inventory_information.dart';
// import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/add_variation_bottomsheet.dart';
// import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
// import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/custom_bottom_sheet.dart';
// import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
// import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
// import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
// import 'package:quick_retail_mobile/ui/widgets/empty_state.dart';
// import 'package:quick_retail_mobile/ui/widgets/pos/product_description_data.dart';
// import 'package:quick_retail_mobile/ui/widgets/pos/product_image_widget.dart';
// import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

// class ProductVariationInformation extends StatefulWidget {
//   const ProductVariationInformation({super.key});

//   @override
//   State<ProductVariationInformation> createState() =>
//       _ProductVariationInformationState();
// }

// class _ProductVariationInformationState
//     extends State<ProductVariationInformation> {
//   @override
//   Widget build(BuildContext context) {
//     final textTheme = Theme.of(context).textTheme;
//     final colorScheme = Theme.of(context).colorScheme;
//     return Scaffold(
//       appBar: customAppBar(
//         context: context,
//         // preferredHeight: 16,
//         title: "Add a Variation Product",
//         centerTitle: true,
//       ),
//       body: 1 + 1 == 3
//           ? Padding(
//               padding: EdgeInsets.symmetric(vertical: 32.h, horizontal: 16.h),
//               child: Column(
//                 children: [
//                   Expanded(
//                     child: Padding(
//                       padding: const EdgeInsets.only(top: 80.0),
//                       child: EmptyState(
//                         imageAsset: Utilities.getSvg('noProduct'),
//                         title: "Add Product Variation (if Any)",
//                         subTitle:
//                             "Add product variation such as color and size with varying prices if any to your product today. ",
//                         buttonText: "Add product variation",
//                         onPressed: () {},
//                       ),
//                     ),
//                   ),
//                   CustomButton(
//                     onPressed: () {},
//                   )
//                 ],
//               ),
//             )
//           : SingleChildScrollView(
//               padding: EdgeInsets.symmetric(vertical: 32.h, horizontal: 16.w),
//               child: Column(
//                 crossAxisAlignment: CrossAxisAlignment.start,
//                 children: [
//                   const ScreenTitle(
//                       title: "PRODUCT VARIATION",
//                       subTitle: "List of Product Variation(s) added so far"),
//                   ListView.separated(
//                       padding: EdgeInsets.only(top: 24.h, bottom: 24.h),
//                       shrinkWrap: true,
//                       physics: const NeverScrollableScrollPhysics(),
//                       itemBuilder: (context, index) {
//                         return Container(
//                           padding: EdgeInsets.all(16.w),
//                           decoration: BoxDecoration(
//                             border: Border.all(color: ColorPath.athensGrey),
//                             borderRadius: BorderRadius.circular(8.r),
//                           ),
//                           child: Column(
//                             crossAxisAlignment: CrossAxisAlignment.start,
//                             children: [
//                               Row(
//                                 mainAxisAlignment:
//                                     MainAxisAlignment.spaceBetween,
//                                 children: [
//                                   Row(
//                                     children: [
//                                       const ProductImageWidget(),
//                                       SizedBox(
//                                         width: 8.w,
//                                       ),
//                                       Text(
//                                         'Varaiant Name',
//                                         style: textTheme.bodySmall?.copyWith(
//                                             fontWeight: FontWeight.w500),
//                                       )
//                                     ],
//                                   ),
//                                   Clickable(
//                                       onPressed: () {
//                                         //todo:: handle delete here
//                                         bottomSheetWrapper(
//                                             context: context,
//                                             child: CustomBottomSheet(
//                                               title: "Remove Variation ?",
//                                               subTitle:
//                                                   "Are you sure you want to remove this product Variation?",
//                                               firstButtonText: "Yes, Remove ",
//                                               secondButtonText: "No, Close",
//                                               onPressedFirst: () {},
//                                               onPressedSecond: () {
//                                                 popNavigation(context: context);
//                                               },
//                                             ));
//                                       },
//                                       child: SvgPicture.asset(
//                                           Utilities.getSvg("trash")))
//                                 ],
//                               ),
//                               SizedBox(
//                                 height: 20.h,
//                               ),
//                               Row(
//                                 mainAxisAlignment:
//                                     MainAxisAlignment.spaceBetween,
//                                 children: [
//                                   const ProductDescriptionData(
//                                     description: "Variation Value",
//                                     data: "Blue, Green, + 3 others",
//                                   ),
//                                   SvgPicture.asset(Utilities.getSvg('edit'))
//                                 ],
//                               )
//                             ],
//                           ),
//                         );
//                       },
//                       separatorBuilder: (context, index) {
//                         return SizedBox(
//                           height: 16.h,
//                         );
//                       },
//                       itemCount: 2),
//                   // ADD NEW BUTTON
//                   Clickable(
//                     onPressed: () {
//                       //todo::: handle add new here
//                       bottomSheetWrapper(
//                           context: context,
//                           child: const AddVariationBottomsheet());
//                     },
//                     child: Container(
//                       padding: EdgeInsets.symmetric(
//                           horizontal: 24.w, vertical: 12.h),
//                       decoration: BoxDecoration(
//                           borderRadius: BorderRadius.circular(8.r),
//                           border: Border.all(color: ColorPath.athensGrey)),
//                       child: Row(
//                         mainAxisAlignment: MainAxisAlignment.center,
//                         mainAxisSize: MainAxisSize.min,
//                         children: [
//                           Text(
//                             "Add New",
//                             style: textTheme.bodyMedium?.copyWith(
//                                 color: colorScheme.subTextSecondary,
//                                 fontWeight: FontWeight.w500),
//                           ),
//                           SizedBox(
//                             width: 12.w,
//                           ),
//                           SvgPicture.asset(Utilities.getSvg("add"))
//                         ],
//                       ),
//                     ),
//                   ),
//                   SizedBox(
//                     height: 32.h,
//                   ),
//                   Container(
//                     height: 1,
//                     color: ColorPath.mischkaGrey,
//                   ),
//                   SizedBox(
//                     height: 32.h,
//                   ),
//                   const ScreenTitle(
//                       title: "Product Variation Pricing",
//                       subTitle:
//                           "Change selling prizes for variation or leave it as default"),
//                   // TODO:::: Confirm how this section should work from Adekunle
//                   ListView.separated(
//                       padding: EdgeInsets.only(top: 24.h, bottom: 24.h),
//                       shrinkWrap: true,
//                       physics: const NeverScrollableScrollPhysics(),
//                       itemBuilder: (context, index) {
//                         return Container(
//                           padding: EdgeInsets.only(bottom: 16.w),
//                           decoration: const BoxDecoration(
//                               border: Border(
//                                   bottom: BorderSide(
//                                       color: ColorPath.mischkaGrey))),
//                           child: Column(
//                             crossAxisAlignment: CrossAxisAlignment.start,
//                             children: [
//                               Row(
//                                 mainAxisAlignment:
//                                     MainAxisAlignment.spaceBetween,
//                                 children: [
//                                   const ProductDescriptionData(
//                                     description: "Color",
//                                     data: "Blue",
//                                   ),
//                                   Clickable(
//                                       onPressed: () {
//                                         //todo:: handle delete here
//                                         bottomSheetWrapper(
//                                             context: context,
//                                             child: CustomBottomSheet(
//                                               title:
//                                                   "Remove Variation Option Type ?",
//                                               subTitle:
//                                                   "Are you sure you want to remove this Variation option Type ? ",
//                                               firstButtonText: "Yes, Remove ",
//                                               secondButtonText: "No, Close",
//                                               onPressedFirst: () {},
//                                               onPressedSecond: () {
//                                                 popNavigation(context: context);
//                                               },
//                                             ));
//                                       },
//                                       child: SvgPicture.asset(
//                                           Utilities.getSvg("trash")))
//                                 ],
//                               ),
//                               SizedBox(
//                                 height: 20.h,
//                               ),
//                               Row(
//                                 mainAxisAlignment:
//                                     MainAxisAlignment.spaceBetween,
//                                 children: List.generate(4, (index) {
//                                   return Column(
//                                     children: [
//                                       Text("Size $index"),
//                                       SizedBox(
//                                         height: 6.h,
//                                       ),
//                                       Container(
//                                         padding: EdgeInsets.all(6.w),
//                                         decoration: BoxDecoration(
//                                           border: Border.all(
//                                               color: ColorPath.athensGrey),
//                                           borderRadius:
//                                               BorderRadius.circular(8.r),
//                                         ),
//                                         child: Text("N 20,000"),
//                                       ),
//                                     ],
//                                   );
//                                 }),
//                               )
//                             ],
//                           ),
//                         );
//                       },
//                       separatorBuilder: (context, index) {
//                         return SizedBox(
//                           height: 16.h,
//                         );
//                       },
//                       itemCount: 5),
//                   SizedBox(
//                     height: 32.h,
//                   ),
//                   CustomButton(onPressed: () {
//                     pushNavigation(
//                         context: context,
//                         widget: const VariationInventoryInformation(),
//                         routeName: NamedRoutes.variationInventory);
//                   })
//                 ],
//               ),
//             ),
//     );
//   }
// }
