// ignore_for_file: use_build_context_synchronously

import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/app_svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/models/create_product_arg.dart';
import 'package:quick_retail_mobile/core/data/models/product_response.dart';
import 'package:quick_retail_mobile/core/data/view_models/product_view_model.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/add_variation_bottomsheet.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/custom_bottom_sheet.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/take_product_action_bottomsheet.dart';
import 'package:quick_retail_mobile/ui/widgets/busy_overlay.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/empty_state.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/product_image_widget.dart';
import 'package:quick_retail_mobile/ui/widgets/show_flush_bar.dart';

class ProductVariationInformation extends ConsumerStatefulWidget {
  const ProductVariationInformation({
    super.key,
    this.existingProductData,
    required this.createProductArg,
  });

  final ProductData? existingProductData;
  final CreateProductArg createProductArg;

  @override
  ConsumerState<ProductVariationInformation> createState() =>
      _ProductVariationInformationState();
}

class _ProductVariationInformationState
    extends ConsumerState<ProductVariationInformation> {
  List<VariationArg> variationArgs = [];

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      setViewProduct();
    });
  }

  setViewProduct() async {
    final prodIdString = (widget.existingProductData?.product?.productId ?? "");
    final productVariations = await ref
        .read(productViewModel)
        .getProductDetailsVariation(prodIdString);

    if (productVariations.isNotEmpty) {
      for (int i = 0; i < productVariations.length; i++) {
        final variation = productVariations[i];
        variationArgs.add(VariationArg(
          image: variation.imagePath,
          costPrice: double.tryParse(variation.costPrice ?? '0'),
          sellingPrice: double.tryParse(variation.sellingPrice ?? '0'),
          quantity: variation.quantityAvailable,
          reorderLevel: int.tryParse(variation.reorderLevel ?? '0'),
          size: variation.variationAttributes
              ?.firstWhere((element) => element.optionType == 'size')
              .optionValue,
          colour: variation.variationAttributes
              ?.firstWhere((element) => element.optionType == 'colour')
              .optionValue,
        ));
      }
      setState(() {});
    }
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return BusyOverlay(
      show: ref.watch(productViewModel).state == ViewState.busy,
      child: Scaffold(
        appBar: customAppBar(
          context: context,
          // preferredHeight: 16,
          title:
              "${widget.existingProductData != null ? "View" : "Add a"} Variation Product",
          centerTitle: true,
        ),
        body: Builder(builder: (context) {
          // todo::: handle for errors as well
          if (variationArgs.isEmpty) {
            return Padding(
              padding: EdgeInsets.symmetric(vertical: 32.h, horizontal: 16.h),
              child: Column(
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsets.only(top: 80.0),
                      child: EmptyState(
                        imageAsset: Utilities.getSvg('noProduct'),
                        title: "Add Product Variation (if Any)",
                        subTitle:
                            "Add product variation such as color and size with varying prices if any to your product today. ",
                        buttonText: "Add product variation",
                        onPressed: () {
                          bottomSheetWrapper(
                            context: context,
                            child: AddVariationBottomsheet(
                              returningValue: (value) {
                                variationArgs.add(value);
                                setState(() {});
                              },
                            ),
                          );
                        },
                      ),
                    ),
                  ),
                  CustomButton(
                    onPressed: () {},
                  ),
                  SizedBox(height: 20.h)
                ],
              ),
            );
          }
          return SingleChildScrollView(
            padding: EdgeInsets.symmetric(vertical: 32.h, horizontal: 16.w),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "VARIANT PRODUCTS",
                  style: textTheme.titleSmall?.copyWith(
                      fontWeight: FontWeight.w500,
                      fontSize: 16.sp,
                      color: colorScheme.subTextSecondary),
                ),
                SizedBox(height: 32.h),
                ListView.separated(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  padding: EdgeInsets.zero,
                  itemBuilder: (ctx, i) {
                    final variation = variationArgs[i];
                    return VariantCard(
                      name: widget.createProductArg.productName ?? "",
                      amtQty: Utilities.formatAmount(
                        addDecimal: false,
                        amount: (variation.quantity ?? 0).toDouble(),
                      ),
                      costPrice: Utilities.formatAmount(
                        addDecimal: false,
                        amount: (variation.costPrice ?? 0).toDouble(),
                      ),
                      sellingPrice: Utilities.formatAmount(
                        addDecimal: false,
                        amount: (variation.sellingPrice ?? 0).toDouble(),
                      ),
                      reorderLevel: variation.reorderLevel?.toString() ?? '',
                      size: variation.size ?? '',
                      colour: variation.colour ?? '',
                      imagePath: variation.image,
                      image:
                          widget.createProductArg.imageFile?.isNotEmpty == true
                              ? (widget.createProductArg.imageFile?[i])
                              : File(''),
                      onEdit: () {
                        bottomSheetWrapper(
                          context: context,
                          child: AddVariationBottomsheet(
                            editingValue: variation,
                            returningValue: (value) {
                              variationArgs[i] = value;
                              setState(() {});
                            },
                          ),
                        );
                      },
                      onRemove: () {
                        bottomSheetWrapper(
                            context: context,
                            child: CustomBottomSheet(
                              title: 'Remove Variation ?',
                              subTitle:
                                  'Are you sure you want to remove this \nproduct Variation?',
                              firstButtonText: "Yes, Remove ",
                              secondButtonText: "No, Close",
                              onPressedFirst: () {
                                variationArgs.removeAt(i);
                                setState(() {});
                                popNavigation(context: context);
                              },
                            ));
                      },
                    );
                  },
                  separatorBuilder: (_, __) => SizedBox(height: 16.h),
                  itemCount: variationArgs.length,
                ),
                SizedBox(height: 46.h),
                Clickable(
                  onPressed: () {
                    // check for the lenght of image
                    if (widget.createProductArg.imageFile?.length ==
                        variationArgs.length) {
                      showFlushBar(
                          context: context,
                          message:
                              "Please upload image for next variation products",
                          success: false);
                      return;
                    }
                    bottomSheetWrapper(
                      context: context,
                      child: AddVariationBottomsheet(
                        returningValue: (value) {
                          variationArgs.add(value);
                          setState(() {});
                        },
                      ),
                    );
                  },
                  child: Row(
                    children: [
                      Icon(
                        Icons.add,
                        color: ColorPath.flamingo,
                        size: 16.sp,
                      ),
                      SizedBox(
                        width: 6.w,
                      ),
                      Text(
                        "Add Variant Product",
                        style: textTheme.bodyMedium?.copyWith(
                            color: ColorPath.flamingo,
                            fontSize: 14,
                            fontWeight: FontWeight.w500),
                      ),
                    ],
                  ),
                ),
                SizedBox(height: 40.h),
                if (widget.existingProductData != null)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Clickable(
                        onPressed: () {
                          popNavigation(context: context);
                        },
                        child: Row(
                          children: [
                            SvgPicture.asset(Utilities.getSvg("arrowLeft")),
                            SizedBox(width: 8.w),
                            Text(
                              "Previous",
                              style: textTheme.bodyMedium?.copyWith(
                                  color: ColorPath.flamingo,
                                  fontSize: 16,
                                  fontWeight: FontWeight.w600),
                            ),
                          ],
                        ),
                      ),
                    ],
                  )
                else
                  CustomButton(
                    buttonText: "Create Variation Product",
                    onPressed: () {
                      bottomSheetWrapper(
                        context: context,
                        child: CustomBottomSheet(
                          title: 'Create Variation Product ?',
                          subTitle:
                              'Are you sure you want to Create this new product \ninclusive of it detail and variations ?',
                          firstButtonText: "Yes, Create Variation Product",
                          secondButtonText: "No, Close",
                          onPressedFirst: () async {
                            popNavigation(context: context);

                            // Add imagePath to variation args
                            final imageFiles =
                                widget.createProductArg.imagePath ?? [];
                            for (int i = 0; i < imageFiles.length; i++) {
                              variationArgs[i].image = imageFiles[i];
                            }

                            await ref
                                .read(productViewModel.notifier)
                                .createProduct({
                              ...widget.createProductArg.toJson(),
                              "has_variations": true,
                              "variations":
                                  variationArgs.map((e) => e.toJson()).toList(),
                            });

                            if (ref.read(productViewModel).state ==
                                ViewState.retrieved) {
                              bottomSheetWrapper(
                                  context: context,
                                  child: CustomBottomSheet(
                                    title: 'New Product Created',
                                    subTitle:
                                        'Congratulations, you have successfully \ncreated a new product',
                                    firstButtonText: "Manage all Product",
                                    secondButtonText: "Create New Product",
                                    onPressedFirst: () {
                                      popNavigation(context: context);
                                      popNavigation(context: context);
                                      popNavigation(context: context);
                                    },
                                    onPressedSecond: () {
                                      popNavigation(context: context);
                                    },
                                  ));
                            } else {
                              showFlushBar(
                                  context: context,
                                  message: ref.read(productViewModel).message,
                                  success: false);
                            }
                          },
                        ),
                      );
                      // pushNavigation(
                      //     context: context,
                      //     widget: const VariationInventoryInformation(),
                      //     routeName: NamedRoutes.variationInventory);
                    },
                  ),
                SizedBox(
                    height: widget.existingProductData != null ? 150.h : 30.h),
              ],
            ),
          );
        }),
        bottomSheet: widget.existingProductData != null
            ? Container(
                height: 163.h,
                padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
                decoration: BoxDecoration(color: Colors.white, boxShadow: [
                  BoxShadow(
                      color: ColorPath.mischkaGrey,
                      blurRadius: 4.r,
                      spreadRadius: 4.r,
                      blurStyle: BlurStyle.outer)
                ]),
                child: Column(
                  children: [
                    Text(
                      "Edit, Delete or Deactivate a Variation Product",
                      style: textTheme.bodySmall
                          ?.copyWith(color: colorScheme.text7),
                    ),
                    SizedBox(
                      height: 16.h,
                    ),
                    CustomButton(
                      onPressed: () {
                        bottomSheetWrapper(
                          context: context,
                          child: TakeProductActionBottomsheet(
                            viewProductArg: CreateProductArg(
                              variationId:
                                  widget.existingProductData?.variationId,
                              productId: widget
                                  .existingProductData?.product?.productId,
                            ),
                            // variationId:
                            //     widget.existingProductData?.variationId ?? "",
                            // productId: widget
                            //         .existingProductData?.product?.productId ??
                            //     "",
                          ),
                        );
                      },
                      borderColor: ColorPath.flamingo,
                      bgColor: Colors.white,
                      buttonTextColor: ColorPath.flamingo,
                      buttonText: "Take Action",
                    )
                  ],
                ),
              )
            : const SizedBox(),
      ),
    );
  }
}

class VariantCard extends ConsumerStatefulWidget {
  const VariantCard({
    super.key,
    required this.name,
    required this.amtQty,
    required this.costPrice,
    required this.sellingPrice,
    required this.reorderLevel,
    required this.size,
    required this.colour,
    this.imagePath,
    required this.image,
    this.onEdit,
    this.onRemove,
  });

  final String name;
  final String amtQty;
  final String costPrice;
  final String sellingPrice;
  final String reorderLevel;
  final String size;
  final String colour;
  final String? imagePath; // for existing product
  final File? image;
  final VoidCallback? onEdit;
  final VoidCallback? onRemove;

  @override
  ConsumerState<VariantCard> createState() => _VariantCardState();
}

class _VariantCardState extends ConsumerState<VariantCard> {
  bool expand = false;

  _onExpand() {
    setState(() {
      expand = !expand;
    });
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: EdgeInsets.symmetric(vertical: 20.h, horizontal: 16.w),
      decoration: BoxDecoration(
        border: Border.all(color: ColorPath.athensGrey),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  widget.imagePath != null
                      ? ProductImageWidget(
                          imagePath: widget.imagePath,
                        )
                      : Container(
                          height: 45.w,
                          width: 45.w,
                          decoration: BoxDecoration(
                            border: Border.all(color: ColorPath.athensGrey),
                            borderRadius: BorderRadius.circular(8.r),
                            image: DecorationImage(
                              fit: BoxFit.cover,
                              image: FileImage(
                                widget.image ?? File(''),
                              ),
                            ),
                          ),
                        ),
                  SizedBox(width: 16.w),
                  Text(
                    widget.name,
                    style: textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w500,
                      color: colorScheme.textPrimary,
                    ),
                  ),
                ],
              ),
              const Spacer(),
              Clickable(
                onPressed: _onExpand,
                child: Icon(
                  expand
                      ? Icons.keyboard_arrow_up_rounded
                      : Icons.keyboard_arrow_down_rounded,
                  color: ColorPath.mirageBlack,
                  weight: 1.5,
                ),
              )
            ],
          ),
          SizedBox(height: 16.h),
          AnimatedSize(
            duration: const Duration(milliseconds: 400),
            curve: Curves.easeInOut,
            child: expand
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      RowText(
                        title: "Available Quantity:",
                        value: widget.amtQty,
                      ),
                      RowText(
                        title: "Cost Price:",
                        value: widget.costPrice,
                      ),
                      RowText(
                        title: "Selling Price:",
                        value: widget.sellingPrice,
                      ),
                      RowText(
                        title: "Reorder Level:",
                        value: widget.reorderLevel,
                      ),
                      RowText(
                        title: "Size:",
                        value: widget.size,
                      ),
                      RowText(
                        title: "Colour:",
                        value: widget.colour,
                      ),
                    ],
                  )
                : const SizedBox.shrink(),
          ),
          SizedBox(height: 16.h),
          Row(
            children: [
              Expanded(
                child: VarientCta(
                  text: "Edit",
                  svgPath: AppSvg.editPen,
                  onPressed: widget.onEdit,
                ),
              ),
              SizedBox(width: 16.w),
              Expanded(
                child: VarientCta(
                  isDelete: true,
                  text: "Remove",
                  svgPath: AppSvg.trash,
                  onPressed: widget.onRemove,
                ),
              ),
            ],
          )
        ],
      ),
    );
  }
}

class VarientCta extends StatelessWidget {
  const VarientCta({
    super.key,
    required this.text,
    required this.svgPath,
    this.isDelete = false,
    this.onPressed,
  });

  final String text;
  final String svgPath;
  final bool isDelete;
  final VoidCallback? onPressed;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    return Clickable(
      onPressed: onPressed,
      child: Container(
        padding: EdgeInsets.symmetric(vertical: 10.h, horizontal: 12.w),
        decoration: BoxDecoration(
          color: isDelete ? ColorPath.lavenderPink : ColorPath.grey100,
          borderRadius: BorderRadius.circular(4.r),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Text(
              text,
              style: textTheme.bodySmall?.copyWith(
                color: isDelete ? ColorPath.amaranthRed : ColorPath.grey800,
                fontWeight: FontWeight.w500,
              ),
            ),
            SizedBox(width: 16.w),
            SvgPicture.asset(svgPath),
          ],
        ),
      ),
    );
  }
}

class RowText extends StatelessWidget {
  const RowText({
    super.key,
    required this.title,
    required this.value,
  });

  final String title;
  final String value;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      padding: EdgeInsets.symmetric(
        vertical: 7.h,
      ),
      child: Row(
        children: [
          Expanded(
            flex: 6,
            child: Text(
              title,
              style: textTheme.bodySmall?.copyWith(color: ColorPath.grey500),
            ),
          ),
          Expanded(
            flex: 4,
            child: Text(
              value,
              style: textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w500,
                color: ColorPath.grey800,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
