import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/pages/pos/sales/sales_order/add_customers.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/store_filter_bottomsheet.dart';
import 'package:quick_retail_mobile/ui/widgets/color_tag.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_icon.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';

class SearchCustomers extends StatefulWidget {
  const SearchCustomers({super.key});

  @override
  State<SearchCustomers> createState() => _SearchCustomersState();
}

class _SearchCustomersState extends State<SearchCustomers> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
        context: context,
        // preferredHeight: 16,
        title: "Search for Customer",
        centerTitle: true,
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 32.h),
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    "All Customer",
                    style: textTheme.titleMedium,
                  ),
                  SizedBox(
                    width: 4.w,
                  ),
                  ColorTag(
                    color: ColorPath.flamingo.withOpacity(.2),
                    child: Text(
                      '100',
                      style: textTheme.bodySmall
                          ?.copyWith(color: ColorPath.flamingo),
                    ),
                  )
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: CustomIcon(
                  imageAsset: Utilities.getSvg("filter"),
                  bgColor: ColorPath.flamingo,
                  onPressed: () {
                    bottomSheetWrapper(
                        context: context,
                        child: const StoreFilterBottomsheet());
                  },
                ),
              ),
            ],
          ),
          CustomTextField(
            isCompulsory: false,
            enabled: false,
            hintText: 'Enter key word',
            prefixIcon: Padding(
              padding: const EdgeInsets.only(left: 8.0, right: 8.0),
              child: SvgPicture.asset(Utilities.getSvg('search')),
            ),
          ),
          ListView.separated(
              padding: EdgeInsets.symmetric(vertical: 32.h),
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemBuilder: (context, index) {
                return const CustomerItem(
                  isSelectable: true,
                  // isSelected: true,
                );
              },
              separatorBuilder: (context, index) {
                return SizedBox(
                  height: 22.h,
                );
              },
              itemCount: 10),
          SizedBox(
            height: 163.h,
          )
        ],
      ),
      bottomSheet: Container(
        height: 163.h,
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
        decoration: BoxDecoration(color: Colors.white, boxShadow: [
          BoxShadow(
              color: ColorPath.mischkaGrey,
              blurRadius: 4.r,
              spreadRadius: 4.r,
              blurStyle: BlurStyle.outer)
        ]),
        child: Column(
          children: [
            Text(
              "Add selected customer (s) to Sales Order",
              style: textTheme.bodySmall?.copyWith(color: colorScheme.text7),
            ),
            SizedBox(
              height: 16.h,
            ),
            CustomButton(
              onPressed: () {
                // bottomSheetWrapper(context: context, child:  TakeProductActionBottomsheet());
              },
              borderColor: ColorPath.flamingo,
              bgColor: Colors.white,
              buttonTextColor: ColorPath.flamingo,
              buttonText: "Add to Order",
            )
          ],
        ),
      ),
    );
  }
}
