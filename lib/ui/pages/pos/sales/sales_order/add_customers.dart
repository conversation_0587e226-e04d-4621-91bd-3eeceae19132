import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/pages/pos/sales/sales_order/confirm_payment.dart';
import 'package:quick_retail_mobile/ui/pages/pos/sales/sales_order/search_customers.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';
import 'package:quick_retail_mobile/ui/widgets/empty_state.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/product_description_data.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/product_image_widget.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

class AddCustomers extends StatefulWidget {
  const AddCustomers({super.key});

  @override
  State<AddCustomers> createState() => _AddCustomersState();
}

class _AddCustomersState extends State<AddCustomers> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
        context: context,
        // preferredHeight: 16,
        title: "Add Sales Order",
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 32.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const ScreenTitle(
                title: "ADD CUSTOMER",
                subTitle: "Choose and search from existing customer"),
            SizedBox(
              height: 8.h,
            ),
            Clickable(
              onPressed: () {
                pushNavigation(
                    context: context,
                    widget: const SearchCustomers(),
                    routeName: NamedRoutes.searchCustomer);
              },
              child: CustomTextField(
                isCompulsory: false,
                enabled: false,
                hintText: 'Enter key word',
                prefixIcon: Padding(
                  padding: const EdgeInsets.only(left: 8.0, right: 8.0),
                  child: SvgPicture.asset(Utilities.getSvg('search')),
                ),
              ),
            ),
            SizedBox(
              height: 16.h,
            ),
            1 + 1 == 3
                ? Container(
                    padding: EdgeInsets.only(bottom: 45.h),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(color: ColorPath.athensGrey)),
                    child: Center(
                      child: EmptyState(
                        imageAsset: Utilities.getSvg("empty"),
                        subTitle: "No Customer added yet",
                        title: "No Customer",
                        showCTA: false,
                      ),
                    ),
                  )
                : Column(
                    children: [
                      ListView.separated(
                          padding: EdgeInsets.symmetric(vertical: 32.h),
                          physics: const NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            return const CustomerItem(
                              isSelectable: true,
                              isSelected: true,
                            );
                          },
                          separatorBuilder: (context, index) {
                            return SizedBox(
                              height: 22.h,
                            );
                          },
                          itemCount: 2),
                      SizedBox(
                        height: 18.h,
                      ),
                      CustomButton(onPressed: () {
                        pushNavigation(
                            context: context,
                            widget: const ConfirmPayment(),
                            routeName: NamedRoutes.confirmPayment);
                      })
                    ],
                  ),
            SizedBox(
              height: 163.h,
            )
          ],
        ),
      ),
      bottomSheet: 1 + 1 == 2 // if Take Action
          ? Container(
              height: 163.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
              decoration: BoxDecoration(color: Colors.white, boxShadow: [
                BoxShadow(
                    color: ColorPath.mischkaGrey,
                    blurRadius: 4.r,
                    spreadRadius: 4.r,
                    blurStyle: BlurStyle.outer)
              ]),
              child: Column(
                children: [
                  Text(
                    "Edit or Delete a Sales order",
                    style:
                        textTheme.bodySmall?.copyWith(color: colorScheme.text7),
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  CustomButton(
                    onPressed: () {
                      // bottomSheetWrapper(context: context, child:  TakeProductActionBottomsheet());
                    },
                    borderColor: ColorPath.flamingo,
                    bgColor: Colors.white,
                    buttonTextColor: ColorPath.flamingo,
                    buttonText: "Take Action",
                  )
                ],
              ),
            )
          : const SizedBox(),
    );
  }
}

//WIDGETS
class CustomerItem extends StatefulWidget {
  final bool isSelectable;
  final bool? isSelected;
  //todo::: add onValue changed function

  const CustomerItem({super.key, this.isSelectable = false, this.isSelected});

  @override
  State<CustomerItem> createState() => _CustomerItemState();
}

class _CustomerItemState extends State<CustomerItem> {
  bool isSelected = false;

  @override
  void initState() {
    setState(() {
      isSelected = widget.isSelected ?? false;
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Clickable(
        onPressed: widget.isSelectable
            ? () {
                setState(() {
                  isSelected = !isSelected;
                });
              }
            : null,
        child: Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            border: Border.all(
                color: isSelected ? ColorPath.flamingo : ColorPath.athensGrey),
            borderRadius: BorderRadius.circular(8.r),
          ),
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (widget.isSelectable)
                Row(
                  children: [
                    Container(
                      height: 16.w,
                      width: 16.w,
                      decoration: BoxDecoration(
                          border: Border.all(
                              color: isSelected
                                  ? ColorPath.flamingo
                                  : ColorPath.mischkaGrey),
                          borderRadius: BorderRadius.circular(6.r)),
                      child: Icon(Icons.done,
                          size: 12,
                          color: isSelected
                              ? ColorPath.flamingo
                              : ColorPath.white),
                    ),
                    SizedBox(
                      width: 6.w,
                    )
                  ],
                ),
              //! User image
              const ProductImageWidget(),
              SizedBox(
                width: 8.w,
              ),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Text("Customer Name"),
                  SizedBox(
                    height: 8.h,
                  ),
                  const ProductDescriptionData(
                    description: "Email",
                    data: "<EMAIL>",
                  ),
                ],
              )
            ],
          ),
        ));
  }
}
