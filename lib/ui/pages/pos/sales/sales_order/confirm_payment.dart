import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/custom_bottom_sheet.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

class ConfirmPayment extends StatefulWidget {
  const ConfirmPayment({super.key});

  @override
  State<ConfirmPayment> createState() => _ConfirmPaymentState();
}

class _ConfirmPaymentState extends State<ConfirmPayment> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
        context: context,
        // preferredHeight: 16,
        title: "Add Sales Order",
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 32.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const ScreenTitle(
                title: "CONFIRM PAYMENT",
                subTitle: "Enter payment details for this sales order"),
            SizedBox(
              height: 16.h,
            ),
            Clickable(
              onPressed: () {},
              child: const CustomTextField(
                label: 'Payment Option',
                hintText: 'Select an option ',
                enabled: false,
                suffixIcon: Padding(
                  padding: EdgeInsets.only(right: 16.0),
                  child: Icon(Icons.keyboard_arrow_down_outlined),
                ),
              ),
            ),
            SizedBox(
              height: 24.h,
            ),
            CustomTextField(
              label: 'Amount Collected',
              hintText: 'Enter Value',
              prefixIcon: Padding(
                padding: EdgeInsets.only(left: 14.0, right: 6.w),
                child: const Text(
                  "₦",
                  style: TextStyle(fontFamily: ''),
                ),
              ),
            ),
            SizedBox(
              height: 24.h,
            ),
            CustomTextField(
              label: 'Discount',
              hintText: 'Enter Value',
              prefixIcon: Padding(
                padding: EdgeInsets.only(left: 14.0, right: 6.w),
                child: const Text(
                  "₦",
                  style: TextStyle(fontFamily: ''),
                ),
              ),
            ),
            SizedBox(
              height: 24.h,
            ),
            CustomTextField(
              label: 'Customer Balance',
              hintText: 'Enter Value',
              prefixIcon: Padding(
                padding: EdgeInsets.only(left: 14.0, right: 6.w),
                child: const Text(
                  "₦",
                  style: TextStyle(fontFamily: ''),
                ),
              ),
            ),
            SizedBox(
              height: 24.h,
            ),
            const CustomTextField(
              label: 'Payment References (optional)',
              hintText: 'Enter reference number',
              isCompulsory: false,
            ),
            SizedBox(
              height: 24.h,
            ),
            ScreenTitle(
              title: "Payment Breakdown",
              subTitle: "Breakdown of payment",
              titleSize: 14.sp,
              subTitleSize: 12.sp,
            ),
            SizedBox(
              height: 16.h,
            ),
            Container(
              padding: EdgeInsets.all(16.w),
              decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(8.r),
                  border: Border.all(color: ColorPath.athensGrey)),
              child: Column(
                children: [
                  const PaymentBreakdownItem(
                    title: "Subtotal (2 Items)",
                    amount: 40000,
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  const PaymentBreakdownItem(
                    title: "Discount",
                    // amount: 40000,
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  const PaymentBreakdownItem(
                    title: "Tax (7.5% VAT)",
                    amount: 5905,
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  const PaymentBreakdownItem(
                    title: "Service Fee",
                    amount: 1000,
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  Row(
                    children: [
                      Expanded(
                          child: Text(
                        "Total Order",
                        style: textTheme.bodyLarge?.copyWith(
                          color: colorScheme.text7,
                          fontWeight: FontWeight.w600,
                        ),
                      )),
                      Row(
                        children: [
                          NairaDisplay(
                            amount: 46905,
                            fontSize: 16,
                            fontWeight: FontWeight.w600,
                            color: colorScheme.text5,
                          ),
                        ],
                      )
                    ],
                  ),
                ],
              ),
            ),
            SizedBox(
              height: 48.h,
            ),
            CustomButton(
              onPressed: () {
                // bottomSheetWrapper(
                //     context: context,
                //     child: CustomBottomSheet(
                //       title: "Create Sales Order?",
                //       lottieAsset: Utilities.getLottie("caution"),
                //       subTitle:
                //           "Are you sure you want to create this sales order in lieu of the customer details and confirmation of receipt of payment (if any)",
                //       firstButtonText: "Yes, Create sales Order",
                //       onPressedFirst: () {},
                //       onPressedSecond: () {},
                //     ));

                bottomSheetWrapper(
                    context: context,
                    child: CustomBottomSheet(
                      title: "Sales Order Created",
                      lottieAsset: Utilities.getLottie("success"),
                      subTitle:
                          "Congratulations, you have successfully created a sales order. ",
                      firstButtonText: "Manage all Sales order",
                      secondButtonText: "Create New Sales Order",
                      onPressedFirst: () {},
                      onPressedSecond: () {},
                    ));
              },
              buttonText: "Create Sales Order",
            ),
            SizedBox(
              height: 163.h,
            )
          ],
        ),
      ),
      bottomSheet: 1 + 1 == 2 // if Take Action
          ? Container(
              height: 163.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
              decoration: BoxDecoration(color: Colors.white, boxShadow: [
                BoxShadow(
                    color: ColorPath.mischkaGrey,
                    blurRadius: 4.r,
                    spreadRadius: 4.r,
                    blurStyle: BlurStyle.outer)
              ]),
              child: Column(
                children: [
                  Text(
                    "Edit or Delete a Sales order",
                    style:
                        textTheme.bodySmall?.copyWith(color: colorScheme.text7),
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  CustomButton(
                    onPressed: () {
                      // bottomSheetWrapper(context: context, child:  TakeProductActionBottomsheet());
                    },
                    borderColor: ColorPath.flamingo,
                    bgColor: Colors.white,
                    buttonTextColor: ColorPath.flamingo,
                    buttonText: "Take Action",
                  )
                ],
              ),
            )
          : const SizedBox(),
    );
  }
}

class PaymentBreakdownItem extends StatelessWidget {
  final String title;
  final double amount;
  const PaymentBreakdownItem({super.key, this.title = '', this.amount = 0});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Row(
      children: [
        Expanded(
            child: Text(
          title,
          style: textTheme.bodySmall?.copyWith(color: colorScheme.text7),
        )),
        Row(
          children: [
            NairaDisplay(
              amount: amount,
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: colorScheme.text4,
            ),
          ],
        )
      ],
    );
  }
}
