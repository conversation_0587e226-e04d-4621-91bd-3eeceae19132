import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/data/models/category_response.dart';
import 'package:quick_retail_mobile/core/data/models/product_response.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/store_filter_bottomsheet.dart';
import 'package:quick_retail_mobile/ui/widgets/color_tag.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_icon.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/product_item.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/search_scan_product_widget.dart';

class SearchProduct extends StatefulWidget {
  const SearchProduct({super.key});

  @override
  State<SearchProduct> createState() => _SearchProductState();
}

class _SearchProductState extends State<SearchProduct> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
        context: context,
        // preferredHeight: 16,
        title: "Search for product",
        centerTitle: true,
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 32.h),
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  Text(
                    "All Product",
                    style: textTheme.titleMedium,
                  ),
                  SizedBox(
                    width: 4.w,
                  ),
                  ColorTag(
                    color: ColorPath.flamingo.withOpacity(.2),
                    child: Text(
                      '100',
                      style: textTheme.bodySmall
                          ?.copyWith(color: ColorPath.flamingo),
                    ),
                  )
                ],
              ),
              Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: CustomIcon(
                  imageAsset: Utilities.getSvg("filter"),
                  bgColor: ColorPath.flamingo,
                  onPressed: () {
                    bottomSheetWrapper(
                        context: context,
                        child: const StoreFilterBottomsheet());
                  },
                ),
              ),
            ],
          ),
          const SearchScanProductWidget(),
          ListView.separated(
              padding: EdgeInsets.symmetric(vertical: 32.h),
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemBuilder: (context, index) {
                return ProductItem(
                  isSelectable: true,
                  productData: ProductData(
                    name: 'product namw',
                    sellingPrice: '50000',
                    quantity: 25,
                    status: 'Active',
                    product: Product(
                      category: Category(name: 'Shoe'),
                      location: Location(name: 'Store 1'),
                    ),
                  ),
                );
              },
              separatorBuilder: (context, index) {
                return SizedBox(
                  height: 22.h,
                );
              },
              itemCount: 3),
          SizedBox(
            height: 163.h,
          )
        ],
      ),
      bottomSheet: Container(
        height: 163.h,
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
        decoration: BoxDecoration(color: Colors.white, boxShadow: [
          BoxShadow(
              color: ColorPath.mischkaGrey,
              blurRadius: 4.r,
              spreadRadius: 4.r,
              blurStyle: BlurStyle.outer)
        ]),
        child: Column(
          children: [
            Text(
              "Add selected product (s) to Sales Order",
              style: textTheme.bodySmall?.copyWith(color: colorScheme.text7),
            ),
            SizedBox(
              height: 16.h,
            ),
            CustomButton(
              onPressed: () {
                // bottomSheetWrapper(context: context, child:  TakeProductActionBottomsheet());
              },
              borderColor: ColorPath.flamingo,
              bgColor: Colors.white,
              buttonTextColor: ColorPath.flamingo,
              buttonText: "Add to Order",
            )
          ],
        ),
      ),
    );
  }
}
