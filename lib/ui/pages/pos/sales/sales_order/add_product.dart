import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/data/models/category_response.dart';
import 'package:quick_retail_mobile/core/data/models/product_response.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/pages/pos/sales/sales_order/add_customers.dart';
import 'package:quick_retail_mobile/ui/pages/pos/sales/sales_order/search_product.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/empty_state.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/product_item.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/search_scan_product_widget.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

class AddProduct extends StatefulWidget {
  const AddProduct({super.key});

  @override
  State<AddProduct> createState() => _AddProductState();
}

class _AddProductState extends State<AddProduct> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
        context: context,
        // preferredHeight: 16,
        title: "Add Sales Order",
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 32.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const ScreenTitle(
                title: "ADD PRODUCT",
                subTitle:
                    "Search or scan product from your database of existing product to continue"),
            SizedBox(
              height: 8.h,
            ),
            Clickable(
              onPressed: () {
                pushNavigation(
                    context: context,
                    widget: const SearchProduct(),
                    routeName: NamedRoutes.searchProduct);
              },
              child: const SearchScanProductWidget(
                isEnable: false,
              ),
            ),
            SizedBox(
              height: 16.h,
            ),
            1 + 1 == 3
                ? Container(
                    padding: EdgeInsets.only(bottom: 45.h),
                    decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(color: ColorPath.athensGrey)),
                    child: Center(
                      child: EmptyState(
                        imageAsset: Utilities.getSvg("empty"),
                        subTitle: "No product added yet",
                        title: "No Product",
                        showCTA: false,
                      ),
                    ),
                  )
                : Column(
                    children: [
                      ListView.separated(
                          padding: EdgeInsets.symmetric(vertical: 32.h),
                          physics: const NeverScrollableScrollPhysics(),
                          shrinkWrap: true,
                          itemBuilder: (context, index) {
                            return ProductItem(
                              isSelectable: true,
                              productData: ProductData(
                                name: 'product namw',
                                sellingPrice: '50000',
                                quantity: 25,
                                status: 'Active',
                                product: Product(
                                  category: Category(name: 'Shoe'),
                                  location: Location(name: 'Store 1'),
                                ),
                              ),
                            );
                            // return const ProductItem(
                            //   isSelectable: true,
                            //   isSelected: true,
                            // );
                          },
                          separatorBuilder: (context, index) {
                            return SizedBox(
                              height: 22.h,
                            );
                          },
                          itemCount: 2),
                      SizedBox(
                        height: 18.h,
                      ),
                      CustomButton(onPressed: () {
                        pushNavigation(
                            context: context,
                            widget: const AddCustomers(),
                            routeName: NamedRoutes.addSaledOrderCustomer);
                      })
                    ],
                  ),
            SizedBox(
              height: 163.h,
            )
          ],
        ),
      ),
      bottomSheet: 1 + 1 == 2 // if Take Action
          ? Container(
              height: 163.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
              decoration: BoxDecoration(color: Colors.white, boxShadow: [
                BoxShadow(
                    color: ColorPath.mischkaGrey,
                    blurRadius: 4.r,
                    spreadRadius: 4.r,
                    blurStyle: BlurStyle.outer)
              ]),
              child: Column(
                children: [
                  Text(
                    "Edit or Delete a Sales order",
                    style:
                        textTheme.bodySmall?.copyWith(color: colorScheme.text7),
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  CustomButton(
                    onPressed: () {
                      // bottomSheetWrapper(context: context, child:  TakeProductActionBottomsheet());
                    },
                    borderColor: ColorPath.flamingo,
                    bgColor: Colors.white,
                    buttonTextColor: ColorPath.flamingo,
                    buttonText: "Take Action",
                  )
                ],
              ),
            )
          : const SizedBox(),
    );
  }
}
