import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/pages/pos/sales/sales_order/add_product.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/store_filter_bottomsheet.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/color_tag.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_icon.dart';
import 'package:quick_retail_mobile/ui/widgets/empty_state.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/search_scan_product_widget.dart';

class SalesProcessing extends StatefulWidget {
  const SalesProcessing({super.key});

  @override
  State<SalesProcessing> createState() => _SalesProcessingState();
}

class _SalesProcessingState extends State<SalesProcessing> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    // final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
          context: context,
          // preferredHeight: 16,
          title: "Sales Processing",
          centerTitle: true,
          actions: [
            Padding(
              padding: EdgeInsets.only(right: 16.w),
              child: Row(
                children: [
                  CustomIcon(
                    imageAsset: Utilities.getSvg("addWhite"),
                    bgColor: ColorPath.flamingo,
                    onPressed: () {
                      pushNavigation(
                          context: context,
                          widget: const AddProduct(),
                          routeName: NamedRoutes.addSaledOrderProduct);
                    },
                  ),
                  SizedBox(
                    width: 8.w,
                  ),
                ],
              ),
            )
          ]),
      body: SingleChildScrollView(
        padding:
            EdgeInsets.only(left: 16.w, right: 16.w, top: 32.h, bottom: 48.h),
        child: 1 + 1 == 3
            ? Padding(
                padding: const EdgeInsets.only(top: 150.0),
                child: EmptyState(
                  imageAsset: Utilities.getSvg('noProduct'),
                  title: "No Order yet",
                  subTitle: "No currently do not have any Sales order yet. ",
                  buttonText: "Add New Order",
                ),
              )
            : Column(
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Text(
                            "Order",
                            style: textTheme.titleMedium,
                          ),
                          SizedBox(
                            width: 4.w,
                          ),
                          ColorTag(
                            color: ColorPath.flamingo.withOpacity(.2),
                            child: Text(
                              '100',
                              style: textTheme.bodySmall
                                  ?.copyWith(color: ColorPath.flamingo),
                            ),
                          )
                        ],
                      ),
                      Padding(
                        padding: const EdgeInsets.only(right: 8.0),
                        child: CustomIcon(
                          imageAsset: Utilities.getSvg("filter"),
                          bgColor: ColorPath.flamingo,
                          onPressed: () {
                            bottomSheetWrapper(
                                context: context,
                                child:
                                    const StoreFilterBottomsheet()); //placeholder
                          },
                        ),
                      ),
                    ],
                  ),
                  // SizedBox(
                  //   height: 15.h,
                  // ),
                  const SearchScanProductWidget(),
                  SizedBox(
                    height: 12.h,
                  ),
                  ListView.separated(
                      padding: EdgeInsets.symmetric(vertical: 32.h),
                      physics: const NeverScrollableScrollPhysics(),
                      shrinkWrap: true,
                      itemBuilder: (context, index) {
                        // return const ProductItem();
                        return const SalesOrderItem();
                      },
                      separatorBuilder: (context, index) {
                        return SizedBox(
                          height: 22.h,
                        );
                      },
                      itemCount: 3)
                ],
              ),
      ),
    );
  }
}

class SalesOrderItem extends StatefulWidget {
  const SalesOrderItem({
    super.key,
  });

  @override
  State<SalesOrderItem> createState() => _SalesOrderItemState();
}

class _SalesOrderItemState extends State<SalesOrderItem> {
  bool isExpanded = false;
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        border: Border.all(color: ColorPath.athensGrey),
        borderRadius: BorderRadius.circular(8.r),
      ),
      child: Column(
        children: [
          Row(
            children: [
              Expanded(
                  child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    "#74094",
                    style: textTheme.bodyMedium
                        ?.copyWith(fontWeight: FontWeight.w600),
                  ),
                  SizedBox(
                    height: 8.h,
                  ),
                  Text(
                    "Order ID:",
                    style: textTheme.bodySmall
                        ?.copyWith(color: colorScheme.subTextPrimary),
                  )
                ],
              )),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    NairaDisplay(
                      amount: 200789.90,
                      color: colorScheme.text4,
                      fontSize: 14.sp,
                    ),
                    SizedBox(
                      height: 8.h,
                    ),
                    ColorTag(
                      color: ColorPath.foamGreen,
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          const Icon(
                            Icons.circle,
                            size: 6,
                            color: ColorPath.meadowGreen,
                          ),
                          SizedBox(
                            width: 8.w,
                          ),
                          Text(
                            "Paid",
                            style: textTheme.bodySmall
                                ?.copyWith(color: ColorPath.funGreen),
                          ),
                        ],
                      ),
                    )
                  ],
                ),
              )
            ],
          ),
          SizedBox(
            height: 16.h,
          ),
          Clickable(
            onPressed: () {
              setState(() {
                isExpanded = !isExpanded;
              });
            },
            child: Container(
              padding: EdgeInsets.all(8.w),
              decoration: BoxDecoration(
                  color: ColorPath.flamingo.withOpacity(.15),
                  borderRadius: BorderRadius.circular(4.r)),
              child: Row(
                children: [
                  Expanded(
                      child: Text(
                    "Product in this Order",
                    style: textTheme.bodySmall
                        ?.copyWith(color: ColorPath.flamingo),
                  )),
                  Icon(
                    isExpanded
                        ? Icons.keyboard_arrow_up_outlined
                        : Icons.keyboard_arrow_down_outlined,
                    size: 24,
                  )
                ],
              ),
            ),
          ),
          if (isExpanded)
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SizedBox(
                  height: 16.h,
                ),
                const OrderProductDescriptionData(
                  productName: "Sleek Shirt with Free Hat",
                ),
                SizedBox(
                  height: 16.h,
                ),
                const OrderProductDescriptionData(
                  productName: "Chelsea Book ",
                ),
                SizedBox(
                  height: 16.h,
                ),
                const OrderProductDescriptionData(
                  productName: "LV Bag ",
                ),
                SizedBox(
                  height: 16.h,
                ),
                Text(
                  "+ 4 more products",
                  // textAlign: TextAlign.start,
                  style: textTheme.bodySmall
                      ?.copyWith(fontStyle: FontStyle.italic),
                )
              ],
            )
        ],
      ),
    );
  }
}

class OrderProductDescriptionData extends StatelessWidget {
  final String productName;
  final double amount;
  final int quantity;
  const OrderProductDescriptionData(
      {super.key, this.productName = '', this.amount = 0, this.quantity = 0});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Row(
      children: [
        Expanded(
            child: Text(
          productName,
          style: textTheme.bodySmall?.copyWith(color: colorScheme.text7),
        )),
        Row(
          children: [
            NairaDisplay(
              amount: amount,
              fontSize: 12,
              fontWeight: FontWeight.w600,
              color: colorScheme.text4,
            ),
            // Text(
            //   "N10,000",
            //   style: textTheme.bodySmall?.copyWith(
            //       color: colorScheme.text4,
            //       fontWeight: FontWeight.w600),
            // ),
            SizedBox(
              width: 4.w,
            ),
            Text(
              "x$quantity",
              style: textTheme.bodySmall?.copyWith(color: colorScheme.text7),
            ),
          ],
        )
      ],
    );
  }
}
