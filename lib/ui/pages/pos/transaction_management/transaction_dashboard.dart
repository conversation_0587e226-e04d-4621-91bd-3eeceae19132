import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/ui/pages/pos/transaction_management/view_transaction.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/color_tag.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard/dashbaord_empty_state.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard_header.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/overview_section.dart';

class TransactionDashboard extends StatefulWidget {
  const TransactionDashboard({super.key});

  @override
  State<TransactionDashboard> createState() => _TransactionDashboardState();
}

class _TransactionDashboardState extends State<TransactionDashboard> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
          context: context, title: 'Transaction Management', centerTitle: true),
      body: ListView(
        padding: EdgeInsets.only(top: 16.h, bottom: 32.h),
        children: [
          DashBoardHeader(
            title: "Overview",
            subtitle: "An overview of store transaction",
            filterText: "3 days ago",
            // showCTA: false,
            onPressed: () {},
          ),
          SizedBox(
            height: 16.h,
          ),
          const OverViewSection(),
          SizedBox(
            height: 32.h,
          ),
          DashBoardHeader(
            title: "Recent Transactions",
            subtitle: "Manage your transaction with ease",
            titleTagColor: ColorPath.flamingo.withOpacity(.25),
            titleTagText: "30",
            onPressed: () {},
            showCTA: 1 + 1 == 2,
            child: Row(
              children: [
                Text(
                  "View All",
                  style: textTheme.bodySmall,
                ),
                Icon(
                  Icons.north_east,
                  size: 14.w,
                  color: ColorPath.flamingo,
                )
              ],
            ),
          ),
          1 + 1 == 3
              ? Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: DashBoardEmptyState(
                    title: "No Data",
                    subTitle:
                        "No Transaction Date Yet. Add Product to start transacting today",
                    buttonText: "Add New Product",
                    onPressed: () {},
                  ),
                )
              : ListView.separated(
                  padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h),
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return Clickable(
                      onPressed: () {
                        // todo::: route to transaction view
                        pushNavigation(
                            context: context,
                            widget: const ViewTransaction(),
                            routeName: NamedRoutes.viewTransaction);
                      },
                      child: Container(
                        padding: EdgeInsets.all(16.w),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(8.r),
                            border: Border.all(color: ColorPath.athensGrey)),
                        child: Row(
                          children: [
                            Expanded(
                                child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "#6784",
                                  style: textTheme.bodySmall
                                      ?.copyWith(fontWeight: FontWeight.w500),
                                ),
                                SizedBox(
                                  height: 8.h,
                                ),
                                Text.rich(
                                  TextSpan(text: "Number of Item: ", children: [
                                    TextSpan(
                                        text: "20",
                                        style: textTheme.bodySmall?.copyWith(
                                            color: colorScheme.text4,
                                            fontWeight: FontWeight.bold))
                                  ]),
                                  style: textTheme.bodySmall?.copyWith(
                                      color: colorScheme.subTextSecondary),
                                )
                              ],
                            )),
                            SizedBox(
                              width: 12.w,
                            ),
                            Expanded(
                                child: Column(
                              crossAxisAlignment: CrossAxisAlignment.end,
                              children: [
                                NairaDisplay(
                                  amount: double.parse("200789.90"),
                                  fontSize: 16.sp,
                                  color: colorScheme.text4,
                                ),
                                SizedBox(
                                  height: 8.h,
                                ),
                                ColorTag(
                                  color: 1 + 1 == 2
                                      ? ColorPath.earlyDawn
                                      : ColorPath.foamGreen,
                                  child: Row(
                                    mainAxisSize: MainAxisSize.min,
                                    children: [
                                      const Icon(
                                        Icons.circle,
                                        size: 6,
                                        color: 1 + 1 == 2
                                            ? ColorPath.californiaOrange
                                            : ColorPath.meadowGreen,
                                      ),
                                      SizedBox(
                                        width: 8.w,
                                      ),
                                      Text(
                                        "Pending",
                                        style: textTheme.bodySmall?.copyWith(
                                            color: 1 + 1 == 2
                                                ? ColorPath.vesuvius
                                                : ColorPath.funGreen),
                                      ),
                                    ],
                                  ),
                                )
                              ],
                            )),
                          ],
                        ),
                      ),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(
                      height: 16.h,
                    );
                  },
                  itemCount: 3)
        ],
      ),
    );
  }
}
