import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/take_transaction_action_bottomsheet.dart';
import 'package:quick_retail_mobile/ui/widgets/color_tag.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/product_image_widget.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

class ViewTransaction extends StatefulWidget {
  const ViewTransaction({super.key});

  @override
  State<ViewTransaction> createState() => _ViewTransactionState();
}

class _ViewTransactionState extends State<ViewTransaction> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
          context: context, title: 'View a Transaction', centerTitle: true),
      body: ListView(
        padding:
            EdgeInsets.only(top: 16.h, bottom: 32.h, left: 16.w, right: 16.w),
        children: [
          const ScreenTitle(
              title: "View Transaction Details",
              subTitle: "See details of a specific transaction"),
          SizedBox(
            height: 14.h,
          ),
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: ColorPath.athensGrey)),
            child: Column(
              children: [
                PaymentBreakdownItem(
                  title: "Customer Details",
                  child: Text(
                    "Dairo Isaac (ID:9040)",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Transaction ID:",
                  child: Text(
                    "894GJAbGJS902",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Transaction Date:",
                  child: Text(
                    "May 11, 2025",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Order ID:",
                  child: Text(
                    "7840",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Transaction Status:",
                  child: ColorTag(
                    color:
                        1 + 1 == 3 ? ColorPath.earlyDawn : ColorPath.foamGreen,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.circle,
                          size: 6,
                          color: 1 + 1 == 3
                              ? ColorPath.californiaOrange
                              : ColorPath.meadowGreen,
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Text(
                          "Successful",
                          style: textTheme.bodySmall?.copyWith(
                              color: 1 + 1 == 3
                                  ? ColorPath.vesuvius
                                  : ColorPath.funGreen),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Total Number of Product:",
                  child: Text(
                    "10",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Transaction Value:",
                  child: NairaDisplay(
                    amount: 1903484,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: colorScheme.text4,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 32.h,
          ),
          const ScreenTitle(
              title: "Products",
              subTitle: "Product / Items part of this transaction"),
          SizedBox(
            height: 14.h,
          ),
          ListView.separated(
              padding: EdgeInsets.only(top: 16.h),
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemBuilder: (context, index) {
                return Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(color: ColorPath.athensGrey)),
                  child: Row(
                    children: [
                      const ProductImageWidget(),
                      SizedBox(
                        width: 8.w,
                      ),
                      Expanded(
                          child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Product Name",
                            style: textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.w500),
                          ),
                          SizedBox(
                            height: 8.h,
                          ),
                          Text.rich(
                            TextSpan(text: "Quantity: ", children: [
                              TextSpan(
                                  text: "02",
                                  style: textTheme.bodySmall?.copyWith(
                                      color: colorScheme.text4,
                                      fontWeight: FontWeight.bold))
                            ]),
                            style: textTheme.bodySmall
                                ?.copyWith(color: colorScheme.subTextSecondary),
                          )
                        ],
                      )),
                      SizedBox(
                        width: 12.w,
                      ),
                      Expanded(
                          child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            "Total Amount",
                            style: textTheme.bodySmall
                                ?.copyWith(color: colorScheme.subTextSecondary),
                          ),
                          SizedBox(
                            height: 8.h,
                          ),
                          NairaDisplay(
                            amount: double.parse("200789.90"),
                            fontSize: 16.sp,
                            color: colorScheme.text4,
                          ),
                        ],
                      )),
                    ],
                  ),
                );
              },
              separatorBuilder: (context, index) {
                return SizedBox(
                  height: 16.h,
                );
              },
              itemCount: 3),
          SizedBox(
            height: 168.h,
          )
        ],
      ),
      bottomSheet: 1 + 1 == 2 // if Take Action
          ? Container(
              height: 163.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
              decoration: BoxDecoration(color: Colors.white, boxShadow: [
                BoxShadow(
                    color: ColorPath.mischkaGrey,
                    blurRadius: 4.r,
                    spreadRadius: 4.r,
                    blurStyle: BlurStyle.outer)
              ]),
              child: Column(
                children: [
                  Text(
                    "Take action on a transaction today",
                    style:
                        textTheme.bodySmall?.copyWith(color: colorScheme.text7),
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  CustomButton(
                    onPressed: () {
                      bottomSheetWrapper(context: context, child:  TakeTransactionActionBottomsheet());
                    },
                    borderColor: ColorPath.flamingo,
                    bgColor: Colors.white,
                    buttonTextColor: ColorPath.flamingo,
                    buttonText: "Take Action",
                  )
                ],
              ),
            )
          : const SizedBox(),
    );
  }
}

class PaymentBreakdownItem extends StatelessWidget {
  final String title;
  final Widget? child;
  const PaymentBreakdownItem({super.key, this.title = '', this.child});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Row(
      children: [
        Expanded(
            child: Text(
          title,
          style: textTheme.bodySmall?.copyWith(color: colorScheme.text7),
        )),
        Row(
          children: [
            child ?? Container(),
          ],
        )
      ],
    );
  }
}
