import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/custom_bottom_sheet.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

// todo::: rework scan flow... use [https://pub.dev/packages/flutter_barcode_scanner]
class ScanProduct extends StatelessWidget {
  const ScanProduct({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
        context: context,
        // preferredHeight: 16,
        title: "Scan Product",
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 32.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Image.asset(
              Utilities.getImage("barCode", fileformat: "gif"),
              height: 80.w,
              width: 80.w,
            ),
            SizedBox(
              height: 22.h,
            ),
            const ScreenTitle(
                title: "Scan Product",
                subTitle: "Scan product and add to your store with ease"),
            SizedBox(
              height: 24.h,
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      color: ColorPath.flamingo.withOpacity(.2)),
                  child: SvgPicture.asset(Utilities.getSvg("edit")),
                ),
                SizedBox(
                  width: 10.w,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Connect Scanner to Device",
                        style: textTheme.bodyLarge
                            ?.copyWith(fontWeight: FontWeight.w600),
                      ),
                      Text(
                        "Connect the scanner via USB or enable Bluetooth on your device and pair it, following the manufacturer's instructions, to ensure proper connection to your computer or POS system.",
                        style: textTheme.bodySmall
                            ?.copyWith(color: colorScheme.subTextPrimary),
                      ),
                    ],
                  ),
                )
              ],
            ),
            SizedBox(
              height: 24.h,
            ),
            Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(
                  padding: EdgeInsets.all(12.w),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      color: ColorPath.flamingo.withOpacity(.2)),
                  child: SvgPicture.asset(Utilities.getSvg("edit")),
                ),
                SizedBox(
                  width: 10.w,
                ),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        "Scan the Product Barcode",
                        style: textTheme.bodyLarge
                            ?.copyWith(fontWeight: FontWeight.w600),
                      ),
                      Text(
                        "Use the barcode scanner to scan the barcode found either on the front or back of the product. Make sure the barcode is fully visible and properly aligned for accurate scanning.",
                        style: textTheme.bodySmall
                            ?.copyWith(color: colorScheme.subTextPrimary),
                      ),
                    ],
                  ),
                )
              ],
            ),
            SizedBox(
              height: 128.h,
            ),
            CustomButton(
              onPressed: () {
                bottomSheetWrapper(
                    context: context,
                    child: CustomBottomSheet(
                      title: "Add Scan Product",
                      subTitle:
                          "Scan product completed. Add Scan product to store now. ",
                      firstButtonText: "Yes, Add Product",
                      onPressedFirst: () {},
                    ));
              },
              buttonText: "Start Scanning",
            )
          ],
        ),
      ),
    );
  }
}
