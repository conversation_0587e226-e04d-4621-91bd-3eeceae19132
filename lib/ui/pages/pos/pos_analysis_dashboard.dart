import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/view_models/customer_view_model.dart';
import 'package:quick_retail_mobile/core/data/view_models/pos_dashboard_view_model.dart';
import 'package:quick_retail_mobile/core/data/view_models/sales_view_model.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/date_picker/custom_date_picker.dart';
import 'package:quick_retail_mobile/ui/widgets/error_state.dart';
import 'package:quick_retail_mobile/ui/widgets/loadable_content_builder.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard/custom_customer_card.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard/dashbaord_empty_state.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard/sales_order_card.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard_header.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/overview_section.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/resuable_sales_overview_card.dart';
import 'package:skeletonizer/skeletonizer.dart';

class PosAnalysisDashboard extends ConsumerStatefulWidget {
  const PosAnalysisDashboard({super.key});

  @override
  ConsumerState<PosAnalysisDashboard> createState() =>
      _PosAnalysisDashboardState();
}

class _PosAnalysisDashboardState extends ConsumerState<PosAnalysisDashboard> {
  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(customerViewModel).fetchAllCustomers();
      ref.read(salesViewModel).fetchAllSalesOrder();
    });
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    final posVm = ref.watch(posDashboardViewModel);
    return Scaffold(
      appBar:
          customAppBar(context: context, title: 'Dashboard', centerTitle: true),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.read(customerViewModel).fetchAllCustomers(showBusyState: false);
          ref.read(salesViewModel).fetchAllSalesOrder(showBusyState: false);
          ref
              .read(posDashboardViewModel)
              .fetchAnalysisOverview(showBusyState: false);
          ref
              .read(posDashboardViewModel)
              .fetchCustomerStats(showBusyState: false);
          ref
              .read(posDashboardViewModel)
              .fetchSalesOverview(showBusyState: false);
        },
        child: ListView(
          padding: EdgeInsets.only(top: 16.h, bottom: 32.h),
          children: [
            DashBoardHeader(
              title: "Analysis overview",
              subtitle: "An overview of store",
              filterText: "3 days ago",
              // showCTA: false,
              onPressed: () {},
            ),
            SizedBox(
              height: 16.h,
            ),
            const OverViewSection(),
            SizedBox(
              height: 32.h,
            ),
            DashBoardHeader(
              title: "Sales overview",
              // showCTA: false,
              subtitle: "An overview of sales made",
              filterText: "filter",
              onPressed: () async {
                final result = await showCustomDatePicker(context,
                    selectedDate: null, endDate: null);

                // if (result != null) {
                //   log('Start Date: ${result['startDate']}');
                //   log('End Date: ${result['endDate']}');
                //   ref.read(posDashboardViewModel).fetchSalesOverview(
                //         startDate: result['startDate']?.toString(),
                //         endDate: result['endDate']?.toString(),
                //       );
                // }
                // bottomSheetWrapper(
                //     context: context, child: const StoreFilterBottomsheet());
              },
            ),
            SizedBox(
              height: 16.h,
            ),
            Builder(builder: (context) {
              if (posVm.state == ViewState.busy) {
                return const Skeletonizer(
                  enabled: true,
                  child: BasicSalesCard(),
                );
              }

              if (posVm.state == ViewState.error) {
                return SizedBox(
                  height: 175.h,
                  child: ErrorState(
                    message: posVm.message,
                    onPressed: () => posVm.fetchSalesOverview(),
                  ),
                );
              }
              if (posVm.salesOverviewResponse == null &&
                  posVm.state != ViewState.busy) {
                return DashBoardEmptyState(
                  title: "No Data",
                  subTitle:
                      "No Sales Overview Date Yet. Add Product to Start Making Sales",
                  buttonText: "Add New Product",
                  onPressed: () {},
                );
              }

              List<double> revenueData = posVm.salesOverviewResponse?.data
                      ?.map((e) => e.revenue ?? 0)
                      .toList() ??
                  [];

              List<String> monthLabels = posVm.salesOverviewResponse?.data
                      ?.map((e) => e.month ?? '')
                      .toList() ??
                  [];

              // Find the maximum revenue for scaling (with a minimum to handle all zeros)
              double maxRevenue = revenueData.reduce((a, b) => a > b ? a : b);
              if (maxRevenue == 0) maxRevenue = 100000;

              return ReusableSalesOverviewCard(
                subtitle: 'This Month',
                animationDuration: const Duration(milliseconds: 1500),
                staggerDelay: const Duration(milliseconds: 150),
                animationCurve: Curves.elasticOut,
                data: revenueData,
                labels: monthLabels,
                barColor: ColorPath.rose30,
                barWidth: 16.w,
                showLabels: true,
                maxValue: maxRevenue,
                yAxisLabels: generateYAxisLabels(maxRevenue),
              );
            }),
            SizedBox(height: 32.h),
            DashBoardHeader(
              title: "Recent Customers",
              subtitle: "Track and manage customers",
              onPressed: () {},
              showCTA: 1 + 1 == 3,
              child: Row(
                children: [
                  Text(
                    "View All",
                    style: textTheme.bodySmall,
                  ),
                  Icon(
                    Icons.north_east,
                    size: 14.w,
                    color: ColorPath.flamingo,
                  )
                ],
              ),
            ),
            LoadableContentBuilder(
              isBusy: ref.watch(customerViewModel).state == ViewState.busy,
              items: ref.watch(customerViewModel).recentCustomers,
              isError: ref.watch(customerViewModel).state == ViewState.error,
              loadingBuilder: (context) {
                return ListView.separated(
                  padding: EdgeInsets.only(
                    top: 16,
                    left: 16.w,
                    right: 16.w,
                  ),
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (ctx, i) {
                    return const Skeletonizer(
                      enabled: true,
                      child: CustomCustomerCard(
                          customerName: 'Adekunle Ibrahim',
                          customerEmail: "<EMAIL>",
                          totalAmount: "200789.90",
                          totalTransactions: "34"),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(height: 16.h);
                  },
                  itemCount:
                      ref.watch(customerViewModel).customerResponse.length,
                );
              },
              errorBuilder: (p0) {
                return SizedBox(
                  height: 175.h,
                  child: ErrorState(
                    message: ref.watch(customerViewModel).message,
                    onPressed: () =>
                        ref.read(customerViewModel).fetchAllCustomers(),
                  ),
                );
              },
              emptyBuilder: (context) {
                return Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: DashBoardEmptyState(
                    title: "No Data",
                    subTitle: "You currently don’t have any customer added Yet",
                    buttonText: "Add New Customer",
                    onPressed: () {},
                  ),
                );
              },
              contentBuilder: (context) {
                return ListView.separated(
                  padding: EdgeInsets.only(
                    top: 16,
                    left: 16.w,
                    right: 16.w,
                  ),
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (ctx, i) {
                    final customer =
                        ref.watch(customerViewModel).customerResponse[i];
                    return CustomCustomerCard(
                      customerName: customer.customerName ?? '',
                      customerEmail: customer.customerEmail ?? '',
                      totalAmount: customer.salesOrdersSumOrderTotal ?? '',
                      totalTransactions:
                          customer.salesOrdersCount?.toString() ?? '',
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(height: 16.h);
                  },
                  itemCount:
                      ref.watch(customerViewModel).customerResponse.length,
                );
              },
            ),
            SizedBox(
              height: 32.h,
            ),
            DashBoardHeader(
              title: "Customers analyses",
              subtitle: "See you customer distributions",
              onPressed: () {},
              child: Row(
                children: [
                  Text(
                    "View All",
                    style: textTheme.bodySmall,
                  ),
                  Icon(
                    Icons.north_east,
                    size: 14.w,
                    color: ColorPath.flamingo,
                  )
                ],
              ),
            ),
            SizedBox(height: 20.h),
            Builder(builder: (context) {
              if (posVm.state == ViewState.busy) {
                return const Skeletonizer(
                  enabled: true,
                  child: BasicSalesCard(),
                );
              }
              if (posVm.state == ViewState.error) {
                return SizedBox(
                  height: 175.h,
                  child: ErrorState(
                    message: posVm.message,
                    onPressed: () => posVm.fetchCustomerStats(),
                  ),
                );
              }
              if (posVm.salesOverviewResponse == null &&
                  posVm.state != ViewState.busy) {
                return Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: DashBoardEmptyState(
                    title: "No Data",
                    subTitle: "You currently don’t have any customer added Yet",
                    buttonText: "Add New Customer",
                    onPressed: () {},
                  ),
                );
              }

              List<double> customerData = [
                posVm.customerStatsData?.newCustomersPercentage?.toDouble() ??
                    0,
                posVm.customerStatsData?.existingCustomersPercentage
                        ?.toDouble() ??
                    0,
              ];

              double maxCustomerData =
                  customerData.reduce((a, b) => a > b ? a : b);
              if (maxCustomerData == 0) maxCustomerData = 100;

              return Padding(
                padding: EdgeInsets.only(left: 16.w, right: 16.w),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      "Total Customer",
                      style: textTheme.bodySmall?.copyWith(
                          fontSize: 12.sp,
                          fontWeight: FontWeight.w400,
                          color: colorScheme.subTextSecondary),
                    ),
                    SizedBox(height: 2.h),
                    Text(
                      "323",
                      style: textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          fontSize: 20.sp,
                          color: colorScheme.subTextSecondary),
                    ),
                    SizedBox(height: 16.h),
                    ReusableSalesOverviewCard(
                      data: customerData,
                      labels: null, // No x-axis labels
                      showLabels: false, // Hide labels
                      barColors: const [
                        ColorPath.rose30,
                        ColorPath.primary900,
                      ],
                      backgroundColor: Colors.grey[50]!,
                      yAxisLabels: generateYAxisLabels(maxCustomerData),
                      maxValue: maxCustomerData,
                      chartHeight: 280,
                      barWidth: 96.w,
                      titleFontSize: 16,
                      titleFontWeight: FontWeight.w500,
                      titleTextStyle: const TextStyle(
                        color: Colors.grey,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                      borderRadius: BorderRadius.circular(16),
                      padding: EdgeInsets.zero,
                    ),
                    SizedBox(height: 16.h),
                    if (posVm.customerStatsData?.newCustomers != null)
                      ChartLabelTile(
                        label: "New Customers",
                        value: "${posVm.customerStatsData?.newCustomers}",
                        color: ColorPath.rose30,
                      ),
                    if (posVm.customerStatsData?.existingCustomers != null)
                      ChartLabelTile(
                        label: "Existing Customers",
                        value: "${posVm.customerStatsData?.existingCustomers}",
                        color: ColorPath.primary900,
                      ),
                  ],
                ),
              );
            }),
            SizedBox(height: 32.h),
            DashBoardHeader(
              title: "Orders",
              subtitle: "Manage your Order with ease",
              onPressed: () {},
              titleTagColor: ColorPath.flamingo.withOpacity(.3),
              titleTagText:
                  ref.watch(salesViewModel).recentSalesOrder.length.toString(),
              child: Row(
                children: [
                  Text(
                    "View All",
                    style: textTheme.bodySmall,
                  ),
                  Icon(
                    Icons.north_east,
                    size: 14.w,
                    color: ColorPath.flamingo,
                  )
                ],
              ),
            ),
            LoadableContentBuilder(
              isBusy: ref.watch(salesViewModel).state == ViewState.busy,
              items: ref.watch(salesViewModel).salesOrderResponse,
              loadingBuilder: (context) {
                return ListView.separated(
                  padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h),
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return const SalesOrderCard(
                      orderNumber: "#6784",
                      numberOfItem: "20",
                      totalAmount: "200789.90",
                      status: "Pending",
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(
                      height: 16.h,
                    );
                  },
                  itemCount: 3,
                );
              },
              emptyBuilder: (context) {
                return Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: DashBoardEmptyState(
                    title: "No Data",
                    subTitle: "You currently don’t have any Order added Yet",
                    buttonText: "Add New Customer",
                    onPressed: () {},
                  ),
                );
              },
              contentBuilder: (context) {
                return ListView.separated(
                  padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h),
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (ctx, i) {
                    final order = ref.watch(salesViewModel).recentSalesOrder[i];
                    return SalesOrderCard(
                      orderNumber: order.orderNumber ?? '',
                      numberOfItem:
                          order.saleOrderDetails?.length.toString() ?? '',
                      totalAmount: order.orderTotal ?? '',
                      status: order.status ?? '',
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(
                      height: 16.h,
                    );
                  },
                  itemCount: ref.watch(salesViewModel).recentSalesOrder.length,
                );
              },
            )
          ],
        ),
      ),
    );
  }
}

class ChartLabelTile extends StatelessWidget {
  const ChartLabelTile({
    super.key,
    this.color,
    required this.label,
    required this.value,
  });

  final Color? color;
  final String label;
  final String value;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Padding(
      padding: EdgeInsets.only(top: 12.h),
      child: Row(
        children: [
          Container(
            height: 16.h,
            width: 8.w,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(2.r),
              color: color ?? ColorPath.rose30,
            ),
          ),
          SizedBox(width: 8.w),
          Text(
            label,
            style: textTheme.bodySmall?.copyWith(
                fontSize: 14.sp,
                fontWeight: FontWeight.w400,
                color: colorScheme.subTextSecondary),
          ),
          const Spacer(),
          Text(
            value,
            style: textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.w500,
                fontSize: 14.sp,
                color: colorScheme.subTextSecondary),
          ),
        ],
      ),
    );
  }
}
