import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';

class CreateSalesReturns extends StatefulWidget {
  const CreateSalesReturns({super.key});

  @override
  State<CreateSalesReturns> createState() => _CreateSalesReturnsState();
}

class _CreateSalesReturnsState extends State<CreateSalesReturns> {
  @override
  Widget build(BuildContext context) {
    // final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
        context: context,
        // preferredHeight: 16,
        title: "Create a Sales Return",
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomText(
              text: 'CREATE SALES RETURN',
              fontWeight: FontWeight.w500,
              fontColor: colorScheme.text4,
            ),
            SizedBox(
              height: 24.h,
            ),
            CustomTextField(
              label: 'Customer Name',
              hintText: 'Select Cutomer name',
              isCompulsory: false,
              suffixIcon: Padding(
                padding: EdgeInsets.only(right: 12.w),
                child: const Icon(
                  Icons.keyboard_arrow_down,
                  size: 16,
                ),
              ),
            ),
            SizedBox(
              height: 24.h,
            ),
            const CustomTextField(
              label: 'Order Number',
              hintText: 'Enter Unique code',
              isCompulsory: false,
            ),
            SizedBox(
              height: 24.h,
            ),
            const CustomTextField(
              label: 'Product Number',
              hintText: 'Enter Unique code',
              isCompulsory: false,
            ),
            SizedBox(
              height: 24.h,
            ),
            Clickable(
              onPressed: () {},
              child: CustomTextField(
                label: 'Reason for Refund',
                hintText: 'Select Option',
                isCompulsory: false,
                enabled: false,
                suffixIcon: Padding(
                  padding: EdgeInsets.only(right: 12.w),
                  child: const Icon(
                    Icons.keyboard_arrow_down,
                    size: 16,
                  ),
                ),
              ),
            ),
            SizedBox(
              height: 24.h,
            ),
            const CustomTextField(
              label: 'Description ',
              hintText: 'Start typing',
              isCompulsory: false,
            ),
            SizedBox(
              height: 32.h,
            ),
            CustomButton(
              onPressed: () {
                // bottomSheetWrapper(
                //     context: context,
                //     child: CustomBottomSheet(
                //       title: "Create a Sales Return ?",
                //       subTitle:
                //           "Are you sure you want to Create this sales return  with it associated details?",
                //       firstButtonText: "Yes, Create Sales return",
                //       secondButtonText: "No, Close",
                //       onPressedFirst: () {},
                //       onPressedSecond: () {},
                //     ));

                // bottomSheetWrapper(
                //     context: context,
                //     child: CustomBottomSheet(
                //       title: "Sales Return Created",
                //       subTitle:
                //           "Congratulations, you have successfully Created a Sales Return",
                //       firstButtonText: "Manage all Sales",
                //       secondButtonText: "Create New Sales Return ",
                //       onPressedFirst: () {},
                //       onPressedSecond: () {},
                //     ));
              },
              buttonText: "Create Discount",
            )
          ],
        ),
      ),
    );
  }
}
