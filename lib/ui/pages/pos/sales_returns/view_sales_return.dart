import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/take_sales_return_action_bottomsheet.dart';
import 'package:quick_retail_mobile/ui/widgets/color_tag.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard_header.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/product_image_widget.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

import '../../../widgets/bottom_sheets/take_transaction_action_bottomsheet.dart';

class ViewSalesReturn extends StatefulWidget {
  const ViewSalesReturn({super.key});

  @override
  State<ViewSalesReturn> createState() => _ViewSalesReturnState();
}

class _ViewSalesReturnState extends State<ViewSalesReturn> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
        context: context,
        // preferredHeight: 16,
        title: "View Sales Return",
        centerTitle: true,
      ),
      body: ListView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
        children: [
          const DashBoardHeader(
            title: "Basic details",
            subtitle: "See details of a specific returns",
            padding: EdgeInsets.zero,
            showCTA: false,
          ),
          SizedBox(
            height: 16.h,
          ),
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: ColorPath.athensGrey)),
            child: Column(
              children: [
                PaymentBreakdownItem(
                  title: "Transaction ID:",
                  child: Text(
                    "894GJAbGJS902",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Transaction Date:",
                  child: Text(
                    "May 11, 2025",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Transaction Time:",
                  child: Text(
                    "10:00 am",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Order ID:",
                  child: Text(
                    "7840",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Total Sales return Value:",
                  child: NairaDisplay(
                    amount: 1903484,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: colorScheme.text4,
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Status:",
                  child: ColorTag(
                    color:
                        1 + 1 == 3 ? ColorPath.earlyDawn : ColorPath.foamGreen,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.circle,
                          size: 6,
                          color: 1 + 1 == 3
                              ? ColorPath.californiaOrange
                              : ColorPath.meadowGreen,
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Text(
                          "Active",
                          style: textTheme.bodySmall?.copyWith(
                              color: 1 + 1 == 3
                                  ? ColorPath.vesuvius
                                  : ColorPath.funGreen),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Resolved by:",
                  child: Text(
                    "Jide Jadesola",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 24.h,
          ),
          const DashBoardHeader(
            title: "Customer details",
            subtitle: "Customer information for returns",
            padding: EdgeInsets.zero,
            showCTA: false,
          ),
          SizedBox(
            height: 16.h,
          ),
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: ColorPath.athensGrey)),
            child: Column(
              children: [
                PaymentBreakdownItem(
                  title: "Customer Details",
                  child: Text(
                    "Dairo Isaac (ID:9040)",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Customer ID",
                  child: Text(
                    "17833",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Customer Email:",
                  child: Text(
                    "<EMAIL>",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 32.h,
          ),
          const ScreenTitle(
              title: "Returned product",
              subTitle: "Product / Items where sales return is applicable."),
          SizedBox(
            height: 14.h,
          ),
          ListView.separated(
              padding: EdgeInsets.only(top: 16.h),
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemBuilder: (context, index) {
                return Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(color: ColorPath.athensGrey)),
                  child: Row(
                    children: [
                      const ProductImageWidget(),
                      SizedBox(
                        width: 8.w,
                      ),
                      Expanded(
                          child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "Product Name",
                            style: textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.w500),
                          ),
                          SizedBox(
                            height: 8.h,
                          ),
                          Text.rich(
                            TextSpan(text: "Quantity: ", children: [
                              TextSpan(
                                  text: "02",
                                  style: textTheme.bodySmall?.copyWith(
                                      color: colorScheme.text4,
                                      fontWeight: FontWeight.bold))
                            ]),
                            style: textTheme.bodySmall
                                ?.copyWith(color: colorScheme.subTextSecondary),
                          )
                        ],
                      )),
                      SizedBox(
                        width: 12.w,
                      ),
                      Expanded(
                          child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          Text(
                            "Total Amount",
                            style: textTheme.bodySmall
                                ?.copyWith(color: colorScheme.subTextSecondary),
                          ),
                          SizedBox(
                            height: 8.h,
                          ),
                          NairaDisplay(
                            amount: double.parse("200789.90"),
                            fontSize: 16.sp,
                            color: colorScheme.text4,
                          ),
                        ],
                      )),
                    ],
                  ),
                );
              },
              separatorBuilder: (context, index) {
                return SizedBox(
                  height: 16.h,
                );
              },
              itemCount: 3),
          SizedBox(
            height: 168.h,
          )
        ],
      ),
      bottomSheet: 1 + 1 == 2 // if Take Action
          ? Container(
              height: 163.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
              decoration: BoxDecoration(color: Colors.white, boxShadow: [
                BoxShadow(
                    color: ColorPath.mischkaGrey,
                    blurRadius: 4.r,
                    spreadRadius: 4.r,
                    blurStyle: BlurStyle.outer)
              ]),
              child: Column(
                children: [
                  Text(
                    "Take action on a specific Sales return",
                    style:
                        textTheme.bodySmall?.copyWith(color: colorScheme.text7),
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  CustomButton(
                    onPressed: () {
                      bottomSheetWrapper(context: context, child:  TakeSalesReturnActionBottomsheet());
                    },
                    borderColor: ColorPath.flamingo,
                    bgColor: Colors.white,
                    buttonTextColor: ColorPath.flamingo,
                    buttonText: "Take Action",
                  )
                ],
              ),
            )
          : const SizedBox(),
    );
  }
}

// Widgets
class PaymentBreakdownItem extends StatelessWidget {
  final String title;
  final Widget? child;
  const PaymentBreakdownItem({super.key, this.title = '', this.child});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Row(
      children: [
        Expanded(
            child: Text(
          title,
          style: textTheme.bodySmall?.copyWith(color: colorScheme.text7),
        )),
        Row(
          children: [
            child ?? Container(),
          ],
        )
      ],
    );
  }
}
