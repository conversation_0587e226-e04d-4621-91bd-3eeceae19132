import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/app_svg.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/ui/pages/pos/category/create_category_screen.dart';
import 'package:quick_retail_mobile/ui/pages/pos/category/tabs/category_tab.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';

// Category model class
class CategoryData {
  final String name;
  final int productCount;
  final int subCategoryCount;
  final bool isActive;

  CategoryData({
    required this.name,
    required this.productCount,
    required this.subCategoryCount,
    this.isActive = true,
  });
}

// SubCategory model class
class SubCategoryData {
  final String name;
  final String parentCategory;
  final int productCount;
  final bool isActive;

  SubCategoryData({
    required this.name,
    required this.parentCategory,
    required this.productCount,
    this.isActive = true,
  });
}

class CategoryScreen extends StatefulWidget {
  const CategoryScreen({super.key});

  @override
  State<CategoryScreen> createState() => _CategoryScreenState();
}

class _CategoryScreenState extends State<CategoryScreen>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();

  // Mock data for subcategories
  final List<SubCategoryData> _subCategories = [
    SubCategoryData(
      name: "Smartphones",
      parentCategory: "Electronics",
      productCount: 45,
    ),
    SubCategoryData(
      name: "Laptops",
      parentCategory: "Electronics",
      productCount: 32,
    ),
    SubCategoryData(
      name: "T-Shirts",
      parentCategory: "Clothing",
      productCount: 78,
    ),
    SubCategoryData(
      name: "Jeans",
      parentCategory: "Clothing",
      productCount: 56,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: customAppBar(
        context: context,
        title: 'Categories',
        centerTitle: true,
        actions: [
          Padding(
            padding: EdgeInsets.only(right: 16.w),
            child: Row(
              children: [
                Clickable(
                  onPressed: () {
                    pushNavigation(
                        context: context,
                        widget: const CreateCategoryScreen(),
                        routeName: NamedRoutes.createCategory);
                  },
                  child: Container(
                    padding: EdgeInsets.all(6.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      color: ColorPath.flamingo,
                    ),
                    child: Icon(
                      Icons.add,
                      size: 24.w,
                      color: ColorPath.white,
                    ),
                  ),
                )
              ],
            ),
          )
        ],
      ),
      body: Column(
        children: [
          // Search bar
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 16.h),
            child: Row(
              children: [
                Expanded(
                  child: Container(
                    height: 46.h,
                    decoration: BoxDecoration(
                      color: Colors.white,
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(color: ColorPath.athensGrey),
                    ),
                    child: Row(
                      children: [
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 12.w),
                          child: Icon(
                            Icons.search,
                            color: ColorPath.paleGrey,
                            size: 24.w,
                          ),
                        ),
                        Expanded(
                          child: TextField(
                            controller: _searchController,
                            decoration: InputDecoration(
                              hintText: 'Search by keyword',
                              hintStyle: TextStyle(
                                color: ColorPath.paleGrey,
                                fontSize: 14.sp,
                              ),
                              border: InputBorder.none,
                            ),
                          ),
                        ),
                        Padding(
                          padding: EdgeInsets.symmetric(horizontal: 12.w),
                          child: Clickable(
                            onPressed: () {
                              // QR code scanner functionality
                            },
                            child: SvgPicture.asset(AppSvg.scan),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(width: 10.w),
                Container(
                    padding: EdgeInsets.all(8.w),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      color: ColorPath.flamingo,
                    ),
                    child: SvgPicture.asset(
                      AppSvg.filter,
                      height: 24.h,
                    ))
              ],
            ),
          ),

          // Tab bar
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 16.w),
            child: Container(
              decoration: BoxDecoration(
                color: ColorPath.white,
                borderRadius: BorderRadius.circular(8.r),
              ),
              child: TabBar(
                controller: _tabController,
                indicatorSize: TabBarIndicatorSize.tab,
                indicator: CustomTabIndicator(),
                // indicator: BoxDecoration(
                //   // color: Colors.white,
                //   borderRadius: BorderRadius.circular(8.r),
                // ),
                labelColor: ColorPath.ebonyBlack,
                dividerColor: Colors.transparent,
                unselectedLabelColor: ColorPath.gullGrey,
                tabs: [
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text('Categories'),
                        SizedBox(width: 4.w),
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 6.w, vertical: 2.h),
                          decoration: BoxDecoration(
                            color: ColorPath.seaShellPink,
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          child: Text(
                            '100',
                            style: TextStyle(
                              color: ColorPath.flamingo,
                              fontSize: 12.sp,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Text('Sub Categories'),
                        SizedBox(width: 4.w),
                        Container(
                          padding: EdgeInsets.symmetric(
                              horizontal: 6.w, vertical: 2.h),
                          decoration: BoxDecoration(
                            color: ColorPath.seaShellPink,
                            borderRadius: BorderRadius.circular(12.r),
                          ),
                          child: Text(
                            '100',
                            style: TextStyle(
                              color: ColorPath.flamingo,
                              fontSize: 12.sp,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Tab bar view
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: const [
                // Categories tab
                CategoryTab(),
                CategoryTab(),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

class CustomTabIndicator extends Decoration {
  @override
  BoxPainter createBoxPainter([VoidCallback? onChanged]) {
    return _CustomPainter();
  }
}

class _CustomPainter extends BoxPainter {
  @override
  void paint(Canvas canvas, Offset offset, ImageConfiguration configuration) {
    final Rect rect = offset & configuration.size!;
    final Paint paint = Paint();
    paint.color = ColorPath.rose30;
    paint.style = PaintingStyle.fill;
    paint.strokeWidth = 1.0;

    // Draw the divider line at the bottom
    canvas.drawLine(
      Offset(rect.left, rect.bottom),
      Offset(rect.right, rect.bottom),
      paint,
    );
  }
}
