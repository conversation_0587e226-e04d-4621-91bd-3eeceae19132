import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/app_svg.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/add_category_bottomsheet.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/category_confirm_bottomsheet.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

class CreateSubCategoryScreen extends StatefulWidget {
  const CreateSubCategoryScreen({super.key});

  @override
  State<CreateSubCategoryScreen> createState() =>
      _CreateSubCategoryScreenState();
}

class _CreateSubCategoryScreenState extends State<CreateSubCategoryScreen> {
  final TextEditingController _nameController = TextEditingController();
  final TextEditingController _categoryController = TextEditingController();
  List<CategoryItem> _selectedCategories = [];

  @override
  void dispose() {
    _nameController.dispose();
    _categoryController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: customAppBar(
          context: context, title: 'Create Category', centerTitle: true),
      body: SingleChildScrollView(
        padding:
            EdgeInsets.only(left: 16.w, right: 16.w, top: 20.h, bottom: 48.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const ScreenTitle(
              title: "Create a Sub-Category",
              subTitle: "Give your sub-category a name below.",
            ),
            SizedBox(height: 32.h),
            CustomTextField(
              controller: _nameController,
              label: 'Sub-Category Name',
              hintText: "Enter Name",
              isCompulsory: false,
              borderRadius: 6,
            ),
            Text(
              "Enter a short and unique name",
              style: textTheme.bodyMedium?.copyWith(color: ColorPath.paleGrey),
            ),
            SizedBox(height: 32.h),
            CustomTextField(
              controller: _categoryController,
              label: 'Add to Category *',
              hintText: _selectedCategories.isEmpty
                  ? "Select an Option"
                  : "${_selectedCategories.length} categories selected",
              isCompulsory: false,
              readOnly: true,
              suffixIcon: Clickable(
                onPressed: () {
                  bottomSheetWrapper(
                    context: context,
                    child: AddCategoryBottomsheet(
                      onCategoriesSelected: (selectedCategories) {
                        setState(() {
                          _selectedCategories = selectedCategories;
                          if (_selectedCategories.isNotEmpty) {
                            _categoryController.text = _selectedCategories
                                .map((category) => category.name)
                                .join(', ');
                          } else {
                            _categoryController.text = "";
                          }
                        });
                      },
                    ),
                  );
                },
                child: Padding(
                  padding: const EdgeInsets.only(right: 16.0),
                  child: SvgPicture.asset(AppSvg.arrowDown),
                ),
              ),
            ),
            SizedBox(height: 48.h),
            CustomButton(
              onPressed: () {
                bottomSheetWrapper(
                  context: context,
                  child: const CategoryConfirmBottomsheet(),
                );
              },
              bgColor: ColorPath.flamingo,
              buttonText: "Create New",
            )
          ],
        ),
      ),
    );
  }
}
