import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/ui/pages/pos/category/create_sub_category_screen.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

class CreateCategoryScreen extends StatefulWidget {
  const CreateCategoryScreen({super.key});

  @override
  State<CreateCategoryScreen> createState() => _CreateCategoryScreenState();
}

class _CreateCategoryScreenState extends State<CreateCategoryScreen> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;

    return Scaffold(
      appBar: customAppBar(
          context: context, title: 'Create Category', centerTitle: true),
      body: SingleChildScrollView(
        padding:
            EdgeInsets.only(left: 16.w, right: 16.w, top: 20.h, bottom: 48.h),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const ScreenTitle(
              title: "Create Category",
              subTitle: "Give your category a name below.",
            ),
            SizedBox(height: 32.h),
            const CustomTextField(
              label: 'Category Name',
              hintText: "Groceries",
              isCompulsory: false,
              readOnly: true,
            ),
            Text(
              "Enter a short and unique name",
              style: textTheme.bodyMedium?.copyWith(color: ColorPath.paleGrey),
            ),
            SizedBox(
              height: 48.h,
            ),
            CustomButton(
              onPressed: () {
                pushNavigation(
                    context: context,
                    widget: const CreateSubCategoryScreen(),
                    routeName: NamedRoutes.createSubCategory);
              },
              bgColor:
                  1 + 1 == 3 ? ColorPath.inactiveFlamingo : ColorPath.flamingo,
              buttonText: "Create New",
            )
          ],
        ),
      ),
    );
  }
}
