import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/app_svg.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/ui/pages/pos/category/build_category_item.dart';
import 'package:quick_retail_mobile/ui/pages/pos/category/category_screen.dart';

class CategoryTab extends StatefulWidget {
  const CategoryTab({super.key});

  @override
  State<CategoryTab> createState() => _CategoryTabState();
}

class _CategoryTabState extends State<CategoryTab> {
  // Mock data for categories
  final List<CategoryData> _categories = [
    CategoryData(name: "Category Name", productCount: 78, subCategoryCount: 6),
    CategoryData(name: "Electronics", productCount: 120, subCategoryCount: 10),
    CategoryData(name: "Clothing", productCount: 250, subCategoryCount: 15),
  ];

  // Mock data for inactive categories
  final List<CategoryData> _inactiveCategories = [
    CategoryData(
        name: "Discontinued",
        productCount: 12,
        subCategoryCount: 2,
        isActive: false),
    CategoryData(
        name: "Seasonal",
        productCount: 45,
        subCategoryCount: 3,
        isActive: false),
  ];
  @override
  Widget build(BuildContext context) {
    return ListView(
      padding:
          EdgeInsets.only(left: 16.w, right: 16.w, top: 24.h, bottom: 48.h),
      children: [
        // Summary card
        Container(
          padding: EdgeInsets.all(16.w),
          decoration: BoxDecoration(
            color: ColorPath.seaShellPink,
            borderRadius: BorderRadius.circular(8.r),
            border: Border.all(color: ColorPath.rose30),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'NUMBER OF CATEGORY',
                style: TextStyle(
                  color: ColorPath.fiordGrey,
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
              SizedBox(height: 8.h),
              Text(
                '32',
                style: TextStyle(
                  color: ColorPath.flamingo,
                  fontSize: 24.sp,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 4.h),
              Row(
                children: [
                  Container(
                    padding:
                        EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                    decoration: BoxDecoration(
                      color: ColorPath.success200,
                      borderRadius: BorderRadius.circular(12.r),
                    ),
                    child: Row(
                      children: [
                        Text(
                          '0.5',
                          style: TextStyle(
                            color: ColorPath.white,
                            fontSize: 12.sp,
                          ),
                        ),
                        SizedBox(width: 2.w),
                        SvgPicture.asset(AppSvg.arrowUpRight)
                      ],
                    ),
                  ),
                  SizedBox(width: 4.w),
                  Text(
                    'last 3 days',
                    style: TextStyle(
                      color: ColorPath.oxfordBlue,
                      fontSize: 12.sp,
                    ),
                  ),
                ],
              ),
              SizedBox(height: 16.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Active',
                        style: TextStyle(
                          color: ColorPath.oxfordBlue,
                          fontSize: 12.sp,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        '30',
                        style: TextStyle(
                          color: ColorPath.meadowGreen,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Inactive',
                        style: TextStyle(
                          color: ColorPath.oxfordBlue,
                          fontSize: 12.sp,
                        ),
                      ),
                      SizedBox(height: 4.h),
                      Text(
                        '2',
                        style: TextStyle(
                          color: ColorPath.flamingoRed,
                          fontSize: 14.sp,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          ),
        ),

        SizedBox(height: 24.h),

        // Active categories
        ..._categories.map((category) => const BuildCategoryItem()),

        // Inactive categories
        ..._inactiveCategories.map((category) => const BuildCategoryItem()),
      ],
    );
  }
}
