import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/ui/widgets/color_tag.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

class ViewCustomer extends StatefulWidget {
  const ViewCustomer({super.key});

  @override
  State<ViewCustomer> createState() => _ViewCustomerState();
}

class _ViewCustomerState extends State<ViewCustomer> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
          context: context, title: 'View a Customer', centerTitle: true),
      body: ListView(
        padding:
            EdgeInsets.only(top: 16.h, bottom: 32.h, left: 16.w, right: 16.w),
        children: [
          const ScreenTitle(
              title: "View Customer Details",
              subTitle: "See details of a specific Customer"),
          SizedBox(
            height: 14.h,
          ),
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: ColorPath.athensGrey)),
            child: Column(
              children: [
                PaymentBreakdownItem(
                  title: "Customer Name",
                  child: Text(
                    "Dairo Isaac",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Customer ID",
                  child: Text(
                    "8944",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Last Active",
                  child: Text(
                    "April 11, 2024 | 11:00 am ",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Status:",
                  child: ColorTag(
                    color:
                        1 + 1 == 3 ? ColorPath.earlyDawn : ColorPath.foamGreen,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.circle,
                          size: 6,
                          color: 1 + 1 == 3
                              ? ColorPath.californiaOrange
                              : ColorPath.meadowGreen,
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Text(
                          "Active",
                          style: textTheme.bodySmall?.copyWith(
                              color: 1 + 1 == 3
                                  ? ColorPath.vesuvius
                                  : ColorPath.funGreen),
                        ),
                      ],
                    ),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Total Amount Spent So far:",
                  child: NairaDisplay(
                    amount: 1903484,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: colorScheme.text4,
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 32.h,
          ),
          const ScreenTitle(
              title: "Customer Order",
              subTitle: "Manage your Customer Order with ease"),
          SizedBox(
            height: 14.h,
          ),
          ListView.separated(
              padding: EdgeInsets.only(top: 16.h),
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemBuilder: (context, index) {
                return Container(
                  padding: EdgeInsets.all(16.w),
                  decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(8.r),
                      border: Border.all(color: ColorPath.athensGrey)),
                  child: Row(
                    children: [
                      Expanded(
                          child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            "#6784",
                            style: textTheme.bodySmall
                                ?.copyWith(fontWeight: FontWeight.w500),
                          ),
                          SizedBox(
                            height: 8.h,
                          ),
                          Text.rich(
                            TextSpan(text: "Number of Item: ", children: [
                              TextSpan(
                                  text: "20",
                                  style: textTheme.bodySmall?.copyWith(
                                      color: colorScheme.text4,
                                      fontWeight: FontWeight.bold))
                            ]),
                            style: textTheme.bodySmall
                                ?.copyWith(color: colorScheme.subTextSecondary),
                          )
                        ],
                      )),
                      SizedBox(
                        width: 12.w,
                      ),
                      Expanded(
                          child: Column(
                        crossAxisAlignment: CrossAxisAlignment.end,
                        children: [
                          NairaDisplay(
                            amount: double.parse("200789.90"),
                            fontSize: 16.sp,
                            color: colorScheme.text4,
                          ),
                          SizedBox(
                            height: 8.h,
                          ),
                          ColorTag(
                            color: 1 + 1 == 2
                                ? ColorPath.earlyDawn
                                : ColorPath.foamGreen,
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.circle,
                                  size: 6,
                                  color: 1 + 1 == 2
                                      ? ColorPath.californiaOrange
                                      : ColorPath.meadowGreen,
                                ),
                                SizedBox(
                                  width: 8.w,
                                ),
                                Text(
                                  "Pending",
                                  style: textTheme.bodySmall?.copyWith(
                                      color: 1 + 1 == 2
                                          ? ColorPath.vesuvius
                                          : ColorPath.funGreen),
                                ),
                              ],
                            ),
                          )
                        ],
                      )),
                    ],
                  ),
                );
              },
              separatorBuilder: (context, index) {
                return SizedBox(
                  height: 16.h,
                );
              },
              itemCount: 3),
          SizedBox(
            height: 168.h,
          )
        ],
      ),
      bottomSheet: 1 + 1 == 2 // if Take Action
          ? Container(
              height: 163.h,
              padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
              decoration: BoxDecoration(color: Colors.white, boxShadow: [
                BoxShadow(
                    color: ColorPath.mischkaGrey,
                    blurRadius: 4.r,
                    spreadRadius: 4.r,
                    blurStyle: BlurStyle.outer)
              ]),
              child: Column(
                children: [
                  Text(
                    "Edit, Delete or Deactivate a Sub-Category",
                    style:
                        textTheme.bodySmall?.copyWith(color: colorScheme.text7),
                  ),
                  SizedBox(
                    height: 16.h,
                  ),
                  CustomButton(
                    onPressed: () {
                      // bottomSheetWrapper(
                      //     context: context,
                      //     child: TakeTransactionActionBottomsheet());
                    },
                    borderColor: ColorPath.flamingo,
                    bgColor: Colors.white,
                    buttonTextColor: ColorPath.flamingo,
                    buttonText: "Take Action",
                  )
                ],
              ),
            )
          : const SizedBox(),
    );
  }
}

class PaymentBreakdownItem extends StatelessWidget {
  final String title;
  final Widget? child;
  const PaymentBreakdownItem({super.key, this.title = '', this.child});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Row(
      children: [
        Expanded(
            child: Text(
          title,
          style: textTheme.bodySmall?.copyWith(color: colorScheme.text7),
        )),
        Row(
          children: [
            child ?? Container(),
          ],
        )
      ],
    );
  }
}
