import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/ui/pages/pos/customer_management/view_customer.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/color_tag.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard/dashbaord_empty_state.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard_header.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

class CustomerManagement extends StatefulWidget {
  const CustomerManagement({super.key});

  @override
  State<CustomerManagement> createState() => _CustomerManagementState();
}

class _CustomerManagementState extends State<CustomerManagement> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
          context: context, title: 'Customer Management', centerTitle: true),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
        child: Column(
          children: [
            DashBoardHeader(
              title: "Overview",
              padding: EdgeInsets.zero,
              subtitle: "An overview of store transaction",
              filterText: "3 days ago",
              // showCTA: false,
              onPressed: () {},
            ),
            SizedBox(
              height: 16.h,
            ),
            const CustomerOverviewCard(),
            SizedBox(
              height: 32.h,
            ),
            DashBoardHeader(
              title: "Customer List",
              subtitle: "Manage your customers with ease",
              titleTagText: "30",
              padding: EdgeInsets.zero,
              titleTagColor: ColorPath.flamingo.withOpacity(.25),
              onPressed: () {},
              showCTA: 1 + 1 == 2,
              child: Row(
                children: [
                  Text(
                    "View All",
                    style: textTheme.bodySmall,
                  ),
                  Icon(
                    Icons.north_east,
                    size: 14.w,
                    color: ColorPath.flamingo,
                  )
                ],
              ),
            ),
            1 + 1 == 3
                ? Padding(
                    padding: const EdgeInsets.only(top: 16.0),
                    child: DashBoardEmptyState(
                      margin: EdgeInsets.zero,
                      title: "No Data",
                      subTitle:
                          "No Customer Data Yet. Add Product to start transacting today",
                      buttonText: "Add New Product",
                      onPressed: () {},
                    ),
                  )
                : ListView.separated(
                    padding: EdgeInsets.only(top: 16.h),
                    physics: const NeverScrollableScrollPhysics(),
                    shrinkWrap: true,
                    itemBuilder: (context, index) {
                      return Clickable(
                        onPressed: () {
                          //todo route to view customer
                          pushNavigation(
                              context: context,
                              widget: const ViewCustomer(),
                              routeName: NamedRoutes.viewCustomer);
                        },
                        child: Container(
                          padding: EdgeInsets.all(16.w),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.r),
                              border: Border.all(color: ColorPath.athensGrey)),
                          child: Row(
                            children: [
                              Expanded(
                                  child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    "Adekunle Ibrahim",
                                    style: textTheme.bodySmall
                                        ?.copyWith(fontWeight: FontWeight.w500),
                                  ),
                                  SizedBox(
                                    height: 8.h,
                                  ),
                                  Text.rich(
                                    TextSpan(text: "Amount Spent: ", children: [
                                      textspanNairaDisplay(
                                          amount: 800,
                                          color: colorScheme.text4,
                                          fontWeight: FontWeight.bold)
                                    ]),
                                    style: textTheme.bodySmall?.copyWith(
                                        color: colorScheme.subTextSecondary),
                                  )
                                ],
                              )),
                              SizedBox(
                                width: 12.w,
                              ),
                              Expanded(
                                  child: Column(
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  Text(
                                    "Status",
                                    style: textTheme.bodySmall?.copyWith(
                                        color: colorScheme.subTextSecondary),
                                  ),
                                  SizedBox(
                                    height: 8.h,
                                  ),
                                  ColorTag(
                                    color: 1 + 1 == 3
                                        ? ColorPath.earlyDawn
                                        : ColorPath.foamGreen,
                                    child: Row(
                                      mainAxisSize: MainAxisSize.min,
                                      children: [
                                        const Icon(
                                          Icons.circle,
                                          size: 6,
                                          color: 1 + 1 == 3
                                              ? ColorPath.californiaOrange
                                              : ColorPath.meadowGreen,
                                        ),
                                        SizedBox(
                                          width: 8.w,
                                        ),
                                        Text(
                                          "Active",
                                          style: textTheme.bodySmall?.copyWith(
                                              color: 1 + 1 == 3
                                                  ? ColorPath.vesuvius
                                                  : ColorPath.funGreen),
                                        ),
                                      ],
                                    ),
                                  )
                                ],
                              )),
                            ],
                          ),
                        ),
                      );
                    },
                    separatorBuilder: (context, index) {
                      return SizedBox(
                        height: 16.h,
                      );
                    },
                    itemCount: 3)
          ],
        ),
      ),
    );
  }
}

// Widgets
class CustomerOverviewCard extends StatelessWidget {
  const CustomerOverviewCard({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
          color: ColorPath.flamingo.withOpacity(.08),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: ColorPath.flamingoRed)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "TOTAL NUMBER OF CUSTOMERS",
            style: textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600, color: colorScheme.text7),
          ),
          SizedBox(
            height: 8.h,
          ),
          CustomText(
            text: "290",
            fontColor: colorScheme.text6,
            fontSize: 24.sp,
            fontWeight: FontWeight.bold,
          ),
          SizedBox(
            height: 8.h,
          ),
          Row(
            children: [
              ColorTag(
                color: ColorPath.emeraldGreen,
                child: Row(
                  children: [
                    Text(
                      '0.5',
                      style: textTheme.bodySmall?.copyWith(color: Colors.white),
                    ),
                    SizedBox(
                      width: 2.w,
                    ),
                    const Icon(
                      Icons.north_east,
                      size: 12,
                      color: Colors.white,
                    )
                  ],
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Text(
                'last 3 days',
                style: textTheme.bodySmall,
              )
            ],
          ),
          SizedBox(
            height: 16.h,
          ),
          Row(
            children: [
              Expanded(
                child: ScreenTitle(
                  title: 'Active',
                  titleSize: 14.sp,
                  titleFontWeight: FontWeight.w400,
                  subtitleFontWeight: FontWeight.w600,
                  subtitleColor: ColorPath.salemGreen,
                  subTitle: "1,000,000",
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Expanded(
                child: ScreenTitle(
                    title: 'Inactive',
                    titleSize: 14.sp,
                    titleFontWeight: FontWeight.w400,
                    subtitleFontWeight: FontWeight.w600,
                    subtitleColor: ColorPath.alizarinRed,
                    subTitle: "200"),
              ),
            ],
          )
        ],
      ),
    );
  }
}
