import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/data/models/quick_action_item.dart';
import 'package:quick_retail_mobile/core/data/services/navigation_service.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/locator.dart';
import 'package:quick_retail_mobile/ui/pages/pos/category/category_screen.dart';
import 'package:quick_retail_mobile/ui/pages/pos/store_management/store_management.dart';
import 'package:quick_retail_mobile/ui/pages/pos/transaction_management/transaction_dashboard.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

class CoreFeatures extends StatefulWidget {
  const CoreFeatures({super.key});

  @override
  State<CoreFeatures> createState() => _CoreFeaturesState();
}

class _CoreFeaturesState extends State<CoreFeatures> {
  final List quickActions = [
    QuickActionsItem(
      actionName: "Categories",
      assetName: "item",
      onPressed: () {
        debugPrint('Categories');
        pushNavigation(
            context: locator<NavigationService>().navigationKey.currentContext!,
            widget: const CategoryScreen(),
            routeName: NamedRoutes.category);
      },
    ),
    QuickActionsItem(
      actionName: "Transaction Management",
      assetName: "item",
      onPressed: () {
        pushNavigation(
            context: locator<NavigationService>().navigationKey.currentContext!,
            widget: const TransactionDashboard(),
            routeName: NamedRoutes.transactionDashboard);
      },
    ),
    QuickActionsItem(
      actionName: "Stores Management",
      assetName: "item",
      onPressed: () {
        pushNavigation(
            context: locator<NavigationService>().navigationKey.currentContext!,
            widget: const StoreManagement(),
            routeName: NamedRoutes.storeMgt);
      },
    ),
    QuickActionsItem(
      actionName: "Setting ",
      assetName: "item",
      onPressed: () {},
    ),
    QuickActionsItem(
      actionName: "Notification",
      assetName: "item",
      onPressed: () {},
    ),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: customAppBar(
          context: context, title: 'Core Features', centerTitle: true),
      body: ListView(
        padding:
            EdgeInsets.only(left: 16.w, right: 16.w, top: 32.h, bottom: 48.h),
        children: [
          const ScreenTitle(
              title: "Core Functionalties",
              subTitle:
                  "List of core functionalities. Do more with Quick Retail"),
          SizedBox(
            height: 24.h,
          ),
          ListView.separated(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemBuilder: (context, index) {
                return Clickable(
                  onPressed: quickActions[index].onPressed,
                  child: Container(
                    padding: EdgeInsets.all(16.w),
                    decoration: BoxDecoration(
                        border: Border.all(color: ColorPath.athensGrey),
                        borderRadius: BorderRadius.circular(8.r)),
                    child: Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(12.w),
                          decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(8.r),
                              color: ColorPath.flamingo.withOpacity(.2)),
                          child: SvgPicture.asset(
                              Utilities.getSvg(quickActions[index].assetName)),
                        ),
                        SizedBox(
                          width: 10.w,
                        ),
                        Text(quickActions[index].actionName)
                      ],
                    ),
                  ),
                );
              },
              separatorBuilder: (context, index) {
                return SizedBox(
                  height: 16.h,
                );
              },
              itemCount: quickActions.length)
        ],
      ),
    );
  }
}
