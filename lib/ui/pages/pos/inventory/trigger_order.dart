import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/ui/widgets/color_tag.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

class TriggerOrder extends StatefulWidget {
  const TriggerOrder({super.key});

  @override
  State<TriggerOrder> createState() => _TriggerOrderState();
}

class _TriggerOrderState extends State<TriggerOrder> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar: customAppBar(
        context: context, title: "Inventory Management", 
        centerTitle: true,
      ),
      body: ListView(
        padding:
            EdgeInsets.only(left: 16.w, right: 16.w, top: 32.h, bottom: 48.h),
        children: [
          const ScreenTitle(
              title: "Basic Details",
              subTitle: "A basic overview of inventory detail. "),
          SizedBox(
            height: 16.h,
          ),
          Container(
            padding: EdgeInsets.all(16.w),
            decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(8.r),
                border: Border.all(color: ColorPath.athensGrey)),
            child: Column(
              children: [
                PaymentBreakdownItem(
                  title: "Created by:",
                  child: Text(
                    "Jide Jimoh",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Last Modified:",
                  child: Text(
                    "May 11, 2025 | 10:00 am",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Category:",
                  child: Text(
                    "Fashion, General Wears, Men’s",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Sub-Category:",
                  child: Text(
                    "Sub-Category 01",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Stock Sold So far:",
                  child: Text(
                    "10",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Reorder Level:",
                  child: Text(
                    "2",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Location:",
                  child: Text(
                    "Ikeja Warehouse",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Current Stock Level:",
                  child: Text(
                    "2",
                    style: textTheme.bodySmall
                        ?.copyWith(fontWeight: FontWeight.w500),
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Total Stock Value:",
                  child: NairaDisplay(
                    amount: 1903484,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                    color: colorScheme.text4,
                  ),
                ),
                SizedBox(
                  height: 16.h,
                ),
                PaymentBreakdownItem(
                  title: "Status:",
                  child: ColorTag(
                    color:
                        1 + 1 == 2 ? ColorPath.earlyDawn : ColorPath.foamGreen,
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        const Icon(
                          Icons.circle,
                          size: 6,
                          color: 1 + 1 == 2
                              ? ColorPath.californiaOrange
                              : ColorPath.meadowGreen,
                        ),
                        SizedBox(
                          width: 8.w,
                        ),
                        Text(
                          "Low Stock",
                          style: textTheme.bodySmall?.copyWith(
                              color: 1 + 1 == 2
                                  ? ColorPath.vesuvius
                                  : ColorPath.funGreen),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
          SizedBox(
            height: 180.h,
          ),
          CustomButton(
            onPressed: () {},
            buttonText: "Trigger Reorder Level",
          )
        ],
      ),
    );
  }
}

// Widgets
class PaymentBreakdownItem extends StatelessWidget {
  final String title;
  final Widget? child;
  const PaymentBreakdownItem({super.key, this.title = '', this.child});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Row(
      children: [
        Expanded(
            child: Text(
          title,
          style: textTheme.bodySmall?.copyWith(color: colorScheme.text7),
        )),
        Row(
          children: [
            child ?? Container(),
          ],
        )
      ],
    );
  }
}
