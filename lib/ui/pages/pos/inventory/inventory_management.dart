import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/data/models/category_response.dart';
import 'package:quick_retail_mobile/core/data/models/product_response.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/pages/pos/inventory/trigger_order.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/color_tag.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_icon.dart';
import 'package:quick_retail_mobile/ui/widgets/naira_display.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard/dashbaord_empty_state.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard_header.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/product_item.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';

class InventoryManagement extends StatefulWidget {
  const InventoryManagement({super.key});

  @override
  State<InventoryManagement> createState() => _InventoryManagementState();
}

class _InventoryManagementState extends State<InventoryManagement> {
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Scaffold(
      appBar: customAppBar(
          context: context,
          showLeadingIcon: true,
          title: "Inventory Management", // Discount Name
          centerTitle: true,
          actions: [
            Padding(
              padding: EdgeInsets.only(right: 16.w),
              child: Row(
                children: [
                  CustomIcon(
                    imageAsset: Utilities.getSvg("export"),
                    bgColor: ColorPath.flamingo,
                    onPressed: () {
                      pushNavigation(
                          context: context,
                          widget: const TriggerOrder(),
                          routeName: NamedRoutes.triggerOrder);
                    },
                  ),
                  SizedBox(
                    width: 8.w,
                  ),
                ],
              ),
            )
          ]),
      body: ListView(
        padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 24.h),
        children: [
          const DiscountOverviewCard(),
          SizedBox(
            height: 32.h,
          ),
          DashBoardHeader(
            title: "List of Inventory",
            subtitle: "Manage your inventory with ease",
            titleTagText: "10",
            padding: EdgeInsets.zero,
            titleTagColor: ColorPath.flamingo.withOpacity(.25),
            showCTA: false,
          ),
          1 + 1 == 3
              ? Padding(
                  padding: const EdgeInsets.only(top: 16.0),
                  child: DashBoardEmptyState(
                    title: "No Inventory",
                    margin: EdgeInsets.zero,
                    subTitle: "You currently do now have any inventory added",
                    buttonText: "Add New Product",
                    onPressed: () {},
                  ),
                )
              : ListView.separated(
                  padding: EdgeInsets.only(top: 16.h),
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return Clickable(
                      onPressed: () {
                        // pushNavigation(
                        //     context: context, widget: const ViewSalesReturn());
                      },
                      child: ProductItem(
                        productData: ProductData(
                          name: 'product namw',
                          sellingPrice: '50000',
                          quantity: 25,
                          status: 'Active',
                          product: Product(
                            category: Category(name: 'Shoe'),
                            location: Location(name: 'Store 1'),
                          ),
                        ),
                      ),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(
                      height: 16.h,
                    );
                  },
                  itemCount: 3,
                )
        ],
      ),
    );
  }
}

//WIDGETS
class DiscountOverviewCard extends StatelessWidget {
  const DiscountOverviewCard({super.key});

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Container(
      padding: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
          color: ColorPath.flamingo.withOpacity(.08),
          borderRadius: BorderRadius.circular(8.r),
          border: Border.all(color: ColorPath.flamingoRed)),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            "TOTAL INVENTORY VALUE",
            style: textTheme.bodySmall?.copyWith(
                fontWeight: FontWeight.w600, color: colorScheme.text7),
          ),
          SizedBox(
            height: 8.h,
          ),
          NairaDisplay(
            amount: 280390,
            color: colorScheme.text6,
          ),
          SizedBox(
            height: 8.h,
          ),
          Row(
            children: [
              ColorTag(
                color: ColorPath.emeraldGreen,
                child: Row(
                  children: [
                    Text(
                      '4.9%',
                      style: textTheme.bodySmall?.copyWith(color: Colors.white),
                    ),
                    SizedBox(
                      width: 2.w,
                    ),
                    const Icon(
                      Icons.north_east,
                      size: 12,
                      color: Colors.white,
                    )
                  ],
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Text(
                'last 3 days',
                style: textTheme.bodySmall,
              )
            ],
          ),
          SizedBox(
            height: 16.h,
          ),
          Row(
            children: [
              Expanded(
                child: ScreenTitle(
                  title: 'No. of Inventory',
                  titleSize: 14.sp,
                  titleFontWeight: FontWeight.w400,
                  subtitleFontWeight: FontWeight.w600,
                  subtitleColor: ColorPath.mirageBlack,
                  subTitle: "200",
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Expanded(
                child: ScreenTitle(
                  title: 'Available Inventory',
                  titleSize: 14.sp,
                  titleFontWeight: FontWeight.w400,
                  subtitleFontWeight: FontWeight.w600,
                  subtitleColor: ColorPath.salemGreen,
                  crossAxisAlignment: CrossAxisAlignment.end,
                  mainAxisAlignment: MainAxisAlignment.end,
                  subTitle: "100",
                ),
              ),
            ],
          ),
          SizedBox(
            height: 12.h,
          ),
          Row(
            children: [
              Expanded(
                child: ScreenTitle(
                  title: 'Low-Stock',
                  titleSize: 14.sp,
                  titleFontWeight: FontWeight.w400,
                  subtitleFontWeight: FontWeight.w600,
                  subtitleColor: ColorPath.mirageBlack,
                  subTitle: "2",
                ),
              ),
              SizedBox(
                width: 8.w,
              ),
              Expanded(
                child: ScreenTitle(
                    title: 'Sold Out Inventory',
                    titleSize: 14.sp,
                    titleFontWeight: FontWeight.w400,
                    subtitleFontWeight: FontWeight.w600,
                    subtitleColor: ColorPath.alizarinRed,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    mainAxisAlignment: MainAxisAlignment.end,
                    subTitle: "20"),
              ),
            ],
          )
        ],
      ),
    );
  }
}
