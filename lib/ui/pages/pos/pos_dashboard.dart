import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/models/quick_action_item.dart';
import 'package:quick_retail_mobile/core/data/services/navigation_service.dart';
import 'package:quick_retail_mobile/core/data/view_models/auth_view_models/auth_view_model.dart';
import 'package:quick_retail_mobile/core/data/view_models/pos_dashboard_view_model.dart';
import 'package:quick_retail_mobile/core/data/view_models/sales_view_model.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/locator.dart';
import 'package:quick_retail_mobile/ui/pages/pos/core_features.dart';
import 'package:quick_retail_mobile/ui/pages/pos/customer_management/customer_management.dart';
import 'package:quick_retail_mobile/ui/pages/pos/happy_time/happy_time_dashboard.dart';
import 'package:quick_retail_mobile/ui/pages/pos/inventory/inventory_management.dart';
import 'package:quick_retail_mobile/ui/pages/pos/pos_analysis_dashboard.dart';
import 'package:quick_retail_mobile/ui/pages/pos/product_management/product_management.dart';
import 'package:quick_retail_mobile/ui/pages/pos/sales/sales_processing.dart';
import 'package:quick_retail_mobile/ui/pages/pos/sales_returns/sales_return_management.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/quick_action_bottomsheet.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/empty_state.dart';
import 'package:quick_retail_mobile/ui/widgets/error_state.dart';
import 'package:quick_retail_mobile/ui/widgets/in_app_display_image.dart';
import 'package:quick_retail_mobile/ui/widgets/loadable_content_builder.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/dashboard/sales_order_card.dart';
import 'package:quick_retail_mobile/ui/widgets/pos/overview_section.dart';
import 'package:quick_retail_mobile/ui/widgets/screen_title.dart';
import 'package:skeletonizer/skeletonizer.dart';

import '../../widgets/custom_icon.dart';

class PosDashboard extends ConsumerStatefulWidget {
  const PosDashboard({super.key});

  @override
  ConsumerState<PosDashboard> createState() => _PosDashboardState();
}

class _PosDashboardState extends ConsumerState<PosDashboard> {
  @override
  void initState() {
    super.initState();
    log("Init state");
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _fetchAnalysisOverview();
    });
  }

  _fetchAnalysisOverview() {
    ref.read(posDashboardViewModel)
      ..fetchAnalysisOverview()
      ..fetchCustomerStats()
      ..fetchSalesOverview();
    ref.read(salesViewModel).fetchAllSalesOrder();
  }

  final quickActions = [
    QuickActionsItem(
      actionName: "Dashboard",
      assetName: "posDashboard",
      onPressed: () {
        pushNavigation(
            context: locator<NavigationService>().navigationKey.currentContext!,
            widget: const PosAnalysisDashboard(),
            routeName: NamedRoutes.posDashboard);
      },
    ),
    QuickActionsItem(
      actionName: "Product",
      assetName: "posProduct",
      onPressed: () {
        pushNavigation(
            context: locator<NavigationService>().navigationKey.currentContext!,
            widget: const ProductMnagement(),
            routeName: NamedRoutes.posDashboard);
      },
    ),
    QuickActionsItem(
      actionName: "Sales",
      assetName: "posSales",
      onPressed: () {
        pushNavigation(
            context: locator<NavigationService>().navigationKey.currentContext!,
            widget: const SalesProcessing(),
            routeName: NamedRoutes.salesProcessing);
      },
    ),
    QuickActionsItem(
      actionName: "Inventory",
      assetName: "posInventory",
      onPressed: () {
        pushNavigation(
            context: locator<NavigationService>().navigationKey.currentContext!,
            widget: const InventoryManagement(),
            routeName: NamedRoutes.inventoryMgt);
      },
    ),
    QuickActionsItem(
      actionName: "Returns",
      assetName: "posReturns",
      onPressed: () {
        pushNavigation(
            context: locator<NavigationService>().navigationKey.currentContext!,
            widget: const SalesReturnManagement(),
            routeName: NamedRoutes.salesReturnMgt);
      },
    ),
    QuickActionsItem(
      actionName: "Customer",
      assetName: "posCustomer",
      onPressed: () {
        pushNavigation(
            context: locator<NavigationService>().navigationKey.currentContext!,
            widget: const CustomerManagement(),
            routeName: NamedRoutes.customerMgt);
      },
    ),
    QuickActionsItem(
      actionName: "Happy Time",
      assetName: "posHappyTime",
      onPressed: () {
        pushNavigation(
            context: locator<NavigationService>().navigationKey.currentContext!,
            widget: const HappyTimeDashboard(),
            routeName: NamedRoutes.happyTimeDashboard);
      },
    ),
    QuickActionsItem(
        actionName: "See more",
        assetName: "posSeeMore",
        onPressed: () {
          pushNavigation(
              context:
                  locator<NavigationService>().navigationKey.currentContext!,
              widget: const CoreFeatures(),
              routeName: NamedRoutes.coreFeature);
        }),
  ];
  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    final authVm = ref.watch(authViewModel);
    return Scaffold(
      appBar: customAppBar(
          context: context,
          showLeadingIcon: true,
          // leadingWidth: MediaQuery.of(context).size.width / 1.7,
          preferredHeight: 16,
          leadingIcon: Align(
            alignment: Alignment.centerLeft,
            child: Padding(
              padding: EdgeInsets.only(left: 16.w),
              child: Clickable(
                onPressed: () {},
                child: const InAppDisplayImage(),
              ),
            ),
          ),
          title: "Point of Sale",
          centerTitle: true,
          actions: [
            Padding(
              padding: EdgeInsets.only(right: 16.w),
              child: Row(
                children: [
                  CustomIcon(
                    imageAsset: Utilities.getSvg("add"),
                    onPressed: () {
                      bottomSheetWrapper(
                          context: context, child: QuickActionBottomsheet());
                    },
                  ),
                  SizedBox(
                    width: 8.w,
                  ),
                ],
              ),
            )
          ]),
      body: RefreshIndicator(
        onRefresh: () async {
          _fetchAnalysisOverview();
        },
        child: ListView(
          padding: EdgeInsets.only(top: 16.h, bottom: 32.h),
          children: [
            Padding(
              padding: EdgeInsets.only(
                left: 16.w,
                right: 16.w,
              ),
              child: const ScreenTitle(
                  title: "Analysis overview",
                  subTitle: "Key highlighting of point of sales."),
            ),
            SizedBox(
              height: 16.h,
            ),
            const OverViewSection(),
            SizedBox(
              height: 24.h,
            ),
            Padding(
              padding: EdgeInsets.only(
                left: 16.w,
                right: 16.w,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const ScreenTitle(
                      title: "More actions",
                      subTitle:
                          "Manage your business with key pos features below"),
                  SizedBox(
                    height: 16.h,
                  ),
                  GridView.builder(
                      shrinkWrap: true,
                      // controller: _scrollController,
                      physics: const NeverScrollableScrollPhysics(),
                      scrollDirection: Axis.vertical,
                      padding: EdgeInsets.symmetric(horizontal: 0.w),
                      itemCount: quickActions.length,
                      gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                        crossAxisCount: 4,
                        mainAxisSpacing: 16.h,
                        crossAxisSpacing: 16.w,
                        mainAxisExtent: 105.h,
                      ),
                      itemBuilder: (BuildContext context, int index) {
                        // final community = vm.communities[index];
                        return Clickable(
                          onPressed: quickActions[index].onPressed,
                          child: Column(
                            children: [
                              Container(
                                padding: EdgeInsets.all(20.w),
                                decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(10.r),
                                    color: ColorPath.flamingo.withOpacity(.2)),
                                child: SvgPicture.asset(Utilities.getSvg(
                                    quickActions[index].assetName)),
                              ),
                              SizedBox(
                                height: 8.h,
                              ),
                              Text(
                                quickActions[index].actionName,
                                style: textTheme.bodySmall?.copyWith(
                                    color: colorScheme.subTextSecondary),
                              )
                            ],
                          ),
                        );
                      }),
                ],
              ),
            ),
            SizedBox(
              height: 24.h,
            ),
            Padding(
              padding: EdgeInsets.only(
                left: 16.w,
                right: 16.w,
              ),
              child: Row(
                children: [
                  const Expanded(
                    child: ScreenTitle(
                        title: "Recent Order",
                        subTitle: "Manage your Order with ease"),
                  ),
                  Clickable(
                    onPressed: () {},
                    child: Row(
                      children: [
                        Text(
                          "View All",
                          style: textTheme.bodySmall,
                        ),
                        Icon(
                          Icons.north_east,
                          size: 14.w,
                          color: ColorPath.flamingo,
                        )
                      ],
                    ),
                  )
                ],
              ),
            ),
            SizedBox(
              height: 16.h,
            ),
            LoadableContentBuilder(
              isBusy: ref.watch(salesViewModel).state == ViewState.busy,
              isError: ref.watch(salesViewModel).state == ViewState.error,
              items: ref.watch(salesViewModel).salesOrderResponse,
              loadingBuilder: (context) {
                return ListView.separated(
                  padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h),
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (context, index) {
                    return const Skeletonizer(
                      enabled: true,
                      child: SalesOrderCard(
                        orderNumber: "#6784",
                        numberOfItem: "20",
                        totalAmount: "200789.90",
                        status: "Pending",
                      ),
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(
                      height: 16.h,
                    );
                  },
                  itemCount: 3,
                );
              },
              errorBuilder: (p0) {
                return SizedBox(
                  height: 175.h,
                  child: ErrorState(
                    message: ref.watch(salesViewModel).message,
                    onPressed: () =>
                        ref.read(salesViewModel).fetchAllSalesOrder(),
                  ),
                );
              },
              emptyBuilder: (context) {
                return Padding(
                    padding: const EdgeInsets.only(top: 16.0),
                    child: Padding(
                      padding: const EdgeInsets.only(top: 24.0),
                      child: EmptyState(
                          imageAsset: Utilities.getSvg("noData"),
                          showCTA: false,
                          title: "No Order",
                          subTitle: "You currently have not recent Order yet"),
                    ));
              },
              contentBuilder: (context) {
                return ListView.separated(
                  padding: EdgeInsets.only(left: 16.w, right: 16.w, top: 16.h),
                  physics: const NeverScrollableScrollPhysics(),
                  shrinkWrap: true,
                  itemBuilder: (ctx, i) {
                    final order = ref.watch(salesViewModel).recentSalesOrder[i];
                    return SalesOrderCard(
                      orderNumber: order.orderNumber ?? '',
                      numberOfItem:
                          order.saleOrderDetails?.length.toString() ?? '',
                      totalAmount: order.orderTotal ?? '',
                      status: order.status ?? '',
                    );
                  },
                  separatorBuilder: (context, index) {
                    return SizedBox(
                      height: 16.h,
                    );
                  },
                  itemCount: ref.watch(salesViewModel).recentSalesOrder.length,
                );
              },
            )
          ],
        ),
      ),
    );
  }
}
