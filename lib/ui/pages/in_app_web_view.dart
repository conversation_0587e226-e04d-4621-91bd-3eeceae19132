import 'package:flutter/material.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/ui/pages/onboarding/subscription/successful_subscription.dart';
import 'package:quick_retail_mobile/ui/widgets/app_loader.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:webview_flutter/webview_flutter.dart';
import 'package:webview_flutter_android/webview_flutter_android.dart';
import 'package:webview_flutter_wkwebview/webview_flutter_wkwebview.dart';

class InAppWebView extends StatefulWidget {
  final String url;
  final String title;
  const InAppWebView({super.key, required this.url, required this.title});

  @override
  State<InAppWebView> createState() =>
      _InAppWebViewState();
}

class _InAppWebViewState extends State<InAppWebView> {
  late final WebViewController _controller;
  bool _isLoading = true;




  @override
  void initState() {
    initControllerDynamics();
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: customAppBar(
          context: context,
          title: widget.title,
          centerTitle: true,
          // textColor: ColorPath.shaftBlack
        ),
       body: _isLoading ?
      const Center(
        child: AppLoader(),
      )
      : WebViewWidget(controller: _controller),
    );
  }

  initControllerDynamics() {
    // #docregion platform_features
    late final PlatformWebViewControllerCreationParams params;
    if (WebViewPlatform.instance is WebKitWebViewPlatform) {
      params = WebKitWebViewControllerCreationParams(
        allowsInlineMediaPlayback: true,
        mediaTypesRequiringUserAction: const <PlaybackMediaTypes>{},
      );
    } else {
      params = const PlatformWebViewControllerCreationParams();
    }

    final WebViewController controller =
    WebViewController.fromPlatformCreationParams(params);
    // #enddocregion platform_features

    controller
      ..setNavigationDelegate(
        NavigationDelegate(
          onProgress: (int progress) {},
          onPageStarted: (String url) {

          },
          onPageFinished: (String url) {
            setState(() {
              _isLoading = false;
            });
          },
          onWebResourceError: (WebResourceError error) {

          },
          onUrlChange: (change) {
            if(change.url != null){
              if(change.url!.contains('https://api-quick-retail.sbscuk.co.uk/public')){
                if(mounted){
                  // todo::: -> route user to payment success. verify payment reference
                  replaceNavigation(context: context, widget: const SuccessfulSubscription(), routeName: NamedRoutes.successfulSubscription);
                }

              }
            }
          },
        ),
      )
      ..setJavaScriptMode(JavaScriptMode.unrestricted)
      ..setBackgroundColor(Colors.white)
      ..loadRequest(
          Uri.parse(widget.url));

    // #docregion platform_features
    if (controller.platform is AndroidWebViewController) {
      AndroidWebViewController.enableDebugging(true);
      (controller.platform as AndroidWebViewController)
          .setMediaPlaybackRequiresUserGesture(false);
    }
    // #enddocregion platform_features

    _controller = controller;
  }
}
