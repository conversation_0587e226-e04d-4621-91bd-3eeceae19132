import 'package:flutter/material.dart';
import 'package:flutter/scheduler.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/data/view_models/bottom_nav_view_model.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_nav_items.dart';

class BottomNav extends ConsumerStatefulWidget {
  const BottomNav({super.key});

  @override
  ConsumerState<BottomNav> createState() => _BottomNavState();
}

class _BottomNavState extends ConsumerState<BottomNav> {
  @override
  void initState() {
    SchedulerBinding.instance.addPostFrameCallback((_) {
      //fetch profile(about me)
    });

    //init push notification listeners
    //FirebaseMessagingUtils.pushNotificationListenerInit(context: context);

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final bottomNavVm = ref.watch(bottomNavViewModel);
    //final accountSecurityVm = ref.watch(accountSecurityViewModel);
    return PopScope(
      canPop: false,
      onPopInvokedWithResult: (bool didPop, Object? result) async {
        if (didPop) {
          return;
        }
      },
      child: Scaffold(
        extendBodyBehindAppBar: true,
        bottomNavigationBar: Container(
          decoration: BoxDecoration(
            color: ColorPath.porceleanGrey,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withOpacity(0.1),
                spreadRadius: 2,
                blurRadius: 2,
                offset: const Offset(0, 0.75),
              ),
            ],
          ),
          child: Theme(
            data: Theme.of(context).copyWith(
              splashColor: Colors.transparent,
              highlightColor: Colors.transparent,
            ),
            child: Padding(
              padding: EdgeInsets.only(top: 0.h),
              child: BottomNavigationBar(
                  onTap: (index) => bottomNavVm.updateIndex(index),
                  type: BottomNavigationBarType.fixed,
                  unselectedFontSize: 14.sp,
                  selectedFontSize: 14.sp,
                  selectedItemColor: ColorPath.flamingo,
                  unselectedItemColor:
                      Theme.of(context).colorScheme.subTextPrimary,
                  selectedLabelStyle:
                      const TextStyle(fontWeight: FontWeight.w400),
                  unselectedLabelStyle:
                      const TextStyle(fontWeight: FontWeight.w400),
                  elevation: 0,
                  backgroundColor: ColorPath.porceleanGrey,
                  currentIndex: bottomNavVm.currentIndex,
                  items: bottomNavItems(context)),
            ),
          ),
        ),
        body: SafeArea(
            top: false,
            child: IndexedStack(
                index: bottomNavVm.currentIndex,
                children: bottomNavVm.children)),
      ),
    );
  }
}
