import 'dart:developer';

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/data/models/user.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/secure_storage/secure_storage_utils.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/ui/pages/authentication/otp_screen.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';

class ResetPassword extends ConsumerStatefulWidget {
  const ResetPassword({super.key});

  @override
  ConsumerState<ResetPassword> createState() => _ResetPasswordState();
}

class _ResetPasswordState extends ConsumerState<ResetPassword> {
  int selection = 0;
  User? user;

  @override
  void initState() {
    getCachedUser();
    super.initState();
  }

  getCachedUser() async {
    user = await SecureStorageUtils.retrieveUser();
    log(user?.toJson().toString() ?? '');
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;

    return Scaffold(
      appBar:
          customAppBar(context: context, title: 'Password', centerTitle: true),
      body: Padding(
        padding:
            EdgeInsets.only(left: 16.w, right: 16.w, top: 32.h, bottom: 48.h),
        child: Column(
          children: [
            Expanded(
              child: SingleChildScrollView(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          "Forgot Password?",
                          style: TextStyle(
                              fontSize: 24.sp, fontWeight: FontWeight.w700),
                        ),
                        SizedBox(
                          height: 2.h,
                        ),
                        Text(
                          "Select your preferred contact detail to reset your password.",
                          style: textTheme.bodySmall,
                        ),
                        SizedBox(
                          height: 32.h,
                        ),
                        ContactItem(
                          assetName: 'ring',
                          title: 'Phone Number',
                          isSelected: selection == 1,
                          onPressed: () {
                            setState(() {
                              selection = 1;
                            });
                          },
                          data: user?.phoneNumber ?? '',
                        ),
                        SizedBox(
                          height: 24.h,
                        ),
                        ContactItem(
                          assetName: 'envelop',
                          title: 'Email Address',
                          isSelected: selection == 2,
                          onPressed: () {
                            setState(() {
                              selection = 2;
                            });
                          },
                          data: user?.email ?? '',
                        ),
                        // Expanded(
                        //     child: SizedBox(
                        //   height: 200,
                        // )),
                      ],
                    ),
                  ],
                ),
              ),
            ),
            CustomButton(
              onPressed: () {
                pushNavigation(
                    context: context,
                    widget: OtpScreen(
                      otpMedium:
                          selection == 1 ? "phone number" : "email address",
                      email: user?.email,
                      phoneno: user?.phoneNumber,
                    ),
                    routeName: NamedRoutes.otpScreen);
              },
              buttonText: "Yes, Forget Password",
            )
          ],
        ),
      ),
    );
  }
}

class ContactItem extends StatelessWidget {
  const ContactItem({
    super.key,
    required this.assetName,
    required this.title,
    required this.data,
    this.isSelected = false,
    this.onPressed,
  });

  final String assetName;
  final String title;
  final String data;
  final bool isSelected;
  final void Function()? onPressed;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Clickable(
      onPressed: onPressed,
      child: Container(
        padding: EdgeInsets.only(top: 16.h, bottom: 16.h, left: 24.w),
        decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(10.r),
            border: Border.all(
                color: isSelected ? ColorPath.flamingo : ColorPath.athensGrey)),
        child: Row(
          children: [
            SvgPicture.asset(Utilities.getSvg(assetName)),
            SizedBox(
              width: 20.w,
            ),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: textTheme.bodySmall
                      ?.copyWith(color: colorScheme.subTextSecondary),
                ),
                SizedBox(
                  height: 2.h,
                ),
                Text(
                    data.isNotEmpty
                        ? Utilities.maskCharacters(
                            subject: data,
                            startIndex: 5,
                            endIndex: data.length - 3)
                        : "N/A",
                    style: TextStyle(
                        fontSize: 16.sp, fontWeight: FontWeight.w500)),
              ],
            )
          ],
        ),
      ),
    );
  }
}
