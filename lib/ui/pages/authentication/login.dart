import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/view_models/auth_view_models/auth_view_model.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/secure_storage/secure_storage_utils.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/core/utilities/validator.dart';
import 'package:quick_retail_mobile/ui/pages/authentication/reset_password.dart';
import 'package:quick_retail_mobile/ui/pages/bottom_nav.dart';
import 'package:quick_retail_mobile/ui/pages/onboarding/subscription/subscription_plans.dart';
import 'package:quick_retail_mobile/ui/widgets/busy_overlay.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';
import 'package:quick_retail_mobile/ui/widgets/show_flush_bar.dart';

class Login extends ConsumerStatefulWidget {
  const Login({super.key});

  @override
  ConsumerState<Login> createState() => _LoginState();
}

class _LoginState extends ConsumerState<Login> {
  TextEditingController emailController =
      TextEditingController(text: "<EMAIL>");
  TextEditingController passwordController =
      TextEditingController(text: "password");
  final _formKey = GlobalKey<FormState>();
  bool isPasswordVisible = true;
  String clientName = '';

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _getCachedUser();
    });
    super.initState();
  }

  _getCachedUser() async {
    final user = await SecureStorageUtils.retrieveUser();
    clientName = "${user?.firstname} ${user?.lastname}";
    setState(() {});
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    final vm = ref.watch(authViewModel);

    return BusyOverlay(
      show: vm.state == ViewState.busy,
      child: Scaffold(
        appBar:
            customAppBar(context: context, title: 'Login', centerTitle: true),
        body: SingleChildScrollView(
          padding:
              EdgeInsets.only(left: 16.w, right: 16.w, top: 32.h, bottom: 48.h),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  "$clientName 🏬 ",
                  style: TextStyle(
                      // fontFamily: clashDisplay,
                      fontSize: 24.sp,
                      fontWeight: FontWeight.w700),
                ),
                SizedBox(
                  height: 8.h,
                ),
                Text.rich(
                  TextSpan(text: 'Don’t have an Account? ', children: [
                    TextSpan(
                        text: 'Sign up.',
                        style: const TextStyle(
                          color: ColorPath.flamingo,
                          fontWeight: FontWeight.w500,
                        ),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            replaceNavigation(
                                context: context,
                                widget: const SubscriptionPlans(),
                                routeName: NamedRoutes.pricing);

                            // replaceNavigation(
                            //     context: context,
                            //     widget: SignUp(),
                            //     routeName: NamedRoutes.signup);
                          })
                  ]),
                  textAlign: TextAlign.center,
                  style:
                      TextStyle(fontSize: 14.sp, fontWeight: FontWeight.w400),
                ),
                SizedBox(
                  height: 32.h,
                ),
                CustomTextField(
                  controller: emailController,
                  validator: EmailValidator.validateEmail,
                  label: 'Email Address',
                  hintText: "Enter email address",
                  onChanged: (value) {
                    setState(() {});
                  },
                  isCompulsory: false,
                ),
                SizedBox(
                  height: 24.h,
                ),
                CustomTextField(
                  controller: passwordController,
                  validator: PasswordValidator.validatePassword,
                  label: 'Password',
                  hintText: "Enter Password",
                  onChanged: (value) {
                    setState(() {});
                  },
                  obscure: isPasswordVisible,
                  suffixIcon: Clickable(
                    onPressed: () {
                      setState(() {
                        isPasswordVisible = !isPasswordVisible;
                      });
                    },
                    child: Padding(
                      padding: const EdgeInsets.only(right: 16.0),
                      child: SvgPicture.asset(Utilities.getSvg('eye')),
                    ),
                  ),
                  isCompulsory: false,
                ),
                SizedBox(
                  height: 24.h,
                ),
                Clickable(
                    onPressed: () {
                      pushNavigation(
                          context: context,
                          widget: const ResetPassword(),
                          routeName: NamedRoutes.resetPassword);
                    },
                    child: Text(
                      "Forgot Password?",
                      style: textTheme.bodyMedium
                          ?.copyWith(color: ColorPath.paleGrey),
                    )),
                SizedBox(
                  height: 48.h,
                ),
                CustomButton(
                  onPressed: () async {
                    await vm.login(
                        email: emailController.text,
                        password: passwordController.text);

                    if (vm.state == ViewState.retrieved) {
                      pushNavigation(
                          context: context,
                          widget: const BottomNav(),
                          routeName: NamedRoutes.bottomNav);
                    } else if (vm.state == ViewState.error) {
                      showFlushBar(
                          context: context,
                          message: vm.message,
                          success: false);
                    }
                  },
                  bgColor:
                      _isActive(emailController.text, passwordController.text)
                          ? ColorPath.flamingo
                          : ColorPath.inactiveFlamingo,
                  buttonText: "Login",
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  _isActive(String email, String password) {
    return email.isNotEmpty && password.isNotEmpty;
  }
}
