import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:quick_retail_mobile/core/constants/app_theme/custom_color_scheme.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/view_models/auth_view_models/subscription_view_model.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/core/utilities/regex.dart';
import 'package:quick_retail_mobile/core/utilities/utilities.dart';
import 'package:quick_retail_mobile/core/utilities/validator.dart';
import 'package:quick_retail_mobile/ui/pages/onboarding/create_password.dart';
import 'package:quick_retail_mobile/ui/widgets/bottom_sheets/bottom_sheet_wrapper.dart';
import 'package:quick_retail_mobile/ui/widgets/busy_overlay.dart';
import 'package:quick_retail_mobile/ui/widgets/clickable.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_text_field.dart';
import 'package:quick_retail_mobile/ui/widgets/select_option.dart';
import 'package:quick_retail_mobile/ui/widgets/show_flush_bar.dart';
//TODO::: CONFIRM WHERE TOKEN IS GOTTEN FROM
class CreateNewPassword extends ConsumerStatefulWidget {
  const CreateNewPassword({super.key});

  @override
  ConsumerState<CreateNewPassword> createState() => _CreateNewPasswordState();
}

class _CreateNewPasswordState extends ConsumerState<CreateNewPassword> {
  TextEditingController newPasswordController = TextEditingController();
  TextEditingController confirmPasswordController = TextEditingController();
  TextEditingController securityQuestionController = TextEditingController();
  TextEditingController securityAnswerController = TextEditingController();
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (ref.read(subscriptionViewModel).securityQuestions.isEmpty) {
        ref.read(subscriptionViewModel).fetchSecurityQuestions();
      }
    });
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    final vm = ref.watch(subscriptionViewModel);

    return BusyOverlay(
      show: vm.state == ViewState.busy,
      child: Scaffold(
        appBar: customAppBar(
            context: context, title: 'Create New Password', centerTitle: true),
        body: SingleChildScrollView(
          padding:
              EdgeInsets.only(left: 16.w, right: 16.w, top: 32.h, bottom: 48.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "Create New Password",
                style: TextStyle(fontSize: 24.sp, fontWeight: FontWeight.w700),
              ),
              SizedBox(
                height: 2.h,
              ),
              const Text("Enter new password to secure your account"),
              SizedBox(
                height: 32.h,
              ),
              CustomTextField(
                label: 'New Password',
                hintText: "Enter Password",
                controller: newPasswordController,
                onChanged: (value) {
                  setState(() {});
                },
                suffixIcon: Padding(
                  padding: const EdgeInsets.only(right: 16.0),
                  child: SvgPicture.asset(Utilities.getSvg('eye')),
                ),
                // isCompulsory: false,
              ),
              SizedBox(
                height: 8.h,
              ),
              Column(
                children: [
                  PasswordCondition(
                      isMin: newPasswordController.text.length >= 8,
                      isValid: newPasswordController.text.length >= 8,
                      text:
                          "Password must contain at least Eight (8) characters. "),
                  PasswordCondition(
                      isMin: newPasswordController.text.length >= 8,
                      isValid: Regex.alphaNumeric
                          .hasMatch(newPasswordController.text),
                      text: "Password must be Alphanumeric "),
                  PasswordCondition(
                      isMin: newPasswordController.text.length >= 8,
                      isValid: Regex.withDigits
                              .hasMatch(newPasswordController.text) &&
                          Regex.withSpecialChar
                              .hasMatch(newPasswordController.text),
                      text:
                          "Password must contain number 0 - 9 and symbol *&%#@."),
                ],
              ),
              SizedBox(
                height: 16.h,
              ),
              CustomTextField(
                label: 'Confirm New Password',
                hintText: "Enter Password",
                controller: confirmPasswordController,
                suffixIcon: Padding(
                  padding: const EdgeInsets.only(right: 16.0),
                  child: SvgPicture.asset(Utilities.getSvg('eye')),
                ),
                // isCompulsory: false,
              ),
              SizedBox(
                height: 16.h,
              ),
              Clickable(
                onPressed: () {
                  bottomSheetWrapper(
                    context: context,
                    child: SelectOption(
                        title: "Security Question",
                        useConfirmationCTA: false,
                        subtitle: "Select security question",
                        options: vm.reducedSecurityQuestions(),
                        initialValue: vm.selectedSecurityQuestion?.question,
                        returningValue: (val) {
                          securityQuestionController.text = val;
                          vm.selectedSecurityQuestion = vm.securityQuestions
                              .firstWhere((element) => element.question == val);
                          setState(() {});
                        }),
                  );
                },
                child: CustomTextField(
                  label: 'Security Question',
                  enabled: false,
                  isCompulsory: false,
                  controller: securityQuestionController,
                  hintText: "Select Security Question",
                  suffixIcon: const Padding(
                    padding: EdgeInsets.only(right: 16.0),
                    child: Icon(Icons.keyboard_arrow_down_outlined),
                  ),
                ),
              ),
              const Text("Create an extra layer of security pass"),
              SizedBox(
                height: 32.h,
              ),
              CustomTextField(
                label: 'Security Questions Answer',
                controller: securityAnswerController,
                validator: FieldValidator.validate,
                isCompulsory: false,
                readOnly: true,
              ),
              SizedBox(
                height: 42.h,
              ),
              CustomButton(
                onPressed: () async {
                  // showFlushBar(context: context, message: "New OTP Code has been sent to your email");
                  // alertDialogWrapper(
                  //     context: context, child: PasswordResetSuccessfulDialog());

                  //todo::: Validate the form and security question selection

                  if (newPasswordController.text !=
                      confirmPasswordController.text) {
                    showFlushBar(
                        context: context,
                        message: "Please ensure both passwords match",
                        success: false);
                    return;
                  }

                 await  vm.createPassword(
                  password: newPasswordController.text,
                  confirmPassword: confirmPasswordController.text,
                  securityAnswer: securityAnswerController.text,
                 );

                  // pushNavigation(
                  //     context: context,
                  //     widget: const CreatePassword(),
                  //     routeName: NamedRoutes.createNewPassword);
                },
                bgColor: 1 + 1 == 3
                    ? ColorPath.inactiveFlamingo
                    : ColorPath.flamingo,
                buttonText: "Create New Password",
              )
            ],
          ),
        ),
      ),
    );
  }
}

class PasswordCondition extends StatelessWidget {
  const PasswordCondition(
      {super.key,
      required this.text,
      this.isValid = false,
      this.isMin = false});
  final String text;
  final bool isValid;
  final bool isMin;

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    return Row(
      children: [
        Icon(
          Icons.circle,
          size: 4.w,
        ),
        SizedBox(width: 8.w),
        Text(
          text,
          style: textTheme.bodySmall?.copyWith(
              color: isValid
                  ? ColorPath.salemGreen
                  : isMin
                      ? ColorPath.shirazRed
                      : colorScheme.subTextSecondary),
        ),
      ],
    );
  }
}
