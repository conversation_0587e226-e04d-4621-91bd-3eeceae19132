import 'dart:async';

import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:pinput/pinput.dart';
import 'package:quick_retail_mobile/core/constants/app_constants.dart';
import 'package:quick_retail_mobile/core/constants/color_path.dart';
import 'package:quick_retail_mobile/core/constants/named_routes.dart';
import 'package:quick_retail_mobile/core/data/enum/view_state.dart';
import 'package:quick_retail_mobile/core/data/view_models/auth_view_models/auth_view_model.dart';
import 'package:quick_retail_mobile/core/utilities/navigator.dart';
import 'package:quick_retail_mobile/ui/pages/authentication/create_new_password.dart';
import 'package:quick_retail_mobile/ui/widgets/busy_overlay.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_appbar.dart';
import 'package:quick_retail_mobile/ui/widgets/custom_button.dart';
import 'package:quick_retail_mobile/ui/widgets/show_flush_bar.dart';
//TODO::: no confirm OTP endpoint, Design to update flow
class OtpScreen extends ConsumerStatefulWidget {
  final String otpMedium;
  final String? email;
  final String? phoneno;
  const OtpScreen(
      {super.key, this.otpMedium = "email address", this.email, this.phoneno});

  @override
  ConsumerState<OtpScreen> createState() => _OtpScreenState();
}

class _OtpScreenState extends ConsumerState<OtpScreen> {
  int _secondsRemaining = 60;
  late Timer _timer;
  bool _canResend = false;

  @override
  void dispose() {
    _timer.cancel();
    super.dispose();
  }

  @override
  void initState() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      triggerForgetPassword();
      startResendTimer();
    });
    super.initState();
  }

  triggerForgetPassword() async {
    final vm = ref.read(authViewModel);

    if (widget.otpMedium == "email address") {
      await vm.forgotPassword(email: widget.email ?? '');
    }

    if (widget.otpMedium == "phone number") {
      await vm.forgotPassword(
          phoneno: widget.phoneno ?? '', otpMedium: widget.otpMedium);
    }

    if (vm.state == ViewState.error) {
      showFlushBar(
        context: context,
        message: vm.message,
        success: false,
      );
    } else if (vm.state == ViewState.retrieved) {
      showFlushBar(
        context: context,
        message: vm.message,
        success: true,
      );
    }
  }

  void startResendTimer() {
    _canResend = false;
    _timer = Timer.periodic(const Duration(seconds: 1), (timer) {
      if (_secondsRemaining == 0) {
        setState(() {
          _canResend = true;
        });
        _timer.cancel();
      } else {
        setState(() {
          _secondsRemaining--;
        });
      }
    });
  }

  void resendCode() async {
    await triggerForgetPassword();
    startResendTimer();
  }

  @override
  Widget build(BuildContext context) {
    final textTheme = Theme.of(context).textTheme;
    final colorScheme = Theme.of(context).colorScheme;
    final vm = ref.watch(authViewModel);

    return BusyOverlay(
      show: vm.state == ViewState.busy,
      child: Scaffold(
        appBar: customAppBar(
            context: context, title: 'Password', centerTitle: true),
        body: SingleChildScrollView(
          padding:
              EdgeInsets.only(left: 16.w, right: 16.w, top: 32.h, bottom: 48.h),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                "OTP code verification",
                style: TextStyle(fontSize: 24.sp, fontWeight: FontWeight.w700),
              ),
              SizedBox(
                height: 2.h,
              ),
              Text(
                  "A four digit code has been sent to your ${widget.otpMedium}"),
              SizedBox(
                height: 32.h,
              ),
              Pinput(
                length: 6,
                // controller: TextEditingController(),
                animationCurve: Curves.bounceInOut,
                animationDuration: const Duration(milliseconds: 300),
                defaultPinTheme: PinTheme(
                    width: 44,
                    height: 44,
                    margin: EdgeInsets.only(right: 12.w),
                    textStyle: TextStyle(
                        fontFamily: clashDisplay,
                        fontSize: 24.sp,
                        fontWeight: FontWeight.w700),
                    decoration: BoxDecoration(
                        color: ColorPath.athensGrey,
                        borderRadius: BorderRadius.circular(8.r),
                        border: Border.all(
                            color: ColorPath.mysticGrey, width: 2.w))),
              ),
              SizedBox(
                height: 24.h,
              ),
              Text.rich(
                TextSpan(text: _canResend ? "" : "Resend code in", children: [
                  if (_canResend)
                    TextSpan(
                        text: ' Resend Code',
                        style: textTheme.bodyMedium
                            ?.copyWith(color: ColorPath.flamingo),
                        recognizer: TapGestureRecognizer()
                          ..onTap = () {
                            resendCode();
                          })
                  else
                    TextSpan(
                        text: ' $_secondsRemaining s', //timer here
                        style: textTheme.bodyMedium
                            ?.copyWith(color: ColorPath.flamingo))
                ]),
                style: textTheme.bodyMedium,
              ),
              SizedBox(
                height: 42.h,
              ),
              CustomButton(
                onPressed: () {
                  // alertDialogWrapper(
                  //     context: context, child: PasswordResetSuccessfulDialog());

                  pushNavigation(
                      context: context,
                      widget: const CreateNewPassword(),
                      routeName: NamedRoutes.createNewPassword);
                },
                bgColor: 1 + 1 == 3
                    ? ColorPath.inactiveFlamingo
                    : ColorPath.flamingo,
                buttonText: "Verify",
              )
            ],
          ),
        ),
      ),
    );
  }
}
