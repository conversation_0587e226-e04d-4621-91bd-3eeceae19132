name: quick_retail_mobile
description: "A new Flutter project."
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.5.4

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.8
  flutter_riverpod: ^2.6.1
  flutter_screenutil: ^5.9.3
  flutter_svg: ^2.1.0
  intl: ^0.20.2
  dio: ^5.8.0+1
  flutter_dotenv: ^5.2.1
  flutter_secure_storage: ^9.2.4
  get_it: ^8.0.3
  page_transition: ^2.2.1
  another_flushbar: ^1.12.30
  shimmer: ^3.0.0
  package_info_plus: ^8.3.0
  url_launcher: ^6.3.1
  flutter_animate: ^4.5.2
  pinput: ^5.0.1
  cached_network_image: ^3.4.1
  lottie: ^3.2.0
  flutter_spinkit: ^5.2.1
  webview_flutter: ^4.10.0
  skeletonizer: ^1.4.3
  pretty_dio_logger: ^1.4.0
  image_picker: ^1.1.2
  image_cropper: ^9.1.0

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^4.0.0
  custom_lint: ^0.7.5
  riverpod_lint: ^2.6.5

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:
  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    # - assets/images/svg/
    - assets/images/png/
    - assets/images/jpg/
    - assets/images/
    - assets/lottie/
    # - assets/icons/png/
    - assets/icons/svg/
    - .env

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: ClashDisplay
      fonts:
        - asset: assets/fonts/ClashDisplay/ClashDisplay-Bold.otf
        - asset: assets/fonts/ClashDisplay/ClashDisplay-Extralight.otf
        - asset: assets/fonts/ClashDisplay/ClashDisplay-Light.otf
        - asset: assets/fonts/ClashDisplay/ClashDisplay-Medium.otf
        - asset: assets/fonts/ClashDisplay/ClashDisplay-Regular.otf
        - asset: assets/fonts/ClashDisplay/ClashDisplay-Semibold.otf
          # style: italic
    - family: DMSans
      fonts:
        - asset: assets/fonts/DMSans/DMSans-Black.ttf
        - asset: assets/fonts/DMSans/DMSans-BlackItalic.ttf
        - asset: assets/fonts/DMSans/DMSans-Bold.ttf
        - asset: assets/fonts/DMSans/DMSans-BoldItalic.ttf
        - asset: assets/fonts/DMSans/DMSans-ExtraBold.ttf
        - asset: assets/fonts/DMSans/DMSans-ExtraBoldItalic.ttf
        - asset: assets/fonts/DMSans/DMSans-ExtraLight.ttf
        - asset: assets/fonts/DMSans/DMSans-ExtraLightItalic.ttf
        - asset: assets/fonts/DMSans/DMSans-Medium.ttf
        - asset: assets/fonts/DMSans/DMSans-Italic.ttf
        - asset: assets/fonts/DMSans/DMSans-Light.ttf
        - asset: assets/fonts/DMSans/DMSans-Regular.ttf
        - asset: assets/fonts/DMSans/DMSans-SemiBold.ttf
        - asset: assets/fonts/DMSans/DMSans-Thin.ttf
        - asset: assets/fonts/DMSans/DMSans-SemiBoldItalic.ttf
        - asset: assets/fonts/DMSans/DMSans-ThinItalic.ttf

          weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
